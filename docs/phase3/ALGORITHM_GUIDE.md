# 算法使用指南

## 概述

本指南详细介绍第3阶段实现的各种优化算法的使用方法、参数设置和应用场景，帮助用户选择合适的算法解决具体问题。

## 算法分类

### 1. 单目标优化算法

#### 1.1 遗传算法 (Genetic Algorithm)

**适用场景**:
- 大规模组合优化问题
- 多约束优化问题
- 非线性、非凸优化问题
- 离散和连续混合优化问题

**算法特点**:
- 全局搜索能力强
- 对目标函数要求低（不需要连续、可导）
- 并行性好
- 参数设置相对简单

**参数设置指南**:

| 参数 | 推荐范围 | 默认值 | 说明 |
|------|----------|--------|------|
| population_size | 50-200 | 100 | 种群大小，问题复杂度高时增大 |
| crossover_rate | 0.6-0.95 | 0.8 | 交叉概率，一般设置较高 |
| mutation_rate | 0.01-0.3 | 0.1 | 变异概率，与问题维度成反比 |
| max_iterations | 100-5000 | 1000 | 最大迭代次数 |
| selection_method | - | tournament | 选择方法：tournament/roulette/rank |

**使用示例**:
```python
from src.algorithms.genetic.genetic_algorithm import GeneticAlgorithm
from src.algorithms.base import AlgorithmConfig

# 配置算法参数
config = AlgorithmConfig(
    max_iterations=1000,
    population_size=100,
    convergence_tolerance=1e-6,
    algorithm_params={
        "crossover_rate": 0.8,
        "mutation_rate": 0.1,
        "selection_method": "tournament",
        "encoding_type": "real"
    }
)

# 创建算法实例
ga = GeneticAlgorithm(config)

# 定义问题
problem_definition = {
    "dimension": 10,
    "bounds": [(-5, 5)] * 10
}

def objective_function(solution):
    variables = solution["variables"]
    return sum(x**2 for x in variables)  # 球面函数

# 执行优化
result = ga.optimize(problem_definition, objective_function, [])
print(f"最优解: {result.best_solution}")
print(f"最优值: {result.best_fitness}")
```

#### 1.2 粒子群优化算法 (Particle Swarm Optimization)

**适用场景**:
- 连续优化问题
- 参数调优问题
- 需要快速收敛的问题
- 中等规模优化问题

**算法特点**:
- 收敛速度快
- 实现简单
- 内存需求小
- 适合并行计算

**参数设置指南**:

| 参数 | 推荐范围 | 默认值 | 说明 |
|------|----------|--------|------|
| population_size | 20-100 | 50 | 粒子群大小 |
| inertia_weight | 0.4-0.9 | 0.9 | 惯性权重，影响探索能力 |
| cognitive_coefficient | 1.0-3.0 | 2.0 | 认知系数，个体学习能力 |
| social_coefficient | 1.0-3.0 | 2.0 | 社会系数，群体学习能力 |
| max_velocity | 0.1-2.0 | 1.0 | 最大速度限制 |

**使用示例**:
```python
from src.algorithms.particle_swarm.pso_algorithm import ParticleSwarmOptimization

config = AlgorithmConfig(
    max_iterations=500,
    population_size=50,
    algorithm_params={
        "inertia_weight": 0.9,
        "cognitive_coefficient": 2.0,
        "social_coefficient": 2.0,
        "max_velocity": 1.0
    }
)

pso = ParticleSwarmOptimization(config)
result = pso.optimize(problem_definition, objective_function, [])
```

#### 1.3 模拟退火算法 (Simulated Annealing)

**适用场景**:
- 组合优化问题
- 局部搜索改进
- 内存受限的环境
- 需要跳出局部最优的问题

**算法特点**:
- 能跳出局部最优
- 内存需求极小
- 理论基础扎实
- 参数设置较为关键

**参数设置指南**:

| 参数 | 推荐范围 | 默认值 | 说明 |
|------|----------|--------|------|
| initial_temperature | 100-10000 | 1000 | 初始温度，影响初期接受概率 |
| final_temperature | 0.001-1.0 | 0.01 | 终止温度 |
| cooling_rate | 0.8-0.99 | 0.95 | 冷却率，影响收敛速度 |
| cooling_schedule | - | exponential | 冷却策略：exponential/linear/logarithmic |
| markov_chain_length | 50-500 | 100 | 马尔可夫链长度 |

**使用示例**:
```python
from src.algorithms.simulated_annealing.sa_algorithm import SimulatedAnnealingAlgorithm

config = AlgorithmConfig(
    max_iterations=2000,
    population_size=1,  # SA是单点搜索
    algorithm_params={
        "initial_temperature": 1000.0,
        "final_temperature": 0.01,
        "cooling_rate": 0.95,
        "cooling_schedule": "exponential"
    }
)

sa = SimulatedAnnealingAlgorithm(config)
result = sa.optimize(problem_definition, objective_function, [])
```

### 2. 多目标优化算法

#### 2.1 NSGA-II算法

**适用场景**:
- 2-3个目标的多目标优化
- 需要帕累托前沿的问题
- 目标间存在冲突的问题
- 工程设计优化

**算法特点**:
- 维护多样性好
- 收敛性有保证
- 实现相对简单
- 广泛验证和应用

**参数设置指南**:

| 参数 | 推荐范围 | 默认值 | 说明 |
|------|----------|--------|------|
| population_size | 50-200 | 100 | 种群大小，目标数多时增大 |
| max_generations | 200-2000 | 1000 | 最大代数 |
| crossover_rate | 0.8-0.95 | 0.9 | 交叉概率 |
| mutation_rate | 0.05-0.2 | 0.1 | 变异概率 |
| tournament_size | 2-5 | 2 | 锦标赛选择大小 |

**使用示例**:
```python
from src.algorithms.multi_objective.nsga2 import NSGA2Algorithm

config = AlgorithmConfig(
    max_iterations=1000,
    population_size=100,
    algorithm_params={
        "crossover_rate": 0.9,
        "mutation_rate": 0.1,
        "num_objectives": 2
    }
)

nsga2 = NSGA2Algorithm(config)

# 多目标问题定义
problem_definition = {
    "gene_length": 10,
    "gene_bounds": [(-5, 5)] * 10,
    "num_objectives": 2,
    "objective_functions": [
        lambda sol: sum(x**2 for x in sol["genes"]),  # 目标1
        lambda sol: sum((x-1)**2 for x in sol["genes"])  # 目标2
    ]
}

nsga2.initialize_population(problem_definition)

# 运行优化
for generation in range(100):
    solutions = nsga2.generate_new_solutions()
    if not solutions:
        break

# 获取帕累托前沿
best_solution = nsga2._get_best_solution()
pareto_front = best_solution["pareto_front"]
```

### 3. 鲁棒优化算法

#### 3.1 最坏情况鲁棒优化

**适用场景**:
- 风险规避决策
- 安全关键系统
- 不确定性较大的问题
- 保守性决策需求

**算法特点**:
- 保守性强
- 理论保证
- 适用于风险规避
- 计算复杂度较高

**参数设置指南**:

| 参数 | 推荐范围 | 默认值 | 说明 |
|------|----------|--------|------|
| robustness_level | 0.01-0.5 | 0.1 | 鲁棒性水平 |
| uncertainty_budget | 0.1-2.0 | 1.0 | 不确定性预算 |
| max_inner_iterations | 50-500 | 100 | 内层优化最大迭代次数 |

**使用示例**:
```python
from src.services.robust_optimization import RobustOptimizationService

# 创建鲁棒优化任务
task_data = {
    "task_name": "鲁棒优化测试",
    "problem_definition": {
        "decision_dimension": 5,
        "decision_bounds": [(0, 1)] * 5,
        "uncertainty_set": {
            "parameter_names": ["demand", "cost"],
            "nominal_values": [100, 1.0],
            "uncertainty_bounds": [(-10, 10), (-0.2, 0.2)],
            "uncertainty_type": "box"
        }
    },
    "algorithm_config": {
        "robustness_level": 0.1,
        "uncertainty_budget": 1.0
    }
}

service = RobustOptimizationService()
# 通过服务接口执行
```

#### 3.2 随机规划

**适用场景**:
- 概率信息可用的问题
- 期望优化问题
- 风险管理决策
- 金融投资组合

**参数设置指南**:

| 参数 | 推荐范围 | 默认值 | 说明 |
|------|----------|--------|------|
| num_scenarios | 50-1000 | 100 | 场景数量 |
| confidence_level | 0.8-0.99 | 0.95 | 置信水平 |
| risk_measure | - | expected_value | 风险度量：expected_value/var/cvar |

## 算法选择指南

### 问题类型与算法匹配

| 问题类型 | 推荐算法 | 理由 |
|----------|----------|------|
| 连续优化 | PSO, GA | PSO收敛快，GA全局性好 |
| 离散优化 | GA, SA | GA适合大规模，SA适合小规模 |
| 组合优化 | GA, SA | 编码灵活，搜索能力强 |
| 多目标优化 | NSGA-II | 专门设计，效果好 |
| 不确定性优化 | 鲁棒优化 | 考虑不确定性，决策可靠 |
| 约束优化 | GA + 罚函数 | 处理约束灵活 |

### 问题规模与算法选择

| 问题规模 | 变量数量 | 推荐算法 | 参数建议 |
|----------|----------|----------|----------|
| 小规模 | < 20 | SA, PSO | 小种群，多迭代 |
| 中等规模 | 20-100 | GA, PSO | 中等种群，适中迭代 |
| 大规模 | > 100 | GA | 大种群，并行计算 |

### 性能要求与算法选择

| 性能要求 | 推荐算法 | 说明 |
|----------|----------|------|
| 快速收敛 | PSO | 收敛速度最快 |
| 高精度解 | GA, SA | 全局搜索能力强 |
| 内存受限 | SA | 内存需求最小 |
| 并行计算 | GA, PSO | 天然并行性 |

## 参数调优策略

### 1. 遗传算法参数调优

**种群大小调优**:
- 小问题：30-50
- 中等问题：50-100
- 大问题：100-200
- 经验公式：population_size = 10 * sqrt(dimension)

**交叉率调优**:
- 初期：0.8-0.9（高探索）
- 后期：0.6-0.7（精细搜索）
- 自适应：根据种群多样性调整

**变异率调优**:
- 高维问题：1/dimension
- 低维问题：0.05-0.1
- 自适应：根据收敛情况调整

### 2. 粒子群算法参数调优

**惯性权重调优**:
- 线性递减：从0.9递减到0.4
- 自适应：根据群体性能调整
- 随机：在[0.5, 1.0]范围内随机

**学习因子调优**:
- 平衡设置：c1 = c2 = 2.0
- 早期探索：c1 > c2
- 后期开发：c1 < c2

### 3. 模拟退火参数调优

**初始温度设置**:
- 经验公式：T0 = -Δf_avg / ln(P0)
- 其中P0为初始接受概率（0.8-0.9）
- Δf_avg为目标函数变化的平均值

**冷却率设置**:
- 快速冷却：0.8-0.9
- 慢速冷却：0.95-0.99
- 自适应：根据接受率调整

## 性能优化技巧

### 1. 算法级优化

**早停策略**:
```python
# 连续N代无改进则停止
no_improvement_count = 0
best_fitness_history = []

for generation in range(max_generations):
    current_best = algorithm.get_best_fitness()
    
    if len(best_fitness_history) > 0:
        if abs(current_best - best_fitness_history[-1]) < tolerance:
            no_improvement_count += 1
        else:
            no_improvement_count = 0
    
    if no_improvement_count >= patience:
        break
    
    best_fitness_history.append(current_best)
```

**自适应参数**:
```python
# 根据种群多样性调整变异率
diversity = calculate_population_diversity(population)
if diversity < threshold:
    mutation_rate *= 1.1  # 增加变异率
else:
    mutation_rate *= 0.9  # 减少变异率
```

### 2. 并行计算优化

**种群并行**:
```python
from concurrent.futures import ProcessPoolExecutor

def evaluate_individual(individual):
    return objective_function(individual)

# 并行评估种群
with ProcessPoolExecutor() as executor:
    fitness_values = list(executor.map(evaluate_individual, population))
```

**岛屿模型**:
```python
# 多个子种群独立进化，定期交换个体
islands = [create_subpopulation() for _ in range(num_islands)]

for generation in range(max_generations):
    # 各岛屿独立进化
    for island in islands:
        island.evolve()
    
    # 定期迁移
    if generation % migration_interval == 0:
        migrate_individuals(islands)
```

### 3. 内存优化

**流式处理**:
```python
# 避免存储所有历史数据
class StreamingOptimizer:
    def __init__(self):
        self.best_solution = None
        self.best_fitness = float('inf')
    
    def update(self, solution, fitness):
        if fitness < self.best_fitness:
            self.best_fitness = fitness
            self.best_solution = solution.copy()
```

## 常见问题和解决方案

### 1. 收敛问题

**问题**: 算法不收敛或收敛到局部最优

**解决方案**:
- 增加种群大小
- 调整选择压力
- 使用多重启动
- 混合多种算法

### 2. 性能问题

**问题**: 算法运行时间过长

**解决方案**:
- 使用并行计算
- 优化目标函数计算
- 减少种群大小
- 设置时间限制

### 3. 参数设置问题

**问题**: 不知道如何设置参数

**解决方案**:
- 使用默认参数开始
- 进行参数敏感性分析
- 使用自适应参数
- 参考相关文献

## 算法评估指标

### 1. 收敛性指标

- **收敛代数**: 达到满意解的迭代次数
- **收敛率**: 成功收敛的运行次数比例
- **收敛精度**: 最终解与最优解的差距

### 2. 多样性指标

- **种群多样性**: 种群中个体的差异程度
- **基因多样性**: 基因位上的多样性
- **适应度多样性**: 适应度值的分布

### 3. 鲁棒性指标

- **成功率**: 多次运行的成功比例
- **稳定性**: 多次运行结果的方差
- **可重复性**: 相同条件下结果的一致性

## 总结

选择合适的算法需要综合考虑问题特性、性能要求和计算资源。建议：

1. **从简单算法开始**: 先尝试PSO或标准GA
2. **逐步优化**: 根据结果调整参数和算法
3. **多算法比较**: 对比不同算法的性能
4. **实际测试**: 在真实问题上验证效果

通过合理的算法选择和参数设置，可以有效解决各种优化问题，获得满意的结果。
