# API使用指南

## 概述

本文档详细介绍第3阶段新增的API接口使用方法，包括高级资源调度、鲁棒优化和多目标优化等功能的API调用示例。

## 基础信息

- **基础URL**: `http://localhost:8000/api/v1`
- **认证方式**: <PERSON><PERSON>（如果启用）
- **数据格式**: JSON
- **字符编码**: UTF-8

## 高级资源调度API

### 1. 创建调度任务

**端点**: `POST /advanced-scheduling/tasks`

**请求示例**:
```json
{
  "task_name": "生产线调度优化",
  "task_description": "使用遗传算法优化生产线资源调度",
  "problem_definition": {
    "resources": [
      {
        "id": "machine_1",
        "name": "机器1",
        "capacity": 1,
        "efficiency": 1.0,
        "cost_per_hour": 10.0
      },
      {
        "id": "machine_2", 
        "name": "机器2",
        "capacity": 1,
        "efficiency": 1.2,
        "cost_per_hour": 12.0
      }
    ],
    "tasks": [
      {
        "id": "job_1",
        "name": "作业1",
        "duration": 5.0,
        "priority": 1
      },
      {
        "id": "job_2",
        "name": "作业2", 
        "duration": 3.0,
        "priority": 2
      }
    ],
    "constraints": [
      {
        "type": "resource_capacity",
        "description": "每台机器同时只能处理一个作业"
      }
    ],
    "optimization_objectives": ["minimize_makespan", "minimize_total_cost"]
  },
  "algorithm_config": {
    "population_size": 100,
    "max_iterations": 1000,
    "crossover_rate": 0.8,
    "mutation_rate": 0.1,
    "selection_method": "tournament"
  }
}
```

**响应示例**:
```json
{
  "task_id": "task_12345",
  "message": "高级调度任务创建成功",
  "status": "created"
}
```

### 2. 执行调度优化

**端点**: `POST /advanced-scheduling/tasks/{task_id}/execute`

**请求示例**:
```json
{
  "task_id": "task_12345",
  "force_restart": false
}
```

**响应示例**:
```json
{
  "task_id": "task_12345",
  "execution_status": "started",
  "message": "高级调度优化任务已开始执行",
  "estimated_completion_time": null
}
```

### 3. 获取可用算法列表

**端点**: `GET /advanced-scheduling/algorithms`

**响应示例**:
```json
[
  {
    "algorithm_type": "genetic_algorithm",
    "algorithm_name": "遗传算法",
    "description": "基于自然选择和遗传机制的进化算法",
    "suitable_for": ["大规模调度", "多约束优化", "非线性问题"],
    "parameters": {
      "population_size": {
        "type": "int",
        "default": 50,
        "range": [20, 200]
      },
      "crossover_rate": {
        "type": "float", 
        "default": 0.8,
        "range": [0.5, 1.0]
      }
    },
    "performance": {
      "convergence_speed": "medium",
      "solution_quality": "high",
      "computational_complexity": "medium"
    }
  }
]
```

### 4. 验证问题定义

**端点**: `POST /advanced-scheduling/validate-problem`

**请求示例**:
```json
{
  "resources": [
    {"id": "r1", "capacity": 1}
  ],
  "tasks": [
    {"id": "t1", "duration": 5.0}
  ],
  "constraints": []
}
```

**响应示例**:
```json
{
  "is_valid": true,
  "errors": [],
  "warnings": [],
  "suggestions": [
    "问题定义格式正确，建议进行小规模测试"
  ]
}
```

## 鲁棒优化API

### 1. 创建鲁棒优化任务

**端点**: `POST /robust-optimization/tasks`

**请求示例**:
```json
{
  "task_name": "供应链鲁棒优化",
  "task_description": "考虑需求不确定性的供应链优化",
  "problem_definition": {
    "decision_dimension": 5,
    "decision_bounds": [
      [0, 100],
      [0, 100], 
      [0, 100],
      [0, 100],
      [0, 100]
    ],
    "uncertainty_set": {
      "parameter_names": ["demand", "cost", "capacity"],
      "nominal_values": [100.0, 1.0, 50.0],
      "uncertainty_bounds": [[-10.0, 10.0], [-0.2, 0.2], [-5.0, 5.0]],
      "uncertainty_type": "box"
    }
  },
  "algorithm_config": {
    "robustness_level": 0.1,
    "max_inner_iterations": 100,
    "uncertainty_budget": 1.0
  }
}
```

**响应示例**:
```json
{
  "task_id": "robust_task_67890",
  "message": "鲁棒优化任务创建成功",
  "status": "created"
}
```

### 2. 执行鲁棒优化

**端点**: `POST /robust-optimization/tasks/{task_id}/execute`

**请求示例**:
```json
{
  "task_id": "robust_task_67890",
  "optimization_method": "worst_case",
  "force_restart": false
}
```

### 3. 获取不确定性分析

**端点**: `GET /robust-optimization/tasks/{task_id}/uncertainty-analysis`

**响应示例**:
```json
{
  "task_id": "robust_task_67890",
  "optimization_method": "worst_case",
  "robustness_measure": 0.85,
  "sensitivity_analysis": {
    "demand": {
      "sensitivity": 0.3,
      "impact_range": [-5.2, 8.7]
    },
    "cost": {
      "sensitivity": 0.6,
      "impact_range": [-12.1, 15.3]
    }
  },
  "uncertainty_impact": {
    "worst_case_loss": 25.6,
    "expected_loss": 12.3,
    "confidence_level": 0.95
  },
  "confidence_intervals": {
    "objective_value": [45.2, 67.8],
    "decision_variables": [
      [8.5, 12.3],
      [15.7, 23.1]
    ]
  }
}
```

### 4. 生成随机场景

**端点**: `POST /robust-optimization/generate-scenarios`

**请求示例**:
```json
{
  "num_scenarios": 100,
  "method": "monte_carlo",
  "parameters": {
    "demand": {
      "distribution": "normal",
      "mean": 100,
      "std": 20
    },
    "cost": {
      "distribution": "uniform", 
      "low": 0.8,
      "high": 1.2
    }
  }
}
```

**响应示例**:
```json
{
  "scenarios": [
    {
      "scenario_id": "scenario_0",
      "parameters": {
        "demand": 95.6,
        "cost": 1.05
      }
    }
  ],
  "num_scenarios": 100,
  "generation_method": "monte_carlo",
  "statistics": {
    "demand": {
      "mean": 99.8,
      "std": 19.7,
      "min": 52.3,
      "max": 147.2
    },
    "cost": {
      "mean": 1.0,
      "std": 0.115,
      "min": 0.801,
      "max": 1.198
    }
  }
}
```

## 多目标优化API

### 1. 创建多目标优化任务

**端点**: `POST /multi-objective/tasks`

**请求示例**:
```json
{
  "task_name": "产品设计多目标优化",
  "task_description": "同时优化成本和质量的产品设计",
  "objective_functions": [
    {
      "function_id": "cost",
      "function_name": "成本函数",
      "expression": "sum(x**2 for x in genes)",
      "type": "minimize",
      "weight": 1.0,
      "priority": 1
    },
    {
      "function_id": "quality",
      "function_name": "质量函数", 
      "expression": "sum(abs(x) for x in genes)",
      "type": "maximize",
      "weight": 1.0,
      "priority": 1
    }
  ],
  "decision_variables": ["x1", "x2", "x3", "x4", "x5"],
  "variable_bounds": {
    "x1": {"lower": -5, "upper": 5},
    "x2": {"lower": -5, "upper": 5},
    "x3": {"lower": -5, "upper": 5},
    "x4": {"lower": -5, "upper": 5},
    "x5": {"lower": -5, "upper": 5}
  },
  "algorithm_type": "nsga2",
  "algorithm_parameters": {
    "crossover_rate": 0.9,
    "mutation_rate": 0.1,
    "tournament_size": 2
  },
  "population_size": 100,
  "max_generations": 1000
}
```

**响应示例**:
```json
{
  "task_id": "mo_task_11111",
  "message": "多目标优化任务创建成功",
  "status": "created"
}
```

### 2. 分析帕累托前沿

**端点**: `POST /multi-objective/analyze-pareto-front`

**请求示例**:
```json
{
  "solutions": [
    {
      "objectives": [1.0, 2.0],
      "decision_variables": [0.5, 0.3, 0.8, 0.2, 0.6]
    },
    {
      "objectives": [1.5, 1.5],
      "decision_variables": [0.7, 0.4, 0.6, 0.3, 0.5]
    },
    {
      "objectives": [2.0, 1.0],
      "decision_variables": [0.9, 0.2, 0.4, 0.5, 0.3]
    }
  ]
}
```

**响应示例**:
```json
{
  "pareto_front_analysis": {
    "basic_statistics": {
      "objective_0": {
        "min": 1.0,
        "max": 2.0,
        "mean": 1.5,
        "std": 0.5,
        "range": 1.0
      },
      "objective_1": {
        "min": 1.0,
        "max": 2.0,
        "mean": 1.5,
        "std": 0.5,
        "range": 1.0
      }
    },
    "quality_indicators": {
      "hypervolume": 2.25,
      "spacing": 0.35,
      "spread": 1.0
    },
    "front_characteristics": {
      "num_solutions": 3,
      "num_objectives": 2,
      "coverage": {
        "objective_ranges": [1.0, 1.0],
        "total_coverage": 1.0
      }
    },
    "recommendations": [
      "前沿解数量适中",
      "解分布较为均匀",
      "前沿覆盖范围良好"
    ]
  }
}
```

## 错误处理

### 常见错误码

- **400 Bad Request**: 请求参数错误
- **404 Not Found**: 资源不存在
- **422 Unprocessable Entity**: 数据验证失败
- **500 Internal Server Error**: 服务器内部错误

### 错误响应格式

```json
{
  "detail": "错误描述信息",
  "error_code": "ERROR_CODE",
  "timestamp": "2024-12-19T10:30:00Z"
}
```

## 使用最佳实践

### 1. 任务创建

- 合理设置算法参数，避免过大的种群大小和迭代次数
- 验证问题定义的正确性
- 为任务设置有意义的名称和描述

### 2. 性能优化

- 对于大规模问题，考虑使用并行计算
- 设置合理的时间限制
- 监控内存使用情况

### 3. 结果分析

- 多次运行算法以获得统计结果
- 比较不同算法的性能
- 分析收敛性和解的质量

## 代码示例

### Python客户端示例

```python
import requests
import json

class OptimizationClient:
    def __init__(self, base_url="http://localhost:8000/api/v1"):
        self.base_url = base_url
        self.session = requests.Session()
    
    def create_scheduling_task(self, task_data):
        """创建调度任务"""
        url = f"{self.base_url}/advanced-scheduling/tasks"
        response = self.session.post(url, json=task_data)
        response.raise_for_status()
        return response.json()
    
    def execute_task(self, task_id):
        """执行任务"""
        url = f"{self.base_url}/advanced-scheduling/tasks/{task_id}/execute"
        execution_data = {"task_id": task_id, "force_restart": False}
        response = self.session.post(url, json=execution_data)
        response.raise_for_status()
        return response.json()
    
    def get_algorithms(self):
        """获取算法列表"""
        url = f"{self.base_url}/advanced-scheduling/algorithms"
        response = self.session.get(url)
        response.raise_for_status()
        return response.json()

# 使用示例
client = OptimizationClient()

# 创建任务
task_data = {
    "task_name": "测试任务",
    "problem_definition": {...},
    "algorithm_config": {...}
}

result = client.create_scheduling_task(task_data)
task_id = result["task_id"]

# 执行任务
execution_result = client.execute_task(task_id)
print(f"任务执行状态: {execution_result['execution_status']}")
```

### JavaScript客户端示例

```javascript
class OptimizationClient {
    constructor(baseUrl = 'http://localhost:8000/api/v1') {
        this.baseUrl = baseUrl;
    }
    
    async createSchedulingTask(taskData) {
        const response = await fetch(`${this.baseUrl}/advanced-scheduling/tasks`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(taskData)
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        return await response.json();
    }
    
    async executeTask(taskId) {
        const response = await fetch(`${this.baseUrl}/advanced-scheduling/tasks/${taskId}/execute`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                task_id: taskId,
                force_restart: false
            })
        });
        
        return await response.json();
    }
}

// 使用示例
const client = new OptimizationClient();

const taskData = {
    task_name: "测试任务",
    problem_definition: {...},
    algorithm_config: {...}
};

client.createSchedulingTask(taskData)
    .then(result => {
        console.log('任务创建成功:', result.task_id);
        return client.executeTask(result.task_id);
    })
    .then(executionResult => {
        console.log('任务执行状态:', executionResult.execution_status);
    })
    .catch(error => {
        console.error('错误:', error);
    });
```

## 限制和注意事项

1. **请求频率限制**: 每分钟最多100个请求
2. **任务并发限制**: 同时执行的任务数量有限制
3. **数据大小限制**: 单个请求最大10MB
4. **超时设置**: 长时间运行的任务会自动超时

## 版本兼容性

- API版本: v1
- 向后兼容性: 保证向后兼容
- 废弃通知: 废弃的接口会提前通知

## 支持和反馈

如有问题或建议，请通过以下方式联系：
- 技术支持: <EMAIL>
- 文档反馈: <EMAIL>
- GitHub Issues: https://github.com/example/issues
