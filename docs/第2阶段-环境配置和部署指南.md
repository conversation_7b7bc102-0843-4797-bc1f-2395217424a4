# 第2阶段 - 环境配置和部署指南

## 部署概述

本指南详细说明了第2阶段装卸载作业效能计算引擎的环境配置和部署流程。系统基于Python FastAPI框架，使用PostgreSQL数据库，支持Docker容器化部署和传统部署方式。

### 系统要求
- **操作系统**: Linux (Ubuntu 20.04+/CentOS 8+) 或 macOS 10.15+
- **Python版本**: Python 3.9+
- **数据库**: PostgreSQL 13+
- **内存**: 最小4GB，推荐8GB+
- **磁盘空间**: 最小10GB，推荐50GB+
- **网络**: 支持HTTP/HTTPS访问

## 环境配置

### 1. Python环境配置

#### 1.1 安装Python和虚拟环境
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install python3.9 python3.9-venv python3.9-dev python3-pip

# CentOS/RHEL
sudo yum install python39 python39-devel python39-pip

# macOS (使用Homebrew)
brew install python@3.9

# 创建虚拟环境
python3.9 -m venv xiaoneng_env
source xiaoneng_env/bin/activate  # Linux/macOS
# xiaoneng_env\Scripts\activate  # Windows
```

#### 1.2 安装项目依赖
```bash
# 进入项目目录
cd xiaoneng

# 升级pip
pip install --upgrade pip

# 安装项目依赖
pip install -r requirements.txt

# 安装开发依赖（可选）
pip install -r requirements-dev.txt
```

### 2. 数据库配置

#### 2.1 PostgreSQL安装和配置
```bash
# Ubuntu/Debian
sudo apt install postgresql postgresql-contrib

# CentOS/RHEL
sudo yum install postgresql-server postgresql-contrib
sudo postgresql-setup initdb

# 启动PostgreSQL服务
sudo systemctl start postgresql
sudo systemctl enable postgresql

# 创建数据库和用户
sudo -u postgres psql
```

```sql
-- 在PostgreSQL命令行中执行
CREATE DATABASE xiaoneng;
CREATE USER xiaoneng_user WITH PASSWORD 'your_secure_password';
GRANT ALL PRIVILEGES ON DATABASE xiaoneng TO xiaoneng_user;
ALTER USER xiaoneng_user CREATEDB;  -- 允许创建测试数据库
\q
```

#### 2.2 数据库连接配置
```bash
# 创建环境变量文件
cp .env.example .env

# 编辑环境变量文件
nano .env
```

```bash
# .env 文件内容
# 数据库配置
DATABASE_URL=postgresql+asyncpg://xiaoneng_user:your_secure_password@localhost:5432/xiaoneng
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=30
DATABASE_POOL_TIMEOUT=30

# 应用配置
APP_NAME=小能效能计算引擎
APP_VERSION=2.0.0
DEBUG=false
LOG_LEVEL=INFO

# API配置
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=4

# 安全配置
SECRET_KEY=your_secret_key_here
ALLOWED_HOSTS=localhost,127.0.0.1,your-domain.com

# Redis配置（如果使用缓存）
REDIS_URL=redis://localhost:6379/0

# 日志配置
LOG_FILE_PATH=/var/log/xiaoneng/app.log
LOG_MAX_SIZE=100MB
LOG_BACKUP_COUNT=5
```

### 3. 数据库初始化

#### 3.1 执行数据库迁移
```bash
# 创建数据库表结构
python scripts/init_db.py

# 生成基础测试数据
python scripts/generate_efficiency_test_data.py

# 生成装卸载作业测试数据
python scripts/generate_loading_efficiency_test_data.py

# 验证数据库初始化
python scripts/validate_efficiency_functionality.py
python scripts/validate_loading_efficiency_functionality.py
```

#### 3.2 数据库迁移脚本
```bash
# 创建迁移脚本目录
mkdir -p migrations

# 执行数据库迁移（如果有迁移文件）
python scripts/run_migrations.py
```

## 部署方式

### 方式一：Docker容器化部署（推荐）

#### 1.1 创建Dockerfile
```dockerfile
# Dockerfile
FROM python:3.9-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建非root用户
RUN useradd -m -u 1000 xiaoneng && chown -R xiaoneng:xiaoneng /app
USER xiaoneng

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["uvicorn", "src.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

#### 1.2 创建docker-compose.yml
```yaml
# docker-compose.yml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=postgresql+asyncpg://xiaoneng_user:xiaoneng_password@db:5432/xiaoneng
      - DEBUG=false
      - LOG_LEVEL=INFO
    depends_on:
      - db
      - redis
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped

  db:
    image: postgres:13
    environment:
      - POSTGRES_DB=xiaoneng
      - POSTGRES_USER=xiaoneng_user
      - POSTGRES_PASSWORD=xiaoneng_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    restart: unless-stopped

  redis:
    image: redis:6-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
```

#### 1.3 Docker部署命令
```bash
# 构建和启动服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f app

# 执行数据库初始化
docker-compose exec app python scripts/init_db.py

# 生成测试数据
docker-compose exec app python scripts/generate_loading_efficiency_test_data.py

# 停止服务
docker-compose down

# 完全清理（包括数据卷）
docker-compose down -v
```

### 方式二：传统部署

#### 2.1 使用Gunicorn部署
```bash
# 安装Gunicorn
pip install gunicorn[gthread]

# 创建Gunicorn配置文件
cat > gunicorn.conf.py << EOF
# Gunicorn配置
bind = "0.0.0.0:8000"
workers = 4
worker_class = "uvicorn.workers.UvicornWorker"
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 100
timeout = 30
keepalive = 2
preload_app = True
user = "xiaoneng"
group = "xiaoneng"
tmp_upload_dir = None
logconfig_dict = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'default': {
            'format': '%(asctime)s [%(process)d] [%(levelname)s] %(message)s',
            'datefmt': '%Y-%m-%d %H:%M:%S %z',
        },
    },
    'handlers': {
        'default': {
            'formatter': 'default',
            'class': 'logging.StreamHandler',
            'stream': 'ext://sys.stdout',
        },
    },
    'root': {
        'level': 'INFO',
        'handlers': ['default'],
    },
    'loggers': {
        'gunicorn.error': {
            'level': 'INFO',
            'handlers': ['default'],
            'propagate': False,
        },
        'gunicorn.access': {
            'level': 'INFO',
            'handlers': ['default'],
            'propagate': False,
        },
    },
}
EOF

# 启动应用
gunicorn -c gunicorn.conf.py src.main:app
```

#### 2.2 使用Systemd服务
```bash
# 创建系统用户
sudo useradd -r -s /bin/false xiaoneng

# 创建systemd服务文件
sudo tee /etc/systemd/system/xiaoneng.service << EOF
[Unit]
Description=小能效能计算引擎
After=network.target postgresql.service

[Service]
Type=exec
User=xiaoneng
Group=xiaoneng
WorkingDirectory=/opt/xiaoneng
Environment=PATH=/opt/xiaoneng/xiaoneng_env/bin
ExecStart=/opt/xiaoneng/xiaoneng_env/bin/gunicorn -c gunicorn.conf.py src.main:app
ExecReload=/bin/kill -s HUP \$MAINPID
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
EOF

# 重新加载systemd配置
sudo systemctl daemon-reload

# 启动服务
sudo systemctl start xiaoneng
sudo systemctl enable xiaoneng

# 查看服务状态
sudo systemctl status xiaoneng

# 查看日志
sudo journalctl -u xiaoneng -f
```

### 方式三：Nginx反向代理配置

#### 3.1 Nginx配置文件
```nginx
# /etc/nginx/sites-available/xiaoneng
upstream xiaoneng_backend {
    server 127.0.0.1:8000;
    # 如果有多个实例，可以添加更多server
    # server 127.0.0.1:8001;
    # server 127.0.0.1:8002;
}

server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    
    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com www.your-domain.com;
    
    # SSL配置
    ssl_certificate /etc/nginx/ssl/your-domain.com.crt;
    ssl_certificate_key /etc/nginx/ssl/your-domain.com.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # 日志配置
    access_log /var/log/nginx/xiaoneng_access.log;
    error_log /var/log/nginx/xiaoneng_error.log;
    
    # 客户端上传限制
    client_max_body_size 10M;
    
    # 代理配置
    location / {
        proxy_pass http://xiaoneng_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时配置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        
        # WebSocket支持（如果需要）
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
    
    # 静态文件服务（如果有）
    location /static/ {
        alias /opt/xiaoneng/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # API文档路径
    location /docs {
        proxy_pass http://xiaoneng_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 健康检查
    location /health {
        proxy_pass http://xiaoneng_backend;
        access_log off;
    }
}
```

#### 3.2 启用Nginx配置
```bash
# 创建软链接
sudo ln -s /etc/nginx/sites-available/xiaoneng /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重新加载配置
sudo systemctl reload nginx
```

## 监控和日志

### 1. 应用监控

#### 1.1 健康检查端点
```python
# 在main.py中添加健康检查端点
@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "2.0.0"
    }

@app.get("/health/db")
async def database_health_check():
    try:
        async for db_session in get_database_session():
            await db_session.execute(text("SELECT 1"))
            await db_session.close()
            break
        return {"status": "healthy", "database": "connected"}
    except Exception as e:
        return {"status": "unhealthy", "database": "disconnected", "error": str(e)}
```

#### 1.2 日志配置
```python
# logging_config.py
import logging
import logging.handlers
import os

def setup_logging():
    """配置应用日志"""
    log_level = os.getenv('LOG_LEVEL', 'INFO')
    log_file = os.getenv('LOG_FILE_PATH', '/var/log/xiaoneng/app.log')
    
    # 创建日志目录
    os.makedirs(os.path.dirname(log_file), exist_ok=True)
    
    # 配置根日志器
    logging.basicConfig(
        level=getattr(logging, log_level),
        format='%(asctime)s [%(process)d] [%(levelname)s] %(name)s: %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S %z'
    )
    
    # 文件处理器（轮转日志）
    file_handler = logging.handlers.RotatingFileHandler(
        log_file,
        maxBytes=100*1024*1024,  # 100MB
        backupCount=5
    )
    file_handler.setFormatter(
        logging.Formatter('%(asctime)s [%(process)d] [%(levelname)s] %(name)s: %(message)s')
    )
    
    # 添加到根日志器
    logging.getLogger().addHandler(file_handler)
```

### 2. 系统监控

#### 2.1 使用Prometheus监控
```bash
# 安装prometheus-client
pip install prometheus-client

# 在应用中添加指标收集
from prometheus_client import Counter, Histogram, Gauge, generate_latest

# 定义指标
REQUEST_COUNT = Counter('http_requests_total', 'Total HTTP requests', ['method', 'endpoint'])
REQUEST_DURATION = Histogram('http_request_duration_seconds', 'HTTP request duration')
ACTIVE_CONNECTIONS = Gauge('active_database_connections', 'Active database connections')

@app.get("/metrics")
async def metrics():
    return Response(generate_latest(), media_type="text/plain")
```

## 性能优化

### 1. 数据库优化
```bash
# PostgreSQL配置优化 (/etc/postgresql/13/main/postgresql.conf)
shared_buffers = 256MB
effective_cache_size = 1GB
maintenance_work_mem = 64MB
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100
random_page_cost = 1.1
effective_io_concurrency = 200
work_mem = 4MB
min_wal_size = 1GB
max_wal_size = 4GB
```

### 2. 应用优化
```python
# 连接池配置
DATABASE_CONFIG = {
    "pool_size": 20,
    "max_overflow": 30,
    "pool_timeout": 30,
    "pool_recycle": 3600,
    "pool_pre_ping": True
}

# 缓存配置
CACHE_CONFIG = {
    "backend": "redis",
    "redis_url": "redis://localhost:6379/0",
    "default_timeout": 300,
    "key_prefix": "xiaoneng:"
}
```

## 故障排除

### 1. 常见问题

#### 1.1 数据库连接问题
```bash
# 检查数据库服务状态
sudo systemctl status postgresql

# 检查数据库连接
psql -h localhost -U xiaoneng_user -d xiaoneng

# 检查连接池状态
python -c "
import asyncio
from src.database.connection import get_database_session
async def test():
    async for db in get_database_session():
        print('Database connection successful')
        await db.close()
        break
asyncio.run(test())
"
```

#### 1.2 应用启动问题
```bash
# 检查Python环境
which python
python --version

# 检查依赖
pip list | grep -E "(fastapi|sqlalchemy|asyncpg)"

# 检查环境变量
env | grep -E "(DATABASE_URL|API_)"

# 测试应用启动
python -c "from src.main import app; print('App import successful')"
```

#### 1.3 性能问题
```bash
# 检查系统资源
htop
df -h
free -h

# 检查数据库性能
sudo -u postgres psql -d xiaoneng -c "
SELECT query, calls, total_time, mean_time 
FROM pg_stat_statements 
ORDER BY total_time DESC 
LIMIT 10;
"

# 检查慢查询日志
tail -f /var/log/postgresql/postgresql-13-main.log
```

## 安全配置

### 1. 防火墙配置
```bash
# Ubuntu UFW
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable

# CentOS firewalld
sudo firewall-cmd --permanent --add-service=ssh
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --reload
```

### 2. SSL证书配置
```bash
# 使用Let's Encrypt
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com

# 自动续期
sudo crontab -e
# 添加：0 12 * * * /usr/bin/certbot renew --quiet
```

---

**环境配置和部署指南** - 版本 1.0  
**最后更新**: 2024年8月  
**适用版本**: 第2阶段装卸载作业效能计算引擎  
**部署环境**: Linux/macOS, Docker, Kubernetes
