# 第2阶段 - 装卸载作业效能API接口文档

## API概述

装卸载作业效能API提供了完整的装卸载作业效能计算和管理功能，支持三段式作业流程建模、效能指标计算、贡献值分析和执行报告生成。所有接口都遵循RESTful设计规范，使用JSON格式进行数据交换。

### API基础信息
- **基础路径**: `/api/v1/loading-efficiency`
- **数据格式**: JSON
- **字符编码**: UTF-8
- **认证方式**: 根据项目配置
- **错误处理**: 统一的HTTP状态码和错误响应格式

## API端点详细说明

### 1. 启动装卸载作业效能计算

#### 接口信息
```http
POST /api/v1/loading-efficiency/calculate
Content-Type: application/json
```

#### 业务功能
创建并启动装卸载作业效能计算任务，支持三段式作业流程分析（仓库装载→地面运输→飞机装载）。

#### 请求参数
```json
{
    "scenario_id": "scenario-001",
    "task_name": "大型装备装载作业",
    "loading_phases": [
        {
            "phase_type": "warehouse_loading",
            "phase_name": "仓库装载",
            "sequence_order": 1,
            "expected_duration": 2.0,
            "resource_requirements": {
                "equipment": ["forklift", "crane"],
                "personnel": 6
            }
        },
        {
            "phase_type": "ground_transport",
            "phase_name": "地面运输",
            "sequence_order": 2,
            "expected_duration": 0.5,
            "resource_requirements": {
                "equipment": ["truck", "trailer"],
                "personnel": 2
            }
        },
        {
            "phase_type": "aircraft_loading",
            "phase_name": "飞机装载",
            "sequence_order": 3,
            "expected_duration": 1.5,
            "resource_requirements": {
                "equipment": ["loader", "conveyor"],
                "personnel": 8
            }
        }
    ],
    "warehouse_loading_data": {
        "cargo_type": "heavy_equipment",
        "cargo_weight": 15000,
        "cargo_volume": 50,
        "loading_time": 120,
        "equipment_count": 3,
        "personnel_count": 6
    },
    "ground_transport_data": {
        "transport_distance": 2.5,
        "transport_time": 30,
        "vehicle_count": 2,
        "driver_count": 2
    },
    "aircraft_loading_data": {
        "aircraft_type": "transport",
        "loading_position": "cargo_bay",
        "loading_time": 90,
        "equipment_count": 4,
        "personnel_count": 8
    },
    "equipment_data": {
        "equipment_001": {
            "type": "forklift",
            "capacity": 5000,
            "usage_time": 4.0,
            "processed_volume": 2500,
            "efficiency_score": 85
        },
        "equipment_002": {
            "type": "crane",
            "capacity": 20000,
            "usage_time": 3.5,
            "processed_volume": 12000,
            "efficiency_score": 90
        }
    },
    "personnel_data": {
        "personnel_001": {
            "role": "operator",
            "skill_level": "expert",
            "work_time": 8.0,
            "completed_tasks": 10,
            "quality_score": 90
        },
        "personnel_002": {
            "role": "supervisor",
            "skill_level": "senior",
            "work_time": 7.5,
            "completed_tasks": 8,
            "quality_score": 95
        }
    },
    "calculation_options": {
        "precision": "high",
        "include_phases": true,
        "calculate_contributions": true,
        "generate_report": true
    }
}
```

#### 响应示例
```json
{
    "id": "task-12345678-abcd-efgh-ijkl-123456789012",
    "task_name": "大型装备装载作业",
    "scenario_id": "scenario-001",
    "status": "pending",
    "priority": "normal",
    "progress_percentage": 0,
    "loading_phases": [...],
    "created_at": "2024-01-01T09:00:00Z",
    "estimated_completion_time": "2024-01-01T09:15:00Z"
}
```

#### 状态码
- `201 Created`: 任务创建成功
- `400 Bad Request`: 请求参数无效
- `500 Internal Server Error`: 服务器内部错误

### 2. 获取计算进度

#### 接口信息
```http
GET /api/v1/loading-efficiency/tasks/{task_id}/progress
```

#### 业务功能
获取装卸载作业效能计算任务的实时执行进度和状态信息。

#### 路径参数
- `task_id`: 任务ID（必需）

#### 响应示例
```json
{
    "task_id": "task-12345678-abcd-efgh-ijkl-123456789012",
    "status": "running",
    "progress_percentage": 65,
    "started_at": "2024-01-01T09:00:00Z",
    "estimated_completion_time": "2024-01-01T09:12:00Z",
    "current_phase": "贡献值计算",
    "error_message": null
}
```

#### 状态说明
- `pending`: 待处理
- `running`: 运行中
- `completed`: 已完成
- `failed`: 失败
- `cancelled`: 已取消

#### 状态码
- `200 OK`: 获取成功
- `404 Not Found`: 任务不存在

### 3. 获取效能计算结果

#### 接口信息
```http
GET /api/v1/loading-efficiency/tasks/{task_id}/results
```

#### 业务功能
获取装卸载作业效能计算的详细结果，包括各类指标的计算值。

#### 响应示例
```json
{
    "task_id": "task-12345678-abcd-efgh-ijkl-123456789012",
    "timeliness_indicators": {
        "operation_completion_time": 4.5,
        "average_response_time": 2.8,
        "on_time_completion_rate": 95.0,
        "processing_volume_per_hour": 12.5
    },
    "efficiency_indicators": {
        "equipment_utilization_rate": 85.0,
        "personnel_utilization_rate": 80.0,
        "operation_success_rate": 98.0,
        "equipment_operation_efficiency": 8.5
    },
    "quality_indicators": {
        "cargo_integrity_rate": 99.5,
        "operation_accuracy": 96.0,
        "safety_incident_rate": 0.0,
        "rework_rate": 1.5
    },
    "resource_config_indicators": {
        "equipment_config_rationality": 92.0,
        "personnel_config_rationality": 88.0,
        "resource_utilization_rate": 87.5,
        "cost_benefit_ratio": 1.25
    },
    "coordination_indicators": {
        "equipment_coordination_degree": 90.0,
        "human_machine_coordination_degree": 85.0,
        "process_smoothness_degree": 88.0,
        "waiting_time_ratio": 12.0
    },
    "overall_score": 87.2,
    "phase_results": {
        "仓库装载": {
            "completion_rate": 100.0,
            "efficiency_score": 85.5,
            "duration": 2.1
        },
        "地面运输": {
            "completion_rate": 100.0,
            "efficiency_score": 92.0,
            "duration": 0.4
        },
        "飞机装载": {
            "completion_rate": 100.0,
            "efficiency_score": 88.5,
            "duration": 1.6
        }
    }
}
```

#### 状态码
- `200 OK`: 获取成功
- `400 Bad Request`: 任务尚未完成
- `404 Not Found`: 任务不存在

### 4. 获取贡献值计算结果

#### 接口信息
```http
GET /api/v1/loading-efficiency/tasks/{task_id}/contributions
```

#### 业务功能
获取装卸载作业中各设备和人员的贡献值分析结果。

#### 响应示例
```json
{
    "task_id": "task-12345678-abcd-efgh-ijkl-123456789012",
    "equipment_contributions": {
        "equipment_001": {
            "contribution_score": 85.5,
            "usage_time": 4.0,
            "processed_volume": 2500,
            "efficiency_score": 85
        },
        "equipment_002": {
            "contribution_score": 92.0,
            "usage_time": 3.5,
            "processed_volume": 12000,
            "efficiency_score": 90
        }
    },
    "personnel_contributions": {
        "personnel_001": {
            "contribution_score": 88.0,
            "work_time": 8.0,
            "completed_tasks": 10,
            "quality_score": 90
        },
        "personnel_002": {
            "contribution_score": 91.5,
            "work_time": 7.5,
            "completed_tasks": 8,
            "quality_score": 95
        }
    },
    "top_equipment_contributors": [
        {
            "equipment_id": "equipment_002",
            "contribution_score": 92.0,
            "usage_time": 3.5,
            "processed_volume": 12000
        },
        {
            "equipment_id": "equipment_001",
            "contribution_score": 85.5,
            "usage_time": 4.0,
            "processed_volume": 2500
        }
    ],
    "top_personnel_contributors": [
        {
            "personnel_id": "personnel_002",
            "contribution_score": 91.5,
            "work_time": 7.5,
            "completed_tasks": 8
        },
        {
            "personnel_id": "personnel_001",
            "contribution_score": 88.0,
            "work_time": 8.0,
            "completed_tasks": 10
        }
    ]
}
```

### 5. 获取作业执行报告

#### 接口信息
```http
GET /api/v1/loading-efficiency/tasks/{task_id}/report
```

#### 业务功能
获取装卸载作业的标准化执行报告，包括效能分析和改进建议。

#### 响应示例
```json
{
    "task_id": "task-12345678-abcd-efgh-ijkl-123456789012",
    "task_name": "大型装备装载作业",
    "scenario_id": "scenario-001",
    "execution_summary": {
        "task_id": "task-12345678-abcd-efgh-ijkl-123456789012",
        "execution_time": 900,
        "completion_time": "2024-01-01T09:15:00Z",
        "total_cargo": "15吨",
        "total_phases": 3
    },
    "phase_analysis": {
        "仓库装载": {
            "duration": 2.1,
            "efficiency_score": 85.5,
            "equipment_usage": {
                "forklift": "2小时",
                "crane": "1.5小时"
            },
            "personnel_usage": {
                "operators": "6人",
                "total_hours": "12小时"
            }
        },
        "地面运输": {
            "duration": 0.4,
            "efficiency_score": 92.0,
            "equipment_usage": {
                "truck": "0.4小时",
                "trailer": "0.4小时"
            },
            "personnel_usage": {
                "drivers": "2人",
                "total_hours": "0.8小时"
            }
        },
        "飞机装载": {
            "duration": 1.6,
            "efficiency_score": 88.5,
            "equipment_usage": {
                "loader": "1.6小时",
                "conveyor": "1.6小时"
            },
            "personnel_usage": {
                "operators": "8人",
                "total_hours": "12.8小时"
            }
        }
    },
    "efficiency_analysis": {
        "timeliness": {
            "average_score": 86.8,
            "indicator_count": 4,
            "max_score": 95.0,
            "min_score": 78.5
        },
        "efficiency": {
            "average_score": 87.8,
            "indicator_count": 4,
            "max_score": 98.0,
            "min_score": 80.0
        },
        "quality": {
            "average_score": 91.3,
            "indicator_count": 4,
            "max_score": 99.5,
            "min_score": 85.0
        }
    },
    "contribution_analysis": {
        "equipment": {
            "total_equipment": 4,
            "average_contribution": 88.5,
            "top_contributor": "equipment_002"
        },
        "personnel": {
            "total_personnel": 16,
            "average_contribution": 89.2,
            "top_contributor": "personnel_002"
        }
    },
    "improvement_recommendations": [
        "建议优化仓库装载阶段的设备配置，提高作业效率",
        "建议加强人员培训，提升操作技能水平",
        "建议优化地面运输路径，减少运输时间",
        "整体效能表现良好，建议继续保持当前作业水平"
    ],
    "generated_at": "2024-01-01T09:15:30Z"
}
```

## 客户端使用示例

### Python客户端示例

```python
import asyncio
import aiohttp
import json

class LoadingEfficiencyAPIClient:
    """装卸载作业效能API客户端"""
    
    def __init__(self, base_url: str):
        self.base_url = base_url.rstrip('/')
        self.api_base = f"{self.base_url}/api/v1/loading-efficiency"
    
    async def start_calculation(self, calculation_request: dict) -> dict:
        """启动效能计算"""
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{self.api_base}/calculate",
                json=calculation_request,
                headers={"Content-Type": "application/json"}
            ) as response:
                if response.status == 201:
                    return await response.json()
                else:
                    raise Exception(f"API调用失败: {response.status}")
    
    async def get_progress(self, task_id: str) -> dict:
        """获取计算进度"""
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{self.api_base}/tasks/{task_id}/progress") as response:
                if response.status == 200:
                    return await response.json()
                else:
                    raise Exception(f"API调用失败: {response.status}")
    
    async def get_results(self, task_id: str) -> dict:
        """获取计算结果"""
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{self.api_base}/tasks/{task_id}/results") as response:
                if response.status == 200:
                    return await response.json()
                else:
                    raise Exception(f"API调用失败: {response.status}")
    
    async def get_contributions(self, task_id: str) -> dict:
        """获取贡献值"""
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{self.api_base}/tasks/{task_id}/contributions") as response:
                if response.status == 200:
                    return await response.json()
                else:
                    raise Exception(f"API调用失败: {response.status}")
    
    async def get_report(self, task_id: str) -> dict:
        """获取执行报告"""
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{self.api_base}/tasks/{task_id}/report") as response:
                if response.status == 200:
                    return await response.json()
                else:
                    raise Exception(f"API调用失败: {response.status}")

async def main():
    """使用示例"""
    client = LoadingEfficiencyAPIClient("http://localhost:8000")
    
    # 准备计算请求
    calculation_request = {
        "scenario_id": "scenario-001",
        "task_name": "API测试装载作业",
        "loading_phases": [
            {
                "phase_type": "warehouse_loading",
                "phase_name": "仓库装载",
                "sequence_order": 1,
                "expected_duration": 2.0
            }
        ],
        "warehouse_loading_data": {
            "cargo_weight": 5000,
            "loading_time": 120
        },
        "ground_transport_data": {
            "transport_distance": 2.0,
            "transport_time": 30
        },
        "aircraft_loading_data": {
            "loading_time": 90
        }
    }
    
    try:
        # 启动计算
        task = await client.start_calculation(calculation_request)
        task_id = task["id"]
        print(f"计算任务已启动: {task_id}")
        
        # 轮询进度
        while True:
            progress = await client.get_progress(task_id)
            print(f"进度: {progress['progress_percentage']}% - {progress['status']}")
            
            if progress["status"] == "completed":
                break
            elif progress["status"] == "failed":
                print(f"计算失败: {progress.get('error_message', '未知错误')}")
                return
            
            await asyncio.sleep(2)  # 等待2秒后再次查询
        
        # 获取结果
        results = await client.get_results(task_id)
        print(f"综合评分: {results['overall_score']}")
        
        # 获取贡献值
        contributions = await client.get_contributions(task_id)
        print(f"设备贡献值数量: {len(contributions['equipment_contributions'])}")
        
        # 获取执行报告
        report = await client.get_report(task_id)
        print(f"改进建议数量: {len(report['improvement_recommendations'])}")
        
    except Exception as e:
        print(f"API调用出错: {e}")

if __name__ == "__main__":
    asyncio.run(main())
```

### JavaScript客户端示例

```javascript
class LoadingEfficiencyAPIClient {
    constructor(baseUrl) {
        this.baseUrl = baseUrl.replace(/\/$/, '');
        this.apiBase = `${this.baseUrl}/api/v1/loading-efficiency`;
    }

    async startCalculation(calculationRequest) {
        const response = await fetch(`${this.apiBase}/calculate`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(calculationRequest)
        });

        if (response.status === 201) {
            return await response.json();
        } else {
            throw new Error(`API调用失败: ${response.status}`);
        }
    }

    async getProgress(taskId) {
        const response = await fetch(`${this.apiBase}/tasks/${taskId}/progress`);
        
        if (response.status === 200) {
            return await response.json();
        } else {
            throw new Error(`API调用失败: ${response.status}`);
        }
    }

    async getResults(taskId) {
        const response = await fetch(`${this.apiBase}/tasks/${taskId}/results`);
        
        if (response.status === 200) {
            return await response.json();
        } else {
            throw new Error(`API调用失败: ${response.status}`);
        }
    }

    async getContributions(taskId) {
        const response = await fetch(`${this.apiBase}/tasks/${taskId}/contributions`);
        
        if (response.status === 200) {
            return await response.json();
        } else {
            throw new Error(`API调用失败: ${response.status}`);
        }
    }

    async getReport(taskId) {
        const response = await fetch(`${this.apiBase}/tasks/${taskId}/report`);
        
        if (response.status === 200) {
            return await response.json();
        } else {
            throw new Error(`API调用失败: ${response.status}`);
        }
    }
}

// 使用示例
async function example() {
    const client = new LoadingEfficiencyAPIClient('http://localhost:8000');
    
    const calculationRequest = {
        scenario_id: 'scenario-001',
        task_name: 'JavaScript API测试',
        loading_phases: [{
            phase_type: 'warehouse_loading',
            phase_name: '仓库装载',
            sequence_order: 1,
            expected_duration: 2.0
        }],
        warehouse_loading_data: {
            cargo_weight: 5000,
            loading_time: 120
        },
        ground_transport_data: {
            transport_distance: 2.0,
            transport_time: 30
        },
        aircraft_loading_data: {
            loading_time: 90
        }
    };
    
    try {
        // 启动计算
        const task = await client.startCalculation(calculationRequest);
        console.log(`计算任务已启动: ${task.id}`);
        
        // 轮询进度
        let progress;
        do {
            progress = await client.getProgress(task.id);
            console.log(`进度: ${progress.progress_percentage}% - ${progress.status}`);
            
            if (progress.status === 'failed') {
                console.error(`计算失败: ${progress.error_message || '未知错误'}`);
                return;
            }
            
            if (progress.status !== 'completed') {
                await new Promise(resolve => setTimeout(resolve, 2000)); // 等待2秒
            }
        } while (progress.status !== 'completed');
        
        // 获取结果
        const results = await client.getResults(task.id);
        console.log(`综合评分: ${results.overall_score}`);
        
        // 获取贡献值
        const contributions = await client.getContributions(task.id);
        console.log(`设备贡献值数量: ${Object.keys(contributions.equipment_contributions).length}`);
        
        // 获取执行报告
        const report = await client.getReport(task.id);
        console.log(`改进建议数量: ${report.improvement_recommendations.length}`);
        
    } catch (error) {
        console.error(`API调用出错: ${error.message}`);
    }
}
```

## 错误处理

### 统一错误响应格式
```json
{
    "error": {
        "code": "VALIDATION_ERROR",
        "message": "请求参数验证失败",
        "details": {
            "field": "scenario_id",
            "reason": "字段不能为空"
        }
    },
    "timestamp": "2024-01-01T09:00:00Z",
    "path": "/api/v1/loading-efficiency/calculate"
}
```

### 常见错误码
- `VALIDATION_ERROR`: 请求参数验证失败
- `TASK_NOT_FOUND`: 任务不存在
- `TASK_NOT_COMPLETED`: 任务尚未完成
- `CALCULATION_ERROR`: 计算过程出错
- `INTERNAL_ERROR`: 服务器内部错误

## 性能和限制

### 性能指标
- **响应时间**: 平均 < 200ms
- **并发处理**: 支持100+并发请求
- **计算时间**: 小规模数据 < 10秒，中等规模数据 < 2分钟

### 使用限制
- 单个任务最大输入数据: 10MB
- 并发任务数量限制: 每用户最多10个
- API调用频率限制: 每分钟最多100次请求

## 版本信息
- **API版本**: v1
- **文档版本**: 1.0
- **最后更新**: 2024年8月
- **兼容性**: 向后兼容
