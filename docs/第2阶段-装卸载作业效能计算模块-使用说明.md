# 第2阶段 - 装卸载作业效能计算模块使用说明

## 模块概述

装卸载作业效能计算模块是第2阶段的核心功能模块，专门针对机场地面装卸载作业的效能评估和计算。该模块实现了完整的三段式作业流程建模（仓库装载→地面运输→飞机装载），提供了20个专业效能指标的计算功能，并支持设备和人员贡献值分析。

### 主要功能
- 装卸载作业任务管理和执行
- 五大类效能指标计算（时效性、效率、质量、资源配置、协调性）
- 设备和人员贡献值分析
- 标准化作业执行报告生成
- 三段式作业流程建模和分析

## 核心类详细说明

### 1. LoadingEfficiencyTaskService - 装卸载作业任务管理服务

#### 类的用途和职责
`LoadingEfficiencyTaskService` 是装卸载作业任务管理的核心服务类，提供任务的创建、状态管理、结果更新等业务逻辑处理。

#### 构造函数
```python
def __init__(self, db_session: AsyncSession) -> None:
    """
    初始化装卸载作业任务服务
    
    Args:
        db_session: 数据库会话，用于执行数据库操作
    """
```

#### 主要方法

##### create_task() - 创建装卸载作业任务
```python
async def create_task(self, task_data: Dict[str, Any]) -> LoadingEfficiencyTask:
    """
    创建装卸载作业效能计算任务
    
    Args:
        task_data: 任务创建数据，包含以下必要字段：
            - task_name: 任务名称
            - scenario_id: 关联场景ID
            - loading_phases: 装卸载阶段配置列表
            - input_data: 输入数据字典
            
    Returns:
        LoadingEfficiencyTask: 创建的任务实例
        
    Raises:
        ValueError: 当任务数据无效时
    """
```

**使用示例**：
```python
import asyncio
from src.database.connection import get_database_session
from src.services.loading_efficiency_service import LoadingEfficiencyTaskService
from src.database.models.loading_efficiency import LoadingPhaseEnum

async def create_loading_task_example():
    """创建装卸载作业任务示例"""
    
    async for db_session in get_database_session():
        try:
            service = LoadingEfficiencyTaskService(db_session)
            
            # 准备任务数据
            task_data = {
                "task_name": "大型装备装载作业",
                "task_description": "重型装备从仓库到飞机的完整装载流程",
                "scenario_id": "scenario-001",
                "loading_phases": [
                    {
                        "phase_type": LoadingPhaseEnum.WAREHOUSE_LOADING.value,
                        "phase_name": "仓库装载",
                        "sequence_order": 1,
                        "expected_duration": 2.0
                    },
                    {
                        "phase_type": LoadingPhaseEnum.GROUND_TRANSPORT.value,
                        "phase_name": "地面运输",
                        "sequence_order": 2,
                        "expected_duration": 0.5
                    },
                    {
                        "phase_type": LoadingPhaseEnum.AIRCRAFT_LOADING.value,
                        "phase_name": "飞机装载",
                        "sequence_order": 3,
                        "expected_duration": 1.5
                    }
                ],
                "input_data": {
                    "cargo_type": "heavy_equipment",
                    "cargo_weight": 15000,
                    "equipment_count": 8,
                    "personnel_count": 12,
                    "start_time": "2024-01-01T09:00:00",
                    "end_time": "2024-01-01T13:00:00"
                },
                "calculation_parameters": {
                    "precision": "high",
                    "include_phases": True
                },
                "created_by": "system_user"
            }
            
            # 创建任务
            task = await service.create_task(task_data)
            print(f"任务创建成功: {task.task_name}")
            print(f"任务ID: {task.id}")
            print(f"装载阶段数量: {len(task.loading_phases)}")
            
        finally:
            await db_session.close()
        break

if __name__ == "__main__":
    asyncio.run(create_loading_task_example())
```

##### update_task_status() - 更新任务状态
```python
async def update_task_status(self, task_id: str, status: TaskStatusEnum, 
                           progress_percentage: Optional[int] = None) -> Optional[LoadingEfficiencyTask]:
    """
    更新任务状态和进度
    
    Args:
        task_id: 任务ID
        status: 新状态（PENDING/RUNNING/COMPLETED/FAILED/CANCELLED）
        progress_percentage: 进度百分比（0-100）
        
    Returns:
        Optional[LoadingEfficiencyTask]: 更新后的任务实例
    """
```

**使用示例**：
```python
from src.database.models.loading_efficiency import TaskStatusEnum

async def update_task_status_example():
    """更新任务状态示例"""
    
    async for db_session in get_database_session():
        try:
            service = LoadingEfficiencyTaskService(db_session)
            
            # 更新任务为运行中状态
            updated_task = await service.update_task_status(
                task_id="task-001",
                status=TaskStatusEnum.RUNNING,
                progress_percentage=50
            )
            
            if updated_task:
                print(f"任务状态更新成功: {updated_task.status.value}")
                print(f"当前进度: {updated_task.progress_percentage}%")
                print(f"开始时间: {updated_task.started_at}")
            
        finally:
            await db_session.close()
        break
```

### 2. LoadingEfficiencyCalculationService - 装卸载作业效能计算服务

#### 类的用途和职责
`LoadingEfficiencyCalculationService` 是装卸载作业效能计算的核心服务类，提供效能指标计算、贡献值分析、执行报告生成等功能。

#### 主要方法

##### calculate_efficiency_indicators() - 计算效能指标
```python
async def calculate_efficiency_indicators(self, task: LoadingEfficiencyTask) -> Dict[str, Any]:
    """
    计算装卸载作业效能指标
    
    Args:
        task: 装卸载作业任务实例
        
    Returns:
        Dict[str, Any]: 效能指标计算结果，包含五大分类：
            - timeliness: 时效性指标
            - efficiency: 效率指标
            - quality: 质量指标
            - resource_config: 资源配置指标
            - coordination: 协调性指标
    """
```

**使用示例**：
```python
async def calculate_efficiency_example():
    """计算效能指标示例"""
    
    async for db_session in get_database_session():
        try:
            task_service = LoadingEfficiencyTaskService(db_session)
            calc_service = LoadingEfficiencyCalculationService(db_session)
            
            # 获取任务
            task = await task_service.get_task_by_id("task-001")
            if not task:
                print("任务不存在")
                return
            
            # 计算效能指标
            results = await calc_service.calculate_efficiency_indicators(task)
            
            # 显示计算结果
            print("=== 效能指标计算结果 ===")
            
            # 时效性指标
            if "timeliness" in results:
                timeliness = results["timeliness"]
                print(f"作业完成时间: {timeliness.get('operation_completion_time', 'N/A')} 小时")
                print(f"平均响应时间: {timeliness.get('average_response_time', 'N/A')} 分钟")
                print(f"准时完成率: {timeliness.get('on_time_completion_rate', 'N/A')}%")
            
            # 效率指标
            if "efficiency" in results:
                efficiency = results["efficiency"]
                print(f"设备利用率: {efficiency.get('equipment_utilization_rate', 'N/A')}%")
                print(f"人员利用率: {efficiency.get('personnel_utilization_rate', 'N/A')}%")
                print(f"作业成功率: {efficiency.get('operation_success_rate', 'N/A')}%")
            
            # 质量指标
            if "quality" in results:
                quality = results["quality"]
                print(f"货物完好率: {quality.get('cargo_integrity_rate', 'N/A')}%")
                print(f"作业精度: {quality.get('operation_accuracy', 'N/A')}%")
                print(f"安全事故率: {quality.get('safety_incident_rate', 'N/A')}%")
            
        finally:
            await db_session.close()
        break
```

##### calculate_contribution_values() - 计算贡献值
```python
async def calculate_contribution_values(self, task: LoadingEfficiencyTask) -> Dict[str, Any]:
    """
    计算贡献值
    
    Args:
        task: 装卸载作业任务实例
        
    Returns:
        Dict[str, Any]: 贡献值计算结果，包含：
            - equipment: 设备贡献值字典
            - personnel: 人员贡献值字典
    """
```

**使用示例**：
```python
async def calculate_contribution_example():
    """计算贡献值示例"""
    
    async for db_session in get_database_session():
        try:
            task_service = LoadingEfficiencyTaskService(db_session)
            calc_service = LoadingEfficiencyCalculationService(db_session)
            
            # 获取任务
            task = await task_service.get_task_by_id("task-001")
            if not task:
                return
            
            # 计算贡献值
            contributions = await calc_service.calculate_contribution_values(task)
            
            print("=== 贡献值分析结果 ===")
            
            # 设备贡献值
            if "equipment" in contributions:
                print("\n设备贡献值排名:")
                equipment_contrib = contributions["equipment"]
                sorted_equipment = sorted(
                    equipment_contrib.items(),
                    key=lambda x: x[1].get('contribution_score', 0),
                    reverse=True
                )
                
                for i, (eq_id, data) in enumerate(sorted_equipment[:5], 1):
                    print(f"{i}. {eq_id}: {data.get('contribution_score', 0):.2f}分")
                    print(f"   使用时间: {data.get('usage_time', 0):.1f}小时")
                    print(f"   处理量: {data.get('processed_volume', 0):.0f}吨")
            
            # 人员贡献值
            if "personnel" in contributions:
                print("\n人员贡献值排名:")
                personnel_contrib = contributions["personnel"]
                sorted_personnel = sorted(
                    personnel_contrib.items(),
                    key=lambda x: x[1].get('contribution_score', 0),
                    reverse=True
                )
                
                for i, (person_id, data) in enumerate(sorted_personnel[:5], 1):
                    print(f"{i}. {person_id}: {data.get('contribution_score', 0):.2f}分")
                    print(f"   工作时间: {data.get('work_time', 0):.1f}小时")
                    print(f"   完成任务: {data.get('completed_tasks', 0)}个")
            
        finally:
            await db_session.close()
        break
```

##### generate_execution_report() - 生成作业执行报告
```python
async def generate_execution_report(self, task: LoadingEfficiencyTask, 
                                  efficiency_results: Dict[str, Any],
                                  contribution_values: Dict[str, Any]) -> Dict[str, Any]:
    """
    生成作业执行报告
    
    Args:
        task: 装卸载作业任务实例
        efficiency_results: 效能计算结果
        contribution_values: 贡献值计算结果
        
    Returns:
        Dict[str, Any]: 作业执行报告，包含：
            - task_info: 任务基本信息
            - loading_phases: 装载阶段信息
            - efficiency_summary: 效能摘要
            - contribution_analysis: 贡献值分析
            - recommendations: 改进建议
    """
```

**使用示例**：
```python
async def generate_report_example():
    """生成执行报告示例"""
    
    async for db_session in get_database_session():
        try:
            task_service = LoadingEfficiencyTaskService(db_session)
            calc_service = LoadingEfficiencyCalculationService(db_session)
            
            # 获取任务
            task = await task_service.get_task_by_id("task-001")
            if not task:
                return
            
            # 计算效能指标和贡献值
            efficiency_results = await calc_service.calculate_efficiency_indicators(task)
            contribution_values = await calc_service.calculate_contribution_values(task)
            
            # 生成执行报告
            report = await calc_service.generate_execution_report(
                task, efficiency_results, contribution_values
            )
            
            print("=== 作业执行报告 ===")
            print(f"任务名称: {report['task_info'].get('task_name', 'N/A')}")
            print(f"执行时间: {report['task_info'].get('execution_time', 'N/A')}秒")
            print(f"完成时间: {report['task_info'].get('completion_time', 'N/A')}")
            
            # 效能摘要
            if "efficiency_summary" in report:
                print("\n效能摘要:")
                summary = report["efficiency_summary"]
                for category, data in summary.items():
                    if isinstance(data, dict) and "average_score" in data:
                        print(f"  {category}: 平均得分 {data['average_score']:.1f}")
            
            # 改进建议
            if "recommendations" in report:
                print("\n改进建议:")
                for i, recommendation in enumerate(report["recommendations"], 1):
                    print(f"  {i}. {recommendation}")
            
        finally:
            await db_session.close()
        break
```

## 装卸载作业计算器使用说明

### TimelinessCalculator - 时效性指标计算器

提供时效性相关指标的计算功能：

```python
from src.services.loading_efficiency_calculator import TimelinessCalculator
from datetime import datetime
from decimal import Decimal

# 计算作业完成时间
start_time = datetime(2024, 1, 1, 9, 0, 0)
end_time = datetime(2024, 1, 1, 13, 30, 0)
completion_time = TimelinessCalculator.calculate_operation_completion_time(start_time, end_time)
print(f"作业完成时间: {completion_time} 小时")

# 计算平均响应时间
response_times = [2.5, 3.0, 2.8, 3.2, 2.9]
avg_response = TimelinessCalculator.calculate_average_response_time(response_times)
print(f"平均响应时间: {avg_response} 分钟")

# 计算准时完成率
completion_rate = TimelinessCalculator.calculate_on_time_completion_rate(100, 95)
print(f"准时完成率: {completion_rate}%")

# 计算单位时间处理量
processing_volume = TimelinessCalculator.calculate_processing_volume_per_hour(
    Decimal('20.0'), Decimal('4.0')
)
print(f"单位时间处理量: {processing_volume} 吨/小时")
```

### EfficiencyCalculator - 效率指标计算器

提供效率相关指标的计算功能：

```python
from src.services.loading_efficiency_calculator import EfficiencyCalculator

# 计算设备利用率
equipment_utilization = EfficiencyCalculator.calculate_equipment_utilization_rate(
    Decimal('80.0'), Decimal('100.0')
)
print(f"设备利用率: {equipment_utilization}%")

# 计算人员利用率
personnel_utilization = EfficiencyCalculator.calculate_personnel_utilization_rate(
    Decimal('160.0'), Decimal('200.0')
)
print(f"人员利用率: {personnel_utilization}%")

# 计算作业成功率
success_rate = EfficiencyCalculator.calculate_operation_success_rate(50, 48)
print(f"作业成功率: {success_rate}%")

# 计算设备作业效率
operation_efficiency = EfficiencyCalculator.calculate_equipment_operation_efficiency(
    Decimal('5000'), Decimal('4.0')
)
print(f"设备作业效率: {operation_efficiency} 吨/小时")
```

## 依赖关系说明

```
LoadingEfficiencyTaskService
    ├── 依赖: AsyncSession (数据库会话)
    ├── 使用: LoadingEfficiencyTask (数据模型)
    └── 输出: LoadingEfficiencyTask 实例

LoadingEfficiencyCalculationService
    ├── 依赖: AsyncSession (数据库会话)
    ├── 使用: LoadingEfficiencyTask (数据模型)
    ├── 调用: TimelinessCalculator, EfficiencyCalculator 等计算器
    └── 输出: 计算结果字典

装卸载作业计算器
    ├── TimelinessCalculator (时效性指标)
    ├── EfficiencyCalculator (效率指标)
    ├── QualityCalculator (质量指标)
    ├── ResourceConfigCalculator (资源配置指标)
    └── CoordinationCalculator (协调性指标)
```

## 错误处理和异常情况

### 常见异常类型
- `ValueError`: 输入数据验证失败
- `DatabaseError`: 数据库操作失败
- `CalculationError`: 计算过程中的错误

### 错误处理示例
```python
async def error_handling_example():
    """错误处理示例"""
    
    async for db_session in get_database_session():
        try:
            service = LoadingEfficiencyTaskService(db_session)
            
            # 尝试创建任务
            task_data = {"task_name": "测试任务"}  # 缺少必要字段
            task = await service.create_task(task_data)
            
        except ValueError as e:
            print(f"数据验证错误: {e}")
        except Exception as e:
            print(f"其他错误: {e}")
            await db_session.rollback()
        finally:
            await db_session.close()
        break
```

## 性能优化建议

1. **批量操作**: 对于大量数据的计算，使用批量处理提高性能
2. **异步处理**: 利用异步特性处理并发计算任务
3. **缓存策略**: 对频繁查询的数据进行缓存
4. **数据库优化**: 为常用查询字段添加索引

## 最佳实践

1. **数据验证**: 始终验证输入数据的有效性
2. **事务管理**: 正确使用数据库事务确保数据一致性
3. **错误处理**: 实现完善的错误处理和日志记录
4. **资源管理**: 及时关闭数据库连接和释放资源
5. **测试覆盖**: 为所有核心功能编写完整的测试用例
