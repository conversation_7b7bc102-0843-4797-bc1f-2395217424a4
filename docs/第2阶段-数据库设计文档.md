# 第2阶段 - 数据库设计文档

## 数据库概述

第2阶段装卸载作业效能计算引擎的数据库设计基于PostgreSQL，采用关系型数据库架构，支持装卸载作业效能计算的完整数据管理需求。数据库设计遵循第三范式，确保数据一致性和完整性。

### 数据库基础信息
- **数据库类型**: PostgreSQL 13+
- **字符编码**: UTF-8
- **时区**: UTC
- **连接池**: 异步连接池（AsyncSession）
- **ORM框架**: SQLAlchemy 2.0+

## 数据库架构图

```mermaid
erDiagram
    scenarios ||--o{ loading_efficiency_tasks : "has"
    scenarios ||--o{ indicator_calculation_results : "has"
    scenarios ||--o{ calculation_tasks : "has"
    
    loading_efficiency_tasks ||--o{ loading_efficiency_results : "has"
    
    efficiency_indicators ||--o{ indicator_calculation_results : "references"
    efficiency_indicators ||--o{ loading_efficiency_results : "references"
    
    calculation_models ||--o{ calculation_tasks : "uses"
    
    scenarios {
        uuid id PK
        string scenario_name
        text scenario_description
        enum task_type
        enum threat_level
        enum status
        jsonb scenario_parameters
        jsonb metadata_info
        timestamp created_at
        timestamp updated_at
        string created_by
    }
    
    loading_efficiency_tasks {
        uuid id PK
        string task_name
        text task_description
        uuid scenario_id FK
        jsonb loading_phases
        jsonb input_data
        jsonb calculation_parameters
        enum status
        enum priority
        integer progress_percentage
        string started_at
        string completed_at
        integer execution_duration
        jsonb efficiency_results
        jsonb contribution_values
        jsonb execution_report
        text error_message
        jsonb error_details
        string created_by
        timestamp created_at
        timestamp updated_at
    }
    
    loading_efficiency_results {
        uuid id PK
        uuid task_id FK
        string indicator_code
        string indicator_name
        decimal calculated_value
        decimal confidence_level
        jsonb phase_results
        jsonb equipment_contributions
        jsonb personnel_contributions
        jsonb calculation_metadata
        timestamp created_at
        timestamp updated_at
    }
    
    efficiency_indicators {
        uuid id PK
        string indicator_name
        string indicator_code
        enum category
        text description
        text calculation_formula
        string unit
        decimal min_threshold
        decimal max_threshold
        decimal target_value
        decimal default_weight
        integer priority_level
        enum status
        string created_by
        jsonb metadata_info
        timestamp created_at
        timestamp updated_at
    }
```

## 核心数据表设计

### 1. loading_efficiency_tasks - 装卸载作业效能任务表

#### 表结构
```sql
CREATE TABLE loading_efficiency_tasks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    task_name VARCHAR(200) NOT NULL COMMENT '任务名称',
    task_description TEXT COMMENT '任务描述',
    scenario_id UUID NOT NULL REFERENCES scenarios(id) COMMENT '关联场景ID',
    loading_phases JSONB NOT NULL DEFAULT '[]' COMMENT '装卸载作业阶段列表',
    input_data JSONB NOT NULL DEFAULT '{}' COMMENT '输入数据',
    calculation_parameters JSONB NOT NULL DEFAULT '{}' COMMENT '计算参数配置',
    status VARCHAR(20) NOT NULL DEFAULT 'pending' COMMENT '任务状态',
    priority VARCHAR(20) NOT NULL DEFAULT 'normal' COMMENT '任务优先级',
    progress_percentage INTEGER NOT NULL DEFAULT 0 COMMENT '执行进度（百分比）',
    started_at VARCHAR(50) COMMENT '开始执行时间',
    completed_at VARCHAR(50) COMMENT '完成时间',
    execution_duration INTEGER COMMENT '执行时长（秒）',
    efficiency_results JSONB NOT NULL DEFAULT '{}' COMMENT '效能计算结果',
    contribution_values JSONB NOT NULL DEFAULT '{}' COMMENT '贡献值计算结果',
    execution_report JSONB NOT NULL DEFAULT '{}' COMMENT '作业执行报告',
    error_message TEXT COMMENT '错误信息',
    error_details JSONB NOT NULL DEFAULT '{}' COMMENT '错误详情',
    created_by VARCHAR(100) COMMENT '创建者',
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

#### 字段说明
- `id`: 主键，UUID类型，自动生成
- `task_name`: 任务名称，最大200字符
- `scenario_id`: 外键，关联scenarios表
- `loading_phases`: JSON数组，存储装卸载阶段配置
- `input_data`: JSON对象，存储计算输入数据
- `status`: 枚举值（pending/running/completed/failed/cancelled）
- `efficiency_results`: JSON对象，存储效能计算结果
- `contribution_values`: JSON对象，存储贡献值计算结果
- `execution_report`: JSON对象，存储作业执行报告

#### 索引设计
```sql
-- 主要查询索引
CREATE INDEX idx_loading_tasks_scenario_id ON loading_efficiency_tasks(scenario_id);
CREATE INDEX idx_loading_tasks_status ON loading_efficiency_tasks(status);
CREATE INDEX idx_loading_tasks_created_at ON loading_efficiency_tasks(created_at DESC);
CREATE INDEX idx_loading_tasks_priority_status ON loading_efficiency_tasks(priority, status);

-- 复合索引
CREATE INDEX idx_loading_tasks_scenario_status ON loading_efficiency_tasks(scenario_id, status);
```

### 2. loading_efficiency_results - 装卸载作业效能结果表

#### 表结构
```sql
CREATE TABLE loading_efficiency_results (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    task_id UUID NOT NULL REFERENCES loading_efficiency_tasks(id) ON DELETE CASCADE COMMENT '关联任务ID',
    indicator_code VARCHAR(50) NOT NULL COMMENT '指标编码',
    indicator_name VARCHAR(200) NOT NULL COMMENT '指标名称',
    calculated_value NUMERIC(15,6) NOT NULL COMMENT '计算得出的数值',
    confidence_level NUMERIC(5,4) COMMENT '置信度',
    phase_results JSONB NOT NULL DEFAULT '{}' COMMENT '各阶段的计算结果',
    equipment_contributions JSONB NOT NULL DEFAULT '{}' COMMENT '设备贡献值',
    personnel_contributions JSONB NOT NULL DEFAULT '{}' COMMENT '人员贡献值',
    calculation_metadata JSONB NOT NULL DEFAULT '{}' COMMENT '计算元数据',
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

#### 字段说明
- `task_id`: 外键，关联loading_efficiency_tasks表，级联删除
- `indicator_code`: 指标编码，对应效能指标
- `calculated_value`: 计算结果，使用高精度数值类型
- `confidence_level`: 置信度，范围0-1
- `phase_results`: JSON对象，存储各阶段的详细结果
- `equipment_contributions`: JSON对象，存储设备贡献值数据
- `personnel_contributions`: JSON对象，存储人员贡献值数据

#### 索引设计
```sql
-- 主要查询索引
CREATE INDEX idx_loading_results_task_id ON loading_efficiency_results(task_id);
CREATE INDEX idx_loading_results_indicator_code ON loading_efficiency_results(indicator_code);
CREATE INDEX idx_loading_results_created_at ON loading_efficiency_results(created_at DESC);

-- 复合索引
CREATE INDEX idx_loading_results_task_indicator ON loading_efficiency_results(task_id, indicator_code);
```

### 3. efficiency_indicators - 效能指标定义表（更新）

#### 表结构更新
```sql
-- 更新指标分类枚举
ALTER TYPE indicator_category_enum ADD VALUE IF NOT EXISTS 'efficiency';
ALTER TYPE indicator_category_enum ADD VALUE IF NOT EXISTS 'quality';
ALTER TYPE indicator_category_enum ADD VALUE IF NOT EXISTS 'resource_config';
ALTER TYPE indicator_category_enum ADD VALUE IF NOT EXISTS 'coordination';

-- 如果需要移除旧的分类值，需要重建枚举类型
-- 注意：这需要在没有数据的情况下执行，或者先迁移数据
```

#### 新增装卸载作业专用指标
```sql
-- 插入装卸载作业效能指标
INSERT INTO efficiency_indicators (
    indicator_name, indicator_code, category, description, 
    calculation_formula, unit, target_value, default_weight, 
    priority_level, status, created_by
) VALUES 
-- 时效性指标
('作业完成时间', 'OPERATION_COMPLETION_TIME', 'timeliness', 
 '从开始装载到完成装载的总耗时', '结束时间 - 开始时间', 
 '小时', 4.00, 0.3000, 1, 'active', 'system'),

('平均响应时间', 'AVERAGE_RESPONSE_TIME', 'timeliness', 
 '从指令下达到开始执行的平均时间', 'sum(响应时间列表) / len(响应时间列表)', 
 '分钟', 5.00, 0.2500, 2, 'active', 'system'),

-- 效率指标
('设备利用率', 'EQUIPMENT_UTILIZATION_RATE', 'efficiency', 
 '设备实际使用时间占可用时间的比例', '(实际使用时间 / 可用时间) * 100', 
 '百分比', 85.00, 0.3000, 1, 'active', 'system'),

('人员利用率', 'PERSONNEL_UTILIZATION_RATE', 'efficiency', 
 '人员实际工作时间占计划时间的比例', '(实际工作时间 / 计划工作时间) * 100', 
 '百分比', 80.00, 0.2500, 2, 'active', 'system'),

-- 质量指标
('货物完好率', 'CARGO_INTEGRITY_RATE', 'quality', 
 '货物在装载过程中保持完好的比例', '(完好货物数 / 总货物数) * 100', 
 '百分比', 99.00, 0.3000, 1, 'active', 'system'),

('作业精度', 'OPERATION_ACCURACY', 'quality', 
 '货物放置位置的准确度', '(准确位置数 / 目标位置数) * 100', 
 '百分比', 95.00, 0.2500, 2, 'active', 'system');
```

## JSON字段结构设计

### 1. loading_phases 字段结构
```json
[
    {
        "phase_type": "warehouse_loading",
        "phase_name": "仓库装载",
        "sequence_order": 1,
        "expected_duration": 2.0,
        "input_parameters": {
            "cargo_type": "heavy_equipment",
            "cargo_weight": 15000,
            "equipment_required": ["forklift", "crane"],
            "personnel_required": 6
        },
        "resource_requirements": {
            "equipment": ["forklift", "crane"],
            "personnel": 6,
            "special_tools": []
        }
    }
]
```

### 2. input_data 字段结构
```json
{
    "basic_info": {
        "start_time": "2024-01-01T09:00:00",
        "end_time": "2024-01-01T13:00:00",
        "cargo_type": "heavy_equipment",
        "total_weight": 15000,
        "total_volume": 50
    },
    "operation_data": {
        "total_operations": 20,
        "successful_operations": 19,
        "on_time_operations": 18,
        "response_times": [2.5, 3.0, 2.8]
    },
    "equipment_data": {
        "equipment_001": {
            "type": "forklift",
            "usage_time": 4.0,
            "processed_volume": 2500,
            "efficiency_score": 85
        }
    },
    "personnel_data": {
        "personnel_001": {
            "role": "operator",
            "work_time": 8.0,
            "completed_tasks": 10,
            "quality_score": 90
        }
    }
}
```

### 3. efficiency_results 字段结构
```json
{
    "timeliness": {
        "operation_completion_time": 4.5,
        "average_response_time": 2.8,
        "on_time_completion_rate": 95.0,
        "processing_volume_per_hour": 12.5
    },
    "efficiency": {
        "equipment_utilization_rate": 85.0,
        "personnel_utilization_rate": 80.0,
        "operation_success_rate": 98.0,
        "equipment_operation_efficiency": 8.5
    },
    "quality": {
        "cargo_integrity_rate": 99.5,
        "operation_accuracy": 96.0,
        "safety_incident_rate": 0.0,
        "rework_rate": 1.5
    },
    "resource_config": {
        "equipment_config_rationality": 92.0,
        "personnel_config_rationality": 88.0,
        "resource_utilization_rate": 87.5,
        "cost_benefit_ratio": 1.25
    },
    "coordination": {
        "equipment_coordination_degree": 90.0,
        "human_machine_coordination_degree": 85.0,
        "process_smoothness_degree": 88.0,
        "waiting_time_ratio": 12.0
    }
}
```

### 4. contribution_values 字段结构
```json
{
    "equipment": {
        "equipment_001": {
            "contribution_score": 85.5,
            "usage_time": 4.0,
            "processed_volume": 2500,
            "efficiency_score": 85,
            "phase_contributions": {
                "warehouse_loading": 0.6,
                "ground_transport": 0.0,
                "aircraft_loading": 0.4
            }
        }
    },
    "personnel": {
        "personnel_001": {
            "contribution_score": 88.0,
            "work_time": 8.0,
            "completed_tasks": 10,
            "quality_score": 90,
            "phase_contributions": {
                "warehouse_loading": 0.5,
                "ground_transport": 0.1,
                "aircraft_loading": 0.4
            }
        }
    }
}
```

## 数据库约束和触发器

### 1. 检查约束
```sql
-- 进度百分比约束
ALTER TABLE loading_efficiency_tasks 
ADD CONSTRAINT chk_progress_percentage 
CHECK (progress_percentage >= 0 AND progress_percentage <= 100);

-- 置信度约束
ALTER TABLE loading_efficiency_results 
ADD CONSTRAINT chk_confidence_level 
CHECK (confidence_level IS NULL OR (confidence_level >= 0 AND confidence_level <= 1));

-- 执行时长约束
ALTER TABLE loading_efficiency_tasks 
ADD CONSTRAINT chk_execution_duration 
CHECK (execution_duration IS NULL OR execution_duration >= 0);
```

### 2. 更新时间触发器
```sql
-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为装卸载任务表添加触发器
CREATE TRIGGER update_loading_efficiency_tasks_updated_at 
    BEFORE UPDATE ON loading_efficiency_tasks 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 为装卸载结果表添加触发器
CREATE TRIGGER update_loading_efficiency_results_updated_at 
    BEFORE UPDATE ON loading_efficiency_results 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

## 数据迁移脚本

### 1. 创建新表的迁移脚本
```sql
-- 001_create_loading_efficiency_tables.sql
BEGIN;

-- 创建装卸载任务状态枚举
CREATE TYPE loading_task_status_enum AS ENUM (
    'pending', 'running', 'completed', 'failed', 'cancelled'
);

-- 创建任务优先级枚举
CREATE TYPE loading_task_priority_enum AS ENUM (
    'low', 'normal', 'high', 'urgent'
);

-- 创建装卸载阶段枚举
CREATE TYPE loading_phase_enum AS ENUM (
    'warehouse_loading', 'ground_transport', 'aircraft_loading'
);

-- 创建装卸载效能任务表
CREATE TABLE loading_efficiency_tasks (
    -- 表结构如上所述
);

-- 创建装卸载效能结果表
CREATE TABLE loading_efficiency_results (
    -- 表结构如上所述
);

-- 创建索引
-- 索引创建语句如上所述

-- 添加外键约束
ALTER TABLE loading_efficiency_tasks 
ADD CONSTRAINT fk_loading_tasks_scenario 
FOREIGN KEY (scenario_id) REFERENCES scenarios(id);

ALTER TABLE loading_efficiency_results 
ADD CONSTRAINT fk_loading_results_task 
FOREIGN KEY (task_id) REFERENCES loading_efficiency_tasks(id) ON DELETE CASCADE;

-- 添加检查约束
-- 约束添加语句如上所述

-- 创建触发器
-- 触发器创建语句如上所述

COMMIT;
```

### 2. 更新现有表的迁移脚本
```sql
-- 002_update_scenarios_table.sql
BEGIN;

-- 为scenarios表添加装卸载任务关联支持
-- 这个通过ORM关系实现，不需要修改表结构

-- 更新效能指标分类枚举
-- 如果使用的是字符串类型而不是枚举，则不需要此步骤
-- ALTER TYPE indicator_category_enum ADD VALUE IF NOT EXISTS 'efficiency';
-- ALTER TYPE indicator_category_enum ADD VALUE IF NOT EXISTS 'quality';
-- ALTER TYPE indicator_category_enum ADD VALUE IF NOT EXISTS 'resource_config';
-- ALTER TYPE indicator_category_enum ADD VALUE IF NOT EXISTS 'coordination';

COMMIT;
```

## 性能优化建议

### 1. 索引优化
- 为经常查询的字段创建索引
- 使用复合索引优化多字段查询
- 定期分析查询计划并优化索引

### 2. JSON字段优化
- 使用GIN索引优化JSON字段查询
- 避免在JSON字段上进行复杂的聚合操作
- 考虑将频繁查询的JSON字段提取为独立列

### 3. 分区策略
- 对于大量历史数据，考虑按时间分区
- 可以按scenario_id进行分区以提高查询性能

### 4. 连接池配置
```python
# 数据库连接池配置建议
DATABASE_CONFIG = {
    "pool_size": 20,
    "max_overflow": 30,
    "pool_timeout": 30,
    "pool_recycle": 3600,
    "pool_pre_ping": True
}
```

## 备份和恢复策略

### 1. 备份策略
- 每日全量备份
- 每小时增量备份
- 重要操作前手动备份

### 2. 恢复测试
- 定期进行恢复测试
- 验证数据完整性
- 测试恢复时间

## 监控和维护

### 1. 性能监控
- 监控慢查询
- 跟踪数据库连接数
- 监控磁盘空间使用

### 2. 定期维护
- 定期更新表统计信息
- 清理过期的临时数据
- 重建碎片化的索引

---

**数据库设计文档** - 版本 1.0  
**最后更新**: 2024年8月  
**数据库版本**: PostgreSQL 13+  
**兼容性**: 支持SQLAlchemy 2.0+
