# 第2阶段 - 装卸载作业效能计算用户操作手册

## 用户手册概述

本手册面向装卸载作业效能计算系统的最终用户，提供详细的操作指导和使用说明。无论您是系统管理员、作业调度员还是效能分析师，都可以通过本手册快速掌握系统的使用方法。

### 适用用户
- **作业调度员**: 负责装卸载作业计划和执行
- **效能分析师**: 负责作业效能分析和优化
- **系统管理员**: 负责系统维护和配置
- **决策管理者**: 需要查看效能报告和分析结果

### 系统功能概览
- 装卸载作业效能计算和分析
- 设备和人员贡献值评估
- 作业执行报告生成
- 效能指标监控和对比
- 三段式作业流程管理

## 系统访问和登录

### 访问系统
1. 打开浏览器，访问系统地址：`http://your-domain.com`
2. 如果是本地部署，访问：`http://localhost:8000`
3. 系统支持Chrome、Firefox、Safari等主流浏览器

### API文档访问
- 访问 `http://your-domain.com/docs` 查看完整的API文档
- 支持在线测试API接口功能

## 核心功能操作指南

### 1. 创建装卸载作业效能计算任务

#### 1.1 通过API创建任务

**步骤1: 准备任务数据**
```json
{
    "scenario_id": "scenario-001",
    "task_name": "重型装备装载作业",
    "loading_phases": [
        {
            "phase_type": "warehouse_loading",
            "phase_name": "仓库装载",
            "sequence_order": 1,
            "expected_duration": 2.0
        },
        {
            "phase_type": "ground_transport",
            "phase_name": "地面运输",
            "sequence_order": 2,
            "expected_duration": 0.5
        },
        {
            "phase_type": "aircraft_loading",
            "phase_name": "飞机装载",
            "sequence_order": 3,
            "expected_duration": 1.5
        }
    ],
    "warehouse_loading_data": {
        "cargo_type": "heavy_equipment",
        "cargo_weight": 15000,
        "loading_time": 120,
        "equipment_count": 8,
        "personnel_count": 12
    },
    "ground_transport_data": {
        "transport_distance": 2.5,
        "transport_time": 30,
        "vehicle_count": 2,
        "driver_count": 2
    },
    "aircraft_loading_data": {
        "aircraft_type": "transport",
        "loading_time": 90,
        "equipment_count": 6,
        "personnel_count": 10
    }
}
```

**步骤2: 发送创建请求**
- 接口地址：`POST /api/v1/loading-efficiency/calculate`
- 请求方式：POST
- 内容类型：application/json

**步骤3: 获取任务ID**
系统返回任务信息，记录任务ID用于后续查询：
```json
{
    "id": "task-12345678-abcd-efgh-ijkl-123456789012",
    "task_name": "重型装备装载作业",
    "status": "pending",
    "created_at": "2024-01-01T09:00:00Z"
}
```

#### 1.2 任务参数说明

**基础参数**
- `scenario_id`: 关联的场景ID，必填
- `task_name`: 任务名称，建议使用描述性名称
- `loading_phases`: 装载阶段配置，支持三段式流程

**装载阶段类型**
- `warehouse_loading`: 仓库装载阶段
- `ground_transport`: 地面运输阶段  
- `aircraft_loading`: 飞机装载阶段

**数据输入要求**
- 重量单位：千克(kg)
- 时间单位：小时(hour)或分钟(minute)
- 距离单位：千米(km)
- 数量单位：个、台、人

### 2. 监控任务执行进度

#### 2.1 查看任务进度
**接口调用**
```bash
GET /api/v1/loading-efficiency/tasks/{task_id}/progress
```

**进度信息解读**
```json
{
    "task_id": "task-12345678-abcd-efgh-ijkl-123456789012",
    "status": "running",
    "progress_percentage": 65,
    "started_at": "2024-01-01T09:00:00Z",
    "estimated_completion_time": "2024-01-01T09:12:00Z",
    "current_phase": "贡献值计算",
    "error_message": null
}
```

**状态说明**
- `pending`: 任务已创建，等待执行
- `running`: 任务正在执行中
- `completed`: 任务执行完成
- `failed`: 任务执行失败
- `cancelled`: 任务已取消

**执行阶段说明**
- "效能指标计算": 正在计算各类效能指标
- "贡献值计算": 正在分析设备和人员贡献值
- "报告生成": 正在生成作业执行报告
- "结果整理": 正在整理最终结果

#### 2.2 处理任务异常
如果任务状态为`failed`，检查`error_message`字段：

**常见错误及解决方法**
- "数据验证失败": 检查输入数据格式和必填字段
- "计算超时": 减少数据规模或联系管理员
- "数据库连接失败": 联系系统管理员检查数据库状态

### 3. 查看效能计算结果

#### 3.1 获取详细计算结果
**接口调用**
```bash
GET /api/v1/loading-efficiency/tasks/{task_id}/results
```

**结果数据结构**
```json
{
    "task_id": "task-12345678-abcd-efgh-ijkl-123456789012",
    "overall_score": 87.2,
    "timeliness_indicators": {
        "operation_completion_time": 4.5,
        "average_response_time": 2.8,
        "on_time_completion_rate": 95.0,
        "processing_volume_per_hour": 12.5
    },
    "efficiency_indicators": {
        "equipment_utilization_rate": 85.0,
        "personnel_utilization_rate": 80.0,
        "operation_success_rate": 98.0,
        "equipment_operation_efficiency": 8.5
    },
    "quality_indicators": {
        "cargo_integrity_rate": 99.5,
        "operation_accuracy": 96.0,
        "safety_incident_rate": 0.0,
        "rework_rate": 1.5
    },
    "phase_results": {
        "仓库装载": {
            "completion_rate": 100.0,
            "efficiency_score": 85.5,
            "duration": 2.1
        }
    }
}
```

#### 3.2 结果指标解读

**综合评分 (overall_score)**
- 范围：0-100分
- 80分以上：优秀
- 60-80分：良好
- 40-60分：一般
- 40分以下：需要改进

**时效性指标**
- `operation_completion_time`: 作业完成时间（小时）
- `average_response_time`: 平均响应时间（分钟）
- `on_time_completion_rate`: 准时完成率（百分比）
- `processing_volume_per_hour`: 单位时间处理量（吨/小时）

**效率指标**
- `equipment_utilization_rate`: 设备利用率（百分比）
- `personnel_utilization_rate`: 人员利用率（百分比）
- `operation_success_rate`: 作业成功率（百分比）
- `equipment_operation_efficiency`: 设备作业效率（吨/小时）

**质量指标**
- `cargo_integrity_rate`: 货物完好率（百分比）
- `operation_accuracy`: 作业精度（百分比）
- `safety_incident_rate`: 安全事故率（百分比）
- `rework_rate`: 返工率（百分比）

### 4. 分析贡献值结果

#### 4.1 获取贡献值数据
**接口调用**
```bash
GET /api/v1/loading-efficiency/tasks/{task_id}/contributions
```

#### 4.2 贡献值分析方法

**设备贡献值分析**
1. 查看`top_equipment_contributors`获取排名前列的设备
2. 分析高贡献值设备的特点：使用时间、处理量、效率评分
3. 识别低贡献值设备，分析改进空间

**人员贡献值分析**
1. 查看`top_personnel_contributors`获取排名前列的人员
2. 分析高贡献值人员的特点：工作时间、完成任务数、质量评分
3. 制定人员培训和激励计划

**优化建议生成**
- 设备配置优化：调整设备分配和使用策略
- 人员安排优化：合理分配人员角色和工作量
- 流程改进：基于贡献值分析优化作业流程

### 5. 生成和查看执行报告

#### 5.1 获取执行报告
**接口调用**
```bash
GET /api/v1/loading-efficiency/tasks/{task_id}/report
```

#### 5.2 报告内容说明

**执行摘要**
- 任务基本信息：名称、执行时间、货物信息
- 总体效能评分和关键指标
- 各阶段执行情况概览

**阶段分析**
- 每个装载阶段的详细分析
- 设备和人员使用情况
- 时间和效率统计

**效能分析**
- 各类指标的详细分析
- 与目标值的对比
- 趋势分析和改进空间

**贡献值分析**
- 设备和人员贡献值排名
- 贡献值分布分析
- 优化建议

**改进建议**
- 基于分析结果的具体改进建议
- 优先级排序
- 预期效果评估

### 6. 数据导出和分享

#### 6.1 导出计算结果
```python
# Python示例：导出结果到Excel
import pandas as pd
import requests

# 获取结果数据
response = requests.get(f"http://localhost:8000/api/v1/loading-efficiency/tasks/{task_id}/results")
results = response.json()

# 转换为DataFrame并导出
df = pd.DataFrame([results])
df.to_excel(f"efficiency_results_{task_id}.xlsx", index=False)
```

#### 6.2 生成报告文档
```python
# 生成PDF报告示例
from reportlab.pdfgen import canvas
import requests

# 获取报告数据
response = requests.get(f"http://localhost:8000/api/v1/loading-efficiency/tasks/{task_id}/report")
report = response.json()

# 生成PDF报告
c = canvas.Canvas(f"efficiency_report_{task_id}.pdf")
c.drawString(100, 750, f"装卸载作业效能报告 - {report['task_name']}")
# 添加更多报告内容...
c.save()
```

## 最佳实践建议

### 1. 数据输入最佳实践

**数据准确性**
- 确保输入数据的准确性和完整性
- 使用标准化的单位和格式
- 定期校验和更新基础数据

**数据完整性**
- 提供尽可能完整的设备和人员数据
- 包含所有相关的作业阶段信息
- 记录异常情况和特殊条件

### 2. 结果分析最佳实践

**对比分析**
- 与历史数据进行对比分析
- 与行业标准或目标值对比
- 分析不同条件下的效能差异

**趋势分析**
- 定期执行效能计算，建立趋势数据
- 识别效能改进或下降的趋势
- 预测未来效能发展方向

### 3. 系统使用最佳实践

**任务管理**
- 使用描述性的任务名称
- 合理安排计算任务的执行时间
- 定期清理历史任务数据

**性能优化**
- 避免在系统高峰期执行大规模计算
- 合理控制并发任务数量
- 监控系统资源使用情况

## 常见问题解答

### Q1: 任务创建后一直处于pending状态怎么办？
**A**: 检查以下几点：
1. 系统是否正常运行
2. 数据库连接是否正常
3. 输入数据是否符合格式要求
4. 系统资源是否充足

### Q2: 计算结果与预期差异较大怎么办？
**A**: 建议：
1. 检查输入数据的准确性
2. 确认计算参数设置是否正确
3. 对比类似场景的历史结果
4. 联系技术支持进行详细分析

### Q3: 如何提高计算效率？
**A**: 优化建议：
1. 减少不必要的数据输入
2. 合理设置计算精度参数
3. 避免在系统繁忙时执行计算
4. 使用批量处理功能

### Q4: 系统支持多少并发用户？
**A**: 系统设计支持：
- 正常负载：100并发用户
- 最大承载：300并发用户
- 建议在高峰期合理安排使用时间

### Q5: 数据安全如何保障？
**A**: 安全措施：
1. 所有数据传输使用HTTPS加密
2. 数据库访问权限严格控制
3. 定期备份重要数据
4. 审计日志记录所有操作

## 技术支持和联系方式

### 获取帮助
1. **在线文档**: 查看完整的技术文档和API说明
2. **系统日志**: 查看详细的错误信息和操作记录
3. **功能验证**: 运行系统验证脚本检查功能状态

### 故障报告
报告系统问题时，请提供：
1. 详细的问题描述和重现步骤
2. 错误信息和日志记录
3. 系统环境和配置信息
4. 相关的任务ID和时间戳

### 功能建议
欢迎提供：
1. 新功能需求和改进建议
2. 用户体验优化建议
3. 性能优化建议
4. 文档改进建议

---

**用户操作手册** - 版本 1.0  
**最后更新**: 2024年8月  
**适用版本**: 第2阶段装卸载作业效能计算引擎  
**目标用户**: 系统最终用户
