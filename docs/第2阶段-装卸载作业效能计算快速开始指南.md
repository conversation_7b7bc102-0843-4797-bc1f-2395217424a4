# 第2阶段 - 装卸载作业效能计算快速开始指南

## 快速开始概述

本指南将帮助您在15分钟内快速搭建和运行第2阶段装卸载作业效能计算引擎，体验完整的装卸载作业效能计算功能。我们将通过最简单的方式让您快速上手，包括环境搭建、数据初始化、API调用和结果查看。

### 🎯 您将学到什么
- 如何快速搭建装卸载作业效能计算环境
- 如何创建和执行装卸载作业效能计算任务
- 如何查看效能计算结果和贡献值分析
- 如何生成和查看作业执行报告

### ⏱️ 预计用时
- 环境搭建：5分钟
- 数据初始化：3分钟
- 功能体验：7分钟

## 🚀 第一步：环境搭建

### 方式一：Docker快速部署（推荐）

如果您已安装Docker和Docker Compose，这是最快的方式：

```bash
# 1. 克隆项目（如果还没有）
git clone <project-repository>
cd xiaoneng

# 2. 使用Docker Compose快速启动
docker-compose up -d

# 3. 等待服务启动（约30秒）
docker-compose logs -f app

# 4. 验证服务状态
curl http://localhost:8000/health
```

### 方式二：本地Python环境

如果您更喜欢本地开发环境：

```bash
# 1. 创建Python虚拟环境
python3.9 -m venv xiaoneng_env
source xiaoneng_env/bin/activate  # Linux/macOS
# xiaoneng_env\Scripts\activate  # Windows

# 2. 安装依赖
pip install -r requirements.txt

# 3. 配置环境变量
cp .env.example .env
# 编辑.env文件，设置数据库连接等配置

# 4. 启动应用
uvicorn src.main:app --host 0.0.0.0 --port 8000 --reload
```

### 验证环境
访问 http://localhost:8000/docs 查看API文档，如果能正常访问说明环境搭建成功。

## 📊 第二步：数据初始化

### 初始化数据库和测试数据

```bash
# 1. 初始化数据库结构
python scripts/init_db.py

# 2. 生成装卸载作业测试数据
python scripts/generate_loading_efficiency_test_data.py

# 3. 验证数据初始化
python scripts/validate_loading_efficiency_functionality.py
```

### 验证数据初始化结果
如果看到以下输出，说明数据初始化成功：
```
✅ 装卸载作业任务管理功能验证通过
✅ 装卸载作业效能计算器功能验证通过
✅ 装卸载作业效能计算服务功能验证通过
✅ 数据完整性验证通过
🎉 所有装卸载作业效能功能验证成功！
```

## 🎮 第三步：功能体验

### 3.1 使用API接口体验（推荐）

#### 启动装卸载作业效能计算

```bash
# 使用curl调用API
curl -X POST "http://localhost:8000/api/v1/loading-efficiency/calculate" \
  -H "Content-Type: application/json" \
  -d '{
    "scenario_id": "scenario-001",
    "task_name": "快速开始装载作业演示",
    "loading_phases": [
      {
        "phase_type": "warehouse_loading",
        "phase_name": "仓库装载",
        "sequence_order": 1,
        "expected_duration": 2.0
      },
      {
        "phase_type": "ground_transport",
        "phase_name": "地面运输",
        "sequence_order": 2,
        "expected_duration": 0.5
      },
      {
        "phase_type": "aircraft_loading",
        "phase_name": "飞机装载",
        "sequence_order": 3,
        "expected_duration": 1.5
      }
    ],
    "warehouse_loading_data": {
      "cargo_type": "equipment",
      "cargo_weight": 8000,
      "loading_time": 120,
      "equipment_count": 4,
      "personnel_count": 8
    },
    "ground_transport_data": {
      "transport_distance": 2.0,
      "transport_time": 30,
      "vehicle_count": 2,
      "driver_count": 2
    },
    "aircraft_loading_data": {
      "aircraft_type": "transport",
      "loading_time": 90,
      "equipment_count": 3,
      "personnel_count": 6
    },
    "equipment_data": {
      "equipment_001": {
        "type": "forklift",
        "usage_time": 3.5,
        "processed_volume": 4000,
        "efficiency_score": 88
      },
      "equipment_002": {
        "type": "crane",
        "usage_time": 2.0,
        "processed_volume": 4000,
        "efficiency_score": 92
      }
    },
    "personnel_data": {
      "personnel_001": {
        "role": "operator",
        "work_time": 4.0,
        "completed_tasks": 8,
        "quality_score": 90
      },
      "personnel_002": {
        "role": "supervisor",
        "work_time": 4.0,
        "completed_tasks": 6,
        "quality_score": 95
      }
    }
  }'
```

#### 获取任务ID并查看进度

从上面的响应中获取`task_id`，然后查看计算进度：

```bash
# 替换YOUR_TASK_ID为实际的任务ID
export TASK_ID="YOUR_TASK_ID"

# 查看计算进度
curl "http://localhost:8000/api/v1/loading-efficiency/tasks/$TASK_ID/progress"
```

#### 查看计算结果

等待任务完成后（status为"completed"），查看详细结果：

```bash
# 查看效能计算结果
curl "http://localhost:8000/api/v1/loading-efficiency/tasks/$TASK_ID/results"

# 查看贡献值分析
curl "http://localhost:8000/api/v1/loading-efficiency/tasks/$TASK_ID/contributions"

# 查看作业执行报告
curl "http://localhost:8000/api/v1/loading-efficiency/tasks/$TASK_ID/report"
```

### 3.2 使用Python脚本体验

创建一个快速体验脚本：

```python
# quick_start_demo.py
import asyncio
import aiohttp
import json
import time

async def quick_start_demo():
    """装卸载作业效能计算快速演示"""
    
    base_url = "http://localhost:8000"
    
    # 准备计算请求数据
    calculation_request = {
        "scenario_id": "scenario-001",
        "task_name": "Python快速演示",
        "loading_phases": [
            {
                "phase_type": "warehouse_loading",
                "phase_name": "仓库装载",
                "sequence_order": 1,
                "expected_duration": 2.0
            },
            {
                "phase_type": "ground_transport", 
                "phase_name": "地面运输",
                "sequence_order": 2,
                "expected_duration": 0.5
            },
            {
                "phase_type": "aircraft_loading",
                "phase_name": "飞机装载", 
                "sequence_order": 3,
                "expected_duration": 1.5
            }
        ],
        "warehouse_loading_data": {
            "cargo_weight": 6000,
            "loading_time": 100
        },
        "ground_transport_data": {
            "transport_distance": 1.5,
            "transport_time": 25
        },
        "aircraft_loading_data": {
            "loading_time": 80
        },
        "equipment_data": {
            "equipment_001": {
                "usage_time": 3.0,
                "processed_volume": 3000,
                "efficiency_score": 85
            }
        },
        "personnel_data": {
            "personnel_001": {
                "work_time": 3.5,
                "completed_tasks": 6,
                "quality_score": 88
            }
        }
    }
    
    async with aiohttp.ClientSession() as session:
        try:
            print("🚀 启动装卸载作业效能计算...")
            
            # 1. 启动计算任务
            async with session.post(
                f"{base_url}/api/v1/loading-efficiency/calculate",
                json=calculation_request
            ) as response:
                if response.status == 201:
                    task = await response.json()
                    task_id = task["id"]
                    print(f"✅ 计算任务已创建: {task_id}")
                    print(f"   任务名称: {task['task_name']}")
                else:
                    print(f"❌ 任务创建失败: {response.status}")
                    return
            
            # 2. 轮询进度
            print("\n📊 监控计算进度...")
            while True:
                async with session.get(
                    f"{base_url}/api/v1/loading-efficiency/tasks/{task_id}/progress"
                ) as response:
                    if response.status == 200:
                        progress = await response.json()
                        status = progress["status"]
                        percentage = progress["progress_percentage"]
                        current_phase = progress.get("current_phase", "")
                        
                        print(f"   进度: {percentage}% - {status} - {current_phase}")
                        
                        if status == "completed":
                            print("✅ 计算完成！")
                            break
                        elif status == "failed":
                            print(f"❌ 计算失败: {progress.get('error_message', '未知错误')}")
                            return
                        
                        await asyncio.sleep(2)
                    else:
                        print(f"❌ 获取进度失败: {response.status}")
                        return
            
            # 3. 获取计算结果
            print("\n📈 获取效能计算结果...")
            async with session.get(
                f"{base_url}/api/v1/loading-efficiency/tasks/{task_id}/results"
            ) as response:
                if response.status == 200:
                    results = await response.json()
                    print(f"   综合效能评分: {results['overall_score']:.1f}")
                    
                    # 显示各类指标结果
                    categories = {
                        "timeliness_indicators": "时效性指标",
                        "efficiency_indicators": "效率指标", 
                        "quality_indicators": "质量指标",
                        "resource_config_indicators": "资源配置指标",
                        "coordination_indicators": "协调性指标"
                    }
                    
                    for key, name in categories.items():
                        if key in results and results[key]:
                            indicators = results[key]
                            avg_score = sum(indicators.values()) / len(indicators)
                            print(f"   {name}: {avg_score:.1f} (包含{len(indicators)}个指标)")
                else:
                    print(f"❌ 获取结果失败: {response.status}")
                    return
            
            # 4. 获取贡献值分析
            print("\n🏆 获取贡献值分析...")
            async with session.get(
                f"{base_url}/api/v1/loading-efficiency/tasks/{task_id}/contributions"
            ) as response:
                if response.status == 200:
                    contributions = await response.json()
                    
                    # 设备贡献值
                    equipment_contrib = contributions.get("equipment_contributions", {})
                    if equipment_contrib:
                        print("   设备贡献值排名:")
                        sorted_equipment = sorted(
                            equipment_contrib.items(),
                            key=lambda x: x[1].get('contribution_score', 0),
                            reverse=True
                        )
                        for i, (eq_id, data) in enumerate(sorted_equipment[:3], 1):
                            score = data.get('contribution_score', 0)
                            print(f"     {i}. {eq_id}: {score:.1f}分")
                    
                    # 人员贡献值
                    personnel_contrib = contributions.get("personnel_contributions", {})
                    if personnel_contrib:
                        print("   人员贡献值排名:")
                        sorted_personnel = sorted(
                            personnel_contrib.items(),
                            key=lambda x: x[1].get('contribution_score', 0),
                            reverse=True
                        )
                        for i, (person_id, data) in enumerate(sorted_personnel[:3], 1):
                            score = data.get('contribution_score', 0)
                            print(f"     {i}. {person_id}: {score:.1f}分")
                else:
                    print(f"❌ 获取贡献值失败: {response.status}")
                    return
            
            # 5. 获取执行报告
            print("\n📋 获取作业执行报告...")
            async with session.get(
                f"{base_url}/api/v1/loading-efficiency/tasks/{task_id}/report"
            ) as response:
                if response.status == 200:
                    report = await response.json()
                    
                    # 显示改进建议
                    recommendations = report.get("improvement_recommendations", [])
                    if recommendations:
                        print("   改进建议:")
                        for i, recommendation in enumerate(recommendations[:3], 1):
                            print(f"     {i}. {recommendation}")
                    
                    print(f"\n🎉 演示完成！任务ID: {task_id}")
                    print("   您可以通过API继续查看更多详细信息")
                else:
                    print(f"❌ 获取报告失败: {response.status}")
                    return
                    
        except Exception as e:
            print(f"❌ 演示过程中出错: {e}")

if __name__ == "__main__":
    asyncio.run(quick_start_demo())
```

运行演示脚本：

```bash
python quick_start_demo.py
```

### 3.3 使用Web界面体验

访问 http://localhost:8000/docs 使用Swagger UI界面：

1. 展开 `POST /api/v1/loading-efficiency/calculate` 接口
2. 点击 "Try it out" 按钮
3. 使用示例数据或修改参数
4. 点击 "Execute" 执行请求
5. 复制返回的 `task_id`
6. 使用其他接口查看进度和结果

## 📊 第四步：理解结果

### 效能计算结果解读

```json
{
  "overall_score": 87.2,  // 综合效能评分（0-100）
  "timeliness_indicators": {
    "operation_completion_time": 4.0,      // 作业完成时间（小时）
    "average_response_time": 2.5,          // 平均响应时间（分钟）
    "on_time_completion_rate": 95.0,       // 准时完成率（%）
    "processing_volume_per_hour": 12.5     // 单位时间处理量（吨/小时）
  },
  "efficiency_indicators": {
    "equipment_utilization_rate": 85.0,    // 设备利用率（%）
    "personnel_utilization_rate": 80.0,    // 人员利用率（%）
    "operation_success_rate": 98.0,        // 作业成功率（%）
    "equipment_operation_efficiency": 8.5  // 设备作业效率（吨/小时）
  },
  "quality_indicators": {
    "cargo_integrity_rate": 99.5,          // 货物完好率（%）
    "operation_accuracy": 96.0,            // 作业精度（%）
    "safety_incident_rate": 0.0,           // 安全事故率（%）
    "rework_rate": 1.5                     // 返工率（%）
  }
}
```

### 贡献值分析解读

```json
{
  "equipment_contributions": {
    "equipment_001": {
      "contribution_score": 85.5,    // 设备贡献值评分
      "usage_time": 4.0,             // 使用时间（小时）
      "processed_volume": 2500,      // 处理量（吨）
      "efficiency_score": 85         // 效率评分
    }
  },
  "personnel_contributions": {
    "personnel_001": {
      "contribution_score": 88.0,    // 人员贡献值评分
      "work_time": 8.0,              // 工作时间（小时）
      "completed_tasks": 10,         // 完成任务数
      "quality_score": 90            // 质量评分
    }
  }
}
```

## 🔧 常见问题解决

### 问题1：API调用返回404错误
```bash
# 检查服务是否正常启动
curl http://localhost:8000/health

# 检查API路径是否正确
curl http://localhost:8000/docs
```

### 问题2：数据库连接错误
```bash
# 检查数据库服务状态
sudo systemctl status postgresql

# 检查数据库连接配置
cat .env | grep DATABASE_URL
```

### 问题3：计算任务一直处于pending状态
```bash
# 检查应用日志
docker-compose logs -f app
# 或者
tail -f /var/log/xiaoneng/app.log
```

### 问题4：内存不足导致计算失败
```bash
# 检查系统资源
free -h
df -h

# 减少测试数据规模或增加系统内存
```

## 🎯 下一步学习

恭喜您完成了快速开始！现在您可以：

1. **深入学习**：阅读详细的技术文档了解更多功能
   - [装卸载作业效能计算模块使用说明](第2阶段-装卸载作业效能计算模块-使用说明.md)
   - [装卸载作业API接口文档](第2阶段-装卸载作业API接口文档.md)

2. **自定义配置**：根据实际需求调整计算参数和指标权重

3. **集成开发**：将API集成到您的应用系统中

4. **性能优化**：根据数据规模调整系统配置

5. **扩展功能**：基于现有框架开发新的效能指标

## 📞 获取帮助

如果在使用过程中遇到问题：

1. 查看 [故障排除指南](第2阶段-环境配置和部署指南.md#故障排除)
2. 检查应用日志获取详细错误信息
3. 运行验证脚本确认系统状态
4. 查阅相关技术文档

---

**快速开始指南** - 版本 1.0  
**最后更新**: 2024年8月  
**适用版本**: 第2阶段装卸载作业效能计算引擎  
**预计完成时间**: 15分钟
