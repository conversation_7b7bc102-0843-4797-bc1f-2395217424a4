# 核心框架依赖
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# 数据库依赖
sqlalchemy==2.0.23
alembic==1.13.1
psycopg2-binary==2.9.9
asyncpg==0.29.0

# 缓存和消息队列
redis==5.0.1
aio-pika==9.3.1

# 数据处理和科学计算
numpy==1.24.4
pandas==2.1.4
scipy==1.11.4

# 测试框架
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
httpx==0.25.2

# 代码质量工具
black==23.11.0
flake8==6.1.0
mypy==1.7.1

# 配置管理
python-dotenv==1.0.0
pyyaml==6.0.1

# 日志和监控
structlog==23.2.0

# 第3阶段新增依赖 - 高级算法和优化功能
# 优化算法库
deap==1.4.1                    # 进化算法框架
platypus-opt==1.0.4           # 多目标优化
pymoo==*******               # 多目标优化框架
networkx==3.2.1               # 图论和网络分析
scikit-optimize==0.9.0        # 贝叶斯优化

# 数值计算增强
cvxpy==1.4.1                  # 凸优化
pulp==2.7.0                   # 线性规划
ortools==9.8.3296             # 约束规划和路由

# 并行计算
joblib==1.3.2                 # 并行计算工具
dask==2023.12.0               # 分布式计算

# 可视化
matplotlib==3.8.2             # 基础绘图
plotly==5.17.0                # 交互式图表
seaborn==0.13.0               # 统计可视化
