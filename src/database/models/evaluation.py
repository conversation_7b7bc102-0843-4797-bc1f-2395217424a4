"""
综合评估数据模型

遵循base-rules.md规范：
- 类文档字符串规则：说明类的用途、主要功能
- 使用描述性的类和属性命名
- 模块职责单一性：只包含综合评估相关的数据模型
"""

from datetime import datetime
from enum import Enum
from typing import Optional, Dict, Any, List
from decimal import Decimal

from sqlalchemy import String, Text, Integer, JSON, Enum as SQLEnum, DateTime, Float
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy import ForeignKey

from src.database.models.base import BaseModel


class EvaluationStatusEnum(str, Enum):
    """评估状态枚举"""
    PENDING = "pending"           # 待评估
    EVALUATING = "evaluating"     # 评估中
    COMPLETED = "completed"       # 已完成
    FAILED = "failed"            # 评估失败
    CANCELLED = "cancelled"       # 已取消


class EvaluationMethodEnum(str, Enum):
    """评估方法枚举"""
    WEIGHTED_AVERAGE = "weighted_average"         # 加权平均
    FUZZY_EVALUATION = "fuzzy_evaluation"         # 模糊评价
    AHP = "ahp"                                  # 层次分析法
    TOPSIS = "topsis"                            # TOPSIS方法
    GREY_RELATIONAL = "grey_relational"          # 灰色关联分析
    ELECTRE = "electre"                          # ELECTRE方法


class ComparisonTypeEnum(str, Enum):
    """比较类型枚举"""
    PAIRWISE = "pairwise"         # 两两比较
    RANKING = "ranking"           # 排序比较
    CLUSTERING = "clustering"     # 聚类比较
    PARETO_ANALYSIS = "pareto_analysis"  # 帕累托分析


class EvaluationTask(BaseModel):
    """
    综合评估任务数据模型
    
    存储综合评估任务的详细信息：
    - 评估方案和指标配置
    - 权重设定和评估方法
    - 评估结果和分析报告
    """
    
    __tablename__ = "evaluation_tasks"
    
    # 任务基本信息
    task_name: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        comment="任务名称"
    )
    
    task_description: Mapped[Optional[str]] = mapped_column(
        Text,
        comment="任务描述"
    )
    
    # 评估方案
    scheme_ids: Mapped[List[str]] = mapped_column(
        JSON,
        nullable=False,
        comment="参与评估的方案ID列表"
    )
    
    scheme_names: Mapped[List[str]] = mapped_column(
        JSON,
        nullable=False,
        comment="方案名称列表"
    )
    
    # 评估场景
    evaluation_scenarios: Mapped[List[Dict[str, Any]]] = mapped_column(
        JSON,
        nullable=False,
        comment="评估场景定义"
    )
    
    # 指标配置
    evaluation_indicators: Mapped[List[str]] = mapped_column(
        JSON,
        nullable=False,
        comment="评估指标ID列表"
    )
    
    indicator_weights: Mapped[Dict[str, float]] = mapped_column(
        JSON,
        nullable=False,
        comment="指标权重配置"
    )
    
    weight_config_id: Mapped[Optional[str]] = mapped_column(
        UUID(as_uuid=False),
        ForeignKey("indicator_weight_configs.id"),
        comment="权重配置ID"
    )
    
    # 评估方法
    evaluation_method: Mapped[EvaluationMethodEnum] = mapped_column(
        SQLEnum(EvaluationMethodEnum),
        nullable=False,
        comment="评估方法"
    )
    
    method_parameters: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        comment="方法参数配置"
    )
    
    # 分析配置
    sensitivity_analysis: Mapped[bool] = mapped_column(
        default=False,
        comment="是否进行敏感性分析"
    )
    
    uncertainty_analysis: Mapped[bool] = mapped_column(
        default=False,
        comment="是否进行不确定性分析"
    )
    
    comparison_type: Mapped[ComparisonTypeEnum] = mapped_column(
        SQLEnum(ComparisonTypeEnum),
        default=ComparisonTypeEnum.RANKING,
        comment="比较分析类型"
    )
    
    # 执行状态
    status: Mapped[EvaluationStatusEnum] = mapped_column(
        SQLEnum(EvaluationStatusEnum),
        default=EvaluationStatusEnum.PENDING,
        comment="评估状态"
    )
    
    progress_percentage: Mapped[int] = mapped_column(
        Integer,
        default=0,
        comment="评估进度百分比"
    )
    
    # 执行时间
    started_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        comment="开始评估时间"
    )
    
    completed_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        comment="完成时间"
    )
    
    evaluation_duration: Mapped[Optional[int]] = mapped_column(
        Integer,
        comment="评估耗时（秒）"
    )
    
    # 评估结果
    scheme_scores: Mapped[Optional[Dict[str, float]]] = mapped_column(
        JSON,
        comment="方案综合评分"
    )
    
    scheme_ranking: Mapped[Optional[List[Dict[str, Any]]]] = mapped_column(
        JSON,
        comment="方案排序结果"
    )
    
    detailed_scores: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        comment="详细评分数据"
    )
    
    # 分析结果
    comparison_analysis: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        comment="比较分析结果"
    )
    
    sensitivity_results: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        comment="敏感性分析结果"
    )
    
    improvement_suggestions: Mapped[Optional[List[Dict[str, Any]]]] = mapped_column(
        JSON,
        comment="改进建议"
    )
    
    # 错误信息
    error_message: Mapped[Optional[str]] = mapped_column(
        Text,
        comment="错误信息"
    )
    
    error_details: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        comment="错误详情"
    )
    
    # 创建者信息
    created_by: Mapped[Optional[str]] = mapped_column(
        String(100),
        comment="创建者"
    )


class WeightConfiguration(BaseModel):
    """
    权重配置数据模型
    
    存储不同场景下的指标权重配置：
    - 权重配置模板和参数
    - 适用场景和条件
    - 权重来源和依据
    """
    
    __tablename__ = "weight_configurations"
    
    # 配置基本信息
    config_name: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        comment="配置名称"
    )
    
    config_description: Mapped[Optional[str]] = mapped_column(
        Text,
        comment="配置描述"
    )
    
    # 适用场景
    applicable_scenarios: Mapped[List[str]] = mapped_column(
        JSON,
        nullable=False,
        comment="适用场景类型列表"
    )
    
    scenario_conditions: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        comment="场景条件限制"
    )
    
    # 权重配置
    indicator_weights: Mapped[Dict[str, float]] = mapped_column(
        JSON,
        nullable=False,
        comment="指标权重配置"
    )
    
    category_weights: Mapped[Optional[Dict[str, float]]] = mapped_column(
        JSON,
        comment="指标分类权重"
    )
    
    # 权重来源
    weight_source: Mapped[str] = mapped_column(
        String(100),
        nullable=False,
        comment="权重来源（expert/ahp/data_driven等）"
    )
    
    source_details: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        comment="来源详细信息"
    )
    
    # 一致性检验
    consistency_ratio: Mapped[Optional[float]] = mapped_column(
        Float,
        comment="一致性比率（AHP方法）"
    )
    
    validation_results: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        comment="权重验证结果"
    )
    
    # 版本控制
    version: Mapped[str] = mapped_column(
        String(50),
        default="1.0.0",
        comment="配置版本"
    )
    
    is_active: Mapped[bool] = mapped_column(
        default=True,
        comment="是否启用"
    )
    
    is_default: Mapped[bool] = mapped_column(
        default=False,
        comment="是否默认配置"
    )
    
    # 有效期
    valid_from: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        comment="生效时间"
    )
    
    valid_until: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        comment="失效时间"
    )
    
    created_by: Mapped[Optional[str]] = mapped_column(
        String(100),
        comment="创建者"
    )


class EvaluationResult(BaseModel):
    """
    评估结果数据模型
    
    存储详细的评估结果和分析数据：
    - 方案评分和排序
    - 指标贡献度分析
    - 敏感性和不确定性分析
    """
    
    __tablename__ = "evaluation_results"
    
    # 关联任务
    task_id: Mapped[str] = mapped_column(
        UUID(as_uuid=False),
        ForeignKey("evaluation_tasks.id"),
        nullable=False,
        comment="关联任务ID"
    )
    
    # 方案信息
    scheme_id: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        comment="方案ID"
    )
    
    scheme_name: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        comment="方案名称"
    )
    
    # 评分结果
    total_score: Mapped[float] = mapped_column(
        Float,
        nullable=False,
        comment="总评分"
    )
    
    normalized_score: Mapped[float] = mapped_column(
        Float,
        nullable=False,
        comment="标准化评分"
    )
    
    ranking_position: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        comment="排名位置"
    )
    
    # 分类评分
    category_scores: Mapped[Dict[str, float]] = mapped_column(
        JSON,
        nullable=False,
        comment="分类评分"
    )
    
    indicator_scores: Mapped[Dict[str, float]] = mapped_column(
        JSON,
        nullable=False,
        comment="指标评分"
    )
    
    # 贡献度分析
    indicator_contributions: Mapped[Dict[str, float]] = mapped_column(
        JSON,
        nullable=False,
        comment="指标贡献度"
    )
    
    strength_indicators: Mapped[List[str]] = mapped_column(
        JSON,
        nullable=False,
        default=list,
        comment="优势指标列表"
    )
    
    weakness_indicators: Mapped[List[str]] = mapped_column(
        JSON,
        nullable=False,
        default=list,
        comment="劣势指标列表"
    )
    
    # 比较分析
    relative_performance: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        comment="相对性能分析"
    )
    
    gap_analysis: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        comment="差距分析"
    )
    
    # 敏感性分析
    weight_sensitivity: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        comment="权重敏感性分析"
    )
    
    parameter_sensitivity: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        comment="参数敏感性分析"
    )
    
    # 不确定性分析
    uncertainty_bounds: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        comment="不确定性边界"
    )
    
    confidence_intervals: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        comment="置信区间"
    )
    
    # 关联关系
    task: Mapped["EvaluationTask"] = relationship(
        "EvaluationTask",
        back_populates="results"
    )


# 在EvaluationTask中添加反向关系
EvaluationTask.results = relationship(
    "EvaluationResult",
    back_populates="task",
    cascade="all, delete-orphan"
)
