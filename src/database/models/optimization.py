"""
优化任务数据模型

遵循base-rules.md规范：
- 类文档字符串规则：说明类的用途、主要功能
- 使用描述性的类和属性命名
- 模块职责单一性：只包含优化任务相关的数据模型
"""

from datetime import datetime
from enum import Enum
from typing import Optional, Dict, Any, List
from decimal import Decimal

from sqlalchemy import String, Text, Integer, JSON, Enum as SQLEnum, DateTime
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy import ForeignKey

from src.database.models.base import BaseModel


class OptimizationTaskTypeEnum(str, Enum):
    """优化任务类型枚举"""
    PATH_PLANNING = "path_planning"           # 路径规划
    RESOURCE_SCHEDULING = "resource_scheduling"  # 资源调度
    MULTI_OBJECTIVE = "multi_objective"       # 多目标优化
    ROBUST_OPTIMIZATION = "robust_optimization"  # 鲁棒优化
    COMPREHENSIVE_EVALUATION = "comprehensive_evaluation"  # 综合评估


class OptimizationStatusEnum(str, Enum):
    """优化任务状态枚举"""
    PENDING = "pending"           # 待执行
    RUNNING = "running"           # 执行中
    COMPLETED = "completed"       # 已完成
    FAILED = "failed"            # 执行失败
    CANCELLED = "cancelled"       # 已取消


class AlgorithmTypeEnum(str, Enum):
    """算法类型枚举"""
    GENETIC_ALGORITHM = "genetic_algorithm"       # 遗传算法
    SIMULATED_ANNEALING = "simulated_annealing"   # 模拟退火
    TABU_SEARCH = "tabu_search"                   # 禁忌搜索
    PARTICLE_SWARM = "particle_swarm"             # 粒子群优化
    NSGA2 = "nsga2"                              # NSGA-II算法
    NSGA3 = "nsga3"                              # NSGA-III算法
    SPEA2 = "spea2"                              # SPEA2算法
    MOEAD = "moead"                              # MOEA/D算法
    DIJKSTRA = "dijkstra"                        # Dijkstra算法
    A_STAR = "a_star"                            # A*算法


class OptimizationTask(BaseModel):
    """
    优化任务数据模型
    
    存储各类优化任务的基本信息和执行状态：
    - 任务基本信息（名称、类型、描述）
    - 算法配置和参数
    - 执行状态和进度
    - 结果数据和性能指标
    """
    
    __tablename__ = "optimization_tasks"
    
    # 任务基本信息
    task_name: Mapped[str] = mapped_column(
        String(255), 
        nullable=False,
        comment="任务名称"
    )
    
    task_description: Mapped[Optional[str]] = mapped_column(
        Text,
        comment="任务描述"
    )
    
    task_type: Mapped[OptimizationTaskTypeEnum] = mapped_column(
        SQLEnum(OptimizationTaskTypeEnum),
        nullable=False,
        comment="任务类型"
    )
    
    # 算法配置
    algorithm_type: Mapped[AlgorithmTypeEnum] = mapped_column(
        SQLEnum(AlgorithmTypeEnum),
        nullable=False,
        comment="算法类型"
    )
    
    algorithm_config: Mapped[Dict[str, Any]] = mapped_column(
        JSON,
        nullable=False,
        default=dict,
        comment="算法配置参数"
    )
    
    # 问题定义
    problem_definition: Mapped[Dict[str, Any]] = mapped_column(
        JSON,
        nullable=False,
        default=dict,
        comment="问题定义和约束条件"
    )
    
    # 执行状态
    status: Mapped[OptimizationStatusEnum] = mapped_column(
        SQLEnum(OptimizationStatusEnum),
        default=OptimizationStatusEnum.PENDING,
        comment="任务状态"
    )
    
    progress_percentage: Mapped[int] = mapped_column(
        Integer,
        default=0,
        comment="执行进度百分比"
    )
    
    # 执行时间
    started_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        comment="开始执行时间"
    )
    
    completed_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        comment="完成时间"
    )
    
    execution_duration: Mapped[Optional[int]] = mapped_column(
        Integer,
        comment="执行时长（秒）"
    )
    
    # 结果数据
    result_data: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        comment="优化结果数据"
    )
    
    performance_metrics: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        comment="性能指标"
    )
    
    # 错误信息
    error_message: Mapped[Optional[str]] = mapped_column(
        Text,
        comment="错误信息"
    )
    
    error_details: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        comment="错误详情"
    )
    
    # 创建者信息
    created_by: Mapped[Optional[str]] = mapped_column(
        String(100),
        comment="创建者"
    )
    
    # 关联的场景ID（可选）
    scenario_id: Mapped[Optional[str]] = mapped_column(
        UUID(as_uuid=False),
        ForeignKey("scenarios.id"),
        comment="关联场景ID"
    )


class AlgorithmConfiguration(BaseModel):
    """
    算法配置数据模型
    
    存储各类算法的配置模板和参数设置：
    - 算法基本信息
    - 默认参数配置
    - 适用问题类型
    - 性能基准数据
    """
    
    __tablename__ = "algorithm_configurations"
    
    # 算法基本信息
    algorithm_name: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        comment="算法名称"
    )
    
    algorithm_type: Mapped[AlgorithmTypeEnum] = mapped_column(
        SQLEnum(AlgorithmTypeEnum),
        nullable=False,
        comment="算法类型"
    )
    
    algorithm_description: Mapped[Optional[str]] = mapped_column(
        Text,
        comment="算法描述"
    )
    
    # 参数配置
    default_parameters: Mapped[Dict[str, Any]] = mapped_column(
        JSON,
        nullable=False,
        default=dict,
        comment="默认参数配置"
    )
    
    parameter_ranges: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        comment="参数取值范围"
    )
    
    # 适用性信息
    applicable_problem_types: Mapped[List[str]] = mapped_column(
        JSON,
        nullable=False,
        default=list,
        comment="适用问题类型"
    )
    
    problem_size_limits: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        comment="问题规模限制"
    )
    
    # 性能基准
    performance_benchmarks: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        comment="性能基准数据"
    )
    
    # 状态信息
    is_active: Mapped[bool] = mapped_column(
        default=True,
        comment="是否启用"
    )
    
    version: Mapped[str] = mapped_column(
        String(50),
        default="1.0.0",
        comment="算法版本"
    )
    
    created_by: Mapped[Optional[str]] = mapped_column(
        String(100),
        comment="创建者"
    )


class OptimizationResult(BaseModel):
    """
    优化结果数据模型
    
    存储优化算法的详细执行结果：
    - 最优解和目标函数值
    - 算法收敛过程数据
    - 解的质量评估指标
    - 计算资源使用情况
    """
    
    __tablename__ = "optimization_results"
    
    # 关联任务
    task_id: Mapped[str] = mapped_column(
        UUID(as_uuid=False),
        ForeignKey("optimization_tasks.id"),
        nullable=False,
        comment="关联任务ID"
    )
    
    # 解的信息
    solution_data: Mapped[Dict[str, Any]] = mapped_column(
        JSON,
        nullable=False,
        comment="解的详细数据"
    )
    
    objective_values: Mapped[Dict[str, float]] = mapped_column(
        JSON,
        nullable=False,
        comment="目标函数值"
    )
    
    constraint_violations: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        comment="约束违反情况"
    )
    
    # 算法执行过程
    convergence_data: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        comment="收敛过程数据"
    )
    
    iteration_count: Mapped[Optional[int]] = mapped_column(
        Integer,
        comment="迭代次数"
    )
    
    # 质量指标
    solution_quality: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        comment="解的质量指标"
    )
    
    # 资源使用
    computation_time: Mapped[Optional[float]] = mapped_column(
        comment="计算时间（秒）"
    )
    
    memory_usage: Mapped[Optional[float]] = mapped_column(
        comment="内存使用量（MB）"
    )
    
    cpu_usage: Mapped[Optional[float]] = mapped_column(
        comment="CPU使用率"
    )
    
    # 关联关系
    task: Mapped["OptimizationTask"] = relationship(
        "OptimizationTask",
        back_populates="results"
    )


# 在OptimizationTask中添加反向关系
OptimizationTask.results = relationship(
    "OptimizationResult",
    back_populates="task",
    cascade="all, delete-orphan"
)
