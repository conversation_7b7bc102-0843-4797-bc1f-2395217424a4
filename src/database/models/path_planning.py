"""
路径规划数据模型

遵循base-rules.md规范：
- 类文档字符串规则：说明类的用途、主要功能
- 使用描述性的类和属性命名
- 模块职责单一性：只包含路径规划相关的数据模型
"""

from datetime import datetime
from enum import Enum
from typing import Optional, Dict, Any, List
from decimal import Decimal

from sqlalchemy import String, Text, Integer, JSON, Enum as SQLEnum, DateTime, Float
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy import ForeignKey

from src.database.models.base import BaseModel


class PathPlanningStatusEnum(str, Enum):
    """路径规划状态枚举"""
    PENDING = "pending"           # 待规划
    PLANNING = "planning"         # 规划中
    COMPLETED = "completed"       # 已完成
    FAILED = "failed"            # 规划失败
    CANCELLED = "cancelled"       # 已取消


class PathTypeEnum(str, Enum):
    """路径类型枚举"""
    WAREHOUSE_INTERNAL = "warehouse_internal"     # 仓库内路径
    GROUND_TRANSPORT = "ground_transport"         # 地面运输路径
    APRON_OPERATION = "apron_operation"          # 停机坪作业路径
    INTEGRATED_PATH = "integrated_path"          # 综合路径


class OptimizationObjectiveEnum(str, Enum):
    """优化目标枚举"""
    SHORTEST_DISTANCE = "shortest_distance"       # 最短距离
    MINIMUM_TIME = "minimum_time"                # 最短时间
    LOWEST_COST = "lowest_cost"                  # 最低成本
    HIGHEST_SAFETY = "highest_safety"            # 最高安全性
    BALANCED_MULTI = "balanced_multi"            # 多目标平衡


class PathPlanningTask(BaseModel):
    """
    路径规划任务数据模型
    
    存储路径规划任务的详细信息：
    - 起点、终点和路径点信息
    - 约束条件和优化目标
    - 规划结果和备选方案
    - 性能指标和分析数据
    """
    
    __tablename__ = "path_planning_tasks"
    
    # 任务基本信息
    task_name: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        comment="任务名称"
    )
    
    task_description: Mapped[Optional[str]] = mapped_column(
        Text,
        comment="任务描述"
    )
    
    path_type: Mapped[PathTypeEnum] = mapped_column(
        SQLEnum(PathTypeEnum),
        nullable=False,
        comment="路径类型"
    )
    
    # 路径点信息
    start_point: Mapped[Dict[str, Any]] = mapped_column(
        JSON,
        nullable=False,
        comment="起点信息（坐标、属性等）"
    )
    
    end_point: Mapped[Dict[str, Any]] = mapped_column(
        JSON,
        nullable=False,
        comment="终点信息（坐标、属性等）"
    )
    
    waypoints: Mapped[Optional[List[Dict[str, Any]]]] = mapped_column(
        JSON,
        comment="中间路径点列表"
    )
    
    # 约束条件
    constraints: Mapped[Dict[str, Any]] = mapped_column(
        JSON,
        nullable=False,
        default=dict,
        comment="约束条件（安全距离、时间窗口、设备能力等）"
    )
    
    # 优化目标
    optimization_objectives: Mapped[List[OptimizationObjectiveEnum]] = mapped_column(
        JSON,
        nullable=False,
        comment="优化目标列表"
    )
    
    objective_weights: Mapped[Optional[Dict[str, float]]] = mapped_column(
        JSON,
        comment="目标权重配置"
    )
    
    # 算法配置
    algorithm_type: Mapped[str] = mapped_column(
        String(50),
        nullable=False,
        comment="使用的路径规划算法"
    )
    
    algorithm_parameters: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        comment="算法参数配置"
    )
    
    # 执行状态
    status: Mapped[PathPlanningStatusEnum] = mapped_column(
        SQLEnum(PathPlanningStatusEnum),
        default=PathPlanningStatusEnum.PENDING,
        comment="规划状态"
    )
    
    progress_percentage: Mapped[int] = mapped_column(
        Integer,
        default=0,
        comment="规划进度百分比"
    )
    
    # 执行时间
    started_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        comment="开始规划时间"
    )
    
    completed_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        comment="完成时间"
    )
    
    planning_duration: Mapped[Optional[int]] = mapped_column(
        Integer,
        comment="规划耗时（秒）"
    )
    
    # 规划结果
    optimal_path: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        comment="最优路径方案"
    )
    
    alternative_paths: Mapped[Optional[List[Dict[str, Any]]]] = mapped_column(
        JSON,
        comment="备选路径方案列表"
    )
    
    path_analysis: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        comment="路径分析结果（距离、时间、成本等）"
    )
    
    # 性能指标
    performance_metrics: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        comment="性能指标（算法收敛性、计算效率等）"
    )
    
    # 错误信息
    error_message: Mapped[Optional[str]] = mapped_column(
        Text,
        comment="错误信息"
    )
    
    error_details: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        comment="错误详情"
    )
    
    # 创建者信息
    created_by: Mapped[Optional[str]] = mapped_column(
        String(100),
        comment="创建者"
    )
    
    # 关联的优化任务ID
    optimization_task_id: Mapped[Optional[str]] = mapped_column(
        UUID(as_uuid=False),
        ForeignKey("optimization_tasks.id"),
        comment="关联的优化任务ID"
    )


class WorkAreaTopology(BaseModel):
    """
    作业区域拓扑数据模型
    
    存储作业区域的拓扑结构信息：
    - 节点信息（仓库、停机坪、中转点）
    - 路径信息（距离、宽度、承载能力）
    - 区域属性和动态状态
    """
    
    __tablename__ = "work_area_topology"
    
    # 区域基本信息
    area_name: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        comment="区域名称"
    )
    
    area_type: Mapped[str] = mapped_column(
        String(50),
        nullable=False,
        comment="区域类型（warehouse/apron/transport）"
    )
    
    area_description: Mapped[Optional[str]] = mapped_column(
        Text,
        comment="区域描述"
    )
    
    # 拓扑结构
    nodes: Mapped[List[Dict[str, Any]]] = mapped_column(
        JSON,
        nullable=False,
        default=list,
        comment="节点信息列表"
    )
    
    edges: Mapped[List[Dict[str, Any]]] = mapped_column(
        JSON,
        nullable=False,
        default=list,
        comment="边信息列表（路径连接）"
    )
    
    # 区域属性
    area_properties: Mapped[Dict[str, Any]] = mapped_column(
        JSON,
        nullable=False,
        default=dict,
        comment="区域属性（安全等级、通行能力等）"
    )
    
    # 动态信息
    current_status: Mapped[Dict[str, Any]] = mapped_column(
        JSON,
        nullable=False,
        default=dict,
        comment="当前状态（实时占用、临时限制等）"
    )
    
    # 几何信息
    boundary_coordinates: Mapped[Optional[List[List[float]]]] = mapped_column(
        JSON,
        comment="区域边界坐标"
    )
    
    center_coordinates: Mapped[Optional[List[float]]] = mapped_column(
        JSON,
        comment="区域中心坐标"
    )
    
    # 版本控制
    version: Mapped[str] = mapped_column(
        String(50),
        default="1.0.0",
        comment="拓扑版本"
    )
    
    is_active: Mapped[bool] = mapped_column(
        default=True,
        comment="是否启用"
    )
    
    created_by: Mapped[Optional[str]] = mapped_column(
        String(100),
        comment="创建者"
    )


class PathPlanningResult(BaseModel):
    """
    路径规划结果数据模型
    
    存储路径规划的详细结果数据：
    - 路径详细信息和分析
    - 关键节点和时间安排
    - 资源需求和约束满足情况
    """
    
    __tablename__ = "path_planning_results"
    
    # 关联任务
    task_id: Mapped[str] = mapped_column(
        UUID(as_uuid=False),
        ForeignKey("path_planning_tasks.id"),
        nullable=False,
        comment="关联任务ID"
    )
    
    # 路径信息
    path_sequence: Mapped[List[Dict[str, Any]]] = mapped_column(
        JSON,
        nullable=False,
        comment="路径序列（节点顺序和详细信息）"
    )
    
    total_distance: Mapped[float] = mapped_column(
        Float,
        nullable=False,
        comment="总距离（米）"
    )
    
    estimated_time: Mapped[float] = mapped_column(
        Float,
        nullable=False,
        comment="预计时间（分钟）"
    )
    
    estimated_cost: Mapped[Optional[float]] = mapped_column(
        Float,
        comment="预计成本"
    )
    
    # 关键节点
    critical_points: Mapped[Optional[List[Dict[str, Any]]]] = mapped_column(
        JSON,
        comment="关键节点信息"
    )
    
    bottleneck_analysis: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        comment="瓶颈分析结果"
    )
    
    # 约束满足情况
    constraint_satisfaction: Mapped[Dict[str, Any]] = mapped_column(
        JSON,
        nullable=False,
        default=dict,
        comment="约束满足情况"
    )
    
    # 风险评估
    risk_assessment: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        comment="风险评估结果"
    )
    
    safety_score: Mapped[Optional[float]] = mapped_column(
        Float,
        comment="安全评分"
    )
    
    # 资源需求
    resource_requirements: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        comment="资源需求分析"
    )
    
    # 关联关系
    task: Mapped["PathPlanningTask"] = relationship(
        "PathPlanningTask",
        back_populates="results"
    )


# 在PathPlanningTask中添加反向关系
PathPlanningTask.results = relationship(
    "PathPlanningResult",
    back_populates="task",
    cascade="all, delete-orphan"
)
