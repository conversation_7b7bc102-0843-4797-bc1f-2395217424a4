"""
多目标优化数据模型

遵循base-rules.md规范：
- 类文档字符串规则：说明类的用途、主要功能
- 使用描述性的类和属性命名
- 模块职责单一性：只包含多目标优化相关的数据模型
"""

from datetime import datetime
from enum import Enum
from typing import Optional, Dict, Any, List
from decimal import Decimal

from sqlalchemy import String, Text, Integer, JSON, Enum as SQLEnum, DateTime, Float
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy import ForeignKey

from src.database.models.base import BaseModel


class MultiObjectiveStatusEnum(str, Enum):
    """多目标优化状态枚举"""
    PENDING = "pending"           # 待优化
    OPTIMIZING = "optimizing"     # 优化中
    COMPLETED = "completed"       # 已完成
    FAILED = "failed"            # 优化失败
    CANCELLED = "cancelled"       # 已取消


class ObjectiveFunctionTypeEnum(str, Enum):
    """目标函数类型枚举"""
    MINIMIZE = "minimize"         # 最小化
    MAXIMIZE = "maximize"         # 最大化


class MultiObjectiveAlgorithmEnum(str, Enum):
    """多目标优化算法枚举"""
    NSGA2 = "nsga2"              # NSGA-II算法
    NSGA3 = "nsga3"              # NSGA-III算法
    SPEA2 = "spea2"              # SPEA2算法
    MOEAD = "moead"              # MOEA/D算法
    PAES = "paes"                # PAES算法


class MultiObjectiveTask(BaseModel):
    """
    多目标优化任务数据模型
    
    存储多目标优化任务的详细信息：
    - 目标函数定义和权重
    - 决策变量和约束条件
    - 算法配置和参数
    - 帕累托解集和分析结果
    """
    
    __tablename__ = "multi_objective_tasks"
    
    # 任务基本信息
    task_name: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        comment="任务名称"
    )
    
    task_description: Mapped[Optional[str]] = mapped_column(
        Text,
        comment="任务描述"
    )
    
    # 目标函数定义
    objective_functions: Mapped[List[Dict[str, Any]]] = mapped_column(
        JSON,
        nullable=False,
        comment="目标函数定义列表"
    )
    
    objective_weights: Mapped[Optional[Dict[str, float]]] = mapped_column(
        JSON,
        comment="目标函数权重"
    )
    
    # 决策变量
    decision_variables: Mapped[List[Dict[str, Any]]] = mapped_column(
        JSON,
        nullable=False,
        comment="决策变量定义"
    )
    
    variable_bounds: Mapped[Dict[str, Any]] = mapped_column(
        JSON,
        nullable=False,
        comment="变量取值范围"
    )
    
    # 约束条件
    constraints: Mapped[List[Dict[str, Any]]] = mapped_column(
        JSON,
        nullable=False,
        default=list,
        comment="约束条件列表"
    )
    
    # 算法配置
    algorithm_type: Mapped[MultiObjectiveAlgorithmEnum] = mapped_column(
        SQLEnum(MultiObjectiveAlgorithmEnum),
        nullable=False,
        comment="多目标优化算法"
    )
    
    algorithm_parameters: Mapped[Dict[str, Any]] = mapped_column(
        JSON,
        nullable=False,
        default=dict,
        comment="算法参数配置"
    )
    
    # 执行配置
    population_size: Mapped[int] = mapped_column(
        Integer,
        default=100,
        comment="种群大小"
    )
    
    max_generations: Mapped[int] = mapped_column(
        Integer,
        default=1000,
        comment="最大迭代代数"
    )
    
    termination_criteria: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        comment="终止条件"
    )
    
    # 执行状态
    status: Mapped[MultiObjectiveStatusEnum] = mapped_column(
        SQLEnum(MultiObjectiveStatusEnum),
        default=MultiObjectiveStatusEnum.PENDING,
        comment="优化状态"
    )
    
    current_generation: Mapped[int] = mapped_column(
        Integer,
        default=0,
        comment="当前迭代代数"
    )
    
    progress_percentage: Mapped[int] = mapped_column(
        Integer,
        default=0,
        comment="优化进度百分比"
    )
    
    # 执行时间
    started_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        comment="开始优化时间"
    )
    
    completed_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        comment="完成时间"
    )
    
    optimization_duration: Mapped[Optional[int]] = mapped_column(
        Integer,
        comment="优化耗时（秒）"
    )
    
    # 结果数据
    pareto_solutions: Mapped[Optional[List[Dict[str, Any]]]] = mapped_column(
        JSON,
        comment="帕累托最优解集"
    )
    
    convergence_data: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        comment="收敛过程数据"
    )
    
    quality_indicators: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        comment="解集质量指标"
    )
    
    # 性能指标
    hypervolume: Mapped[Optional[float]] = mapped_column(
        Float,
        comment="超体积指标"
    )
    
    spacing: Mapped[Optional[float]] = mapped_column(
        Float,
        comment="间距指标"
    )
    
    spread: Mapped[Optional[float]] = mapped_column(
        Float,
        comment="分布性指标"
    )
    
    # 错误信息
    error_message: Mapped[Optional[str]] = mapped_column(
        Text,
        comment="错误信息"
    )
    
    error_details: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        comment="错误详情"
    )
    
    # 创建者信息
    created_by: Mapped[Optional[str]] = mapped_column(
        String(100),
        comment="创建者"
    )
    
    # 关联的优化任务ID
    optimization_task_id: Mapped[Optional[str]] = mapped_column(
        UUID(as_uuid=False),
        ForeignKey("optimization_tasks.id"),
        comment="关联的优化任务ID"
    )


class ParetoSolution(BaseModel):
    """
    帕累托解数据模型
    
    存储帕累托最优解的详细信息：
    - 决策变量值和目标函数值
    - 解的质量评估和排序
    - 支配关系和拥挤距离
    """
    
    __tablename__ = "pareto_solutions"
    
    # 关联任务
    task_id: Mapped[str] = mapped_column(
        UUID(as_uuid=False),
        ForeignKey("multi_objective_tasks.id"),
        nullable=False,
        comment="关联任务ID"
    )
    
    # 解的标识
    solution_id: Mapped[str] = mapped_column(
        String(100),
        nullable=False,
        comment="解的唯一标识"
    )
    
    # 决策变量值
    decision_variables: Mapped[Dict[str, Any]] = mapped_column(
        JSON,
        nullable=False,
        comment="决策变量值"
    )
    
    # 目标函数值
    objective_values: Mapped[List[float]] = mapped_column(
        JSON,
        nullable=False,
        comment="目标函数值列表"
    )
    
    # 约束违反情况
    constraint_violations: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        comment="约束违反情况"
    )
    
    # 解的质量指标
    dominance_rank: Mapped[Optional[int]] = mapped_column(
        Integer,
        comment="支配等级"
    )
    
    crowding_distance: Mapped[Optional[float]] = mapped_column(
        Float,
        comment="拥挤距离"
    )
    
    # 解的评估
    feasibility: Mapped[bool] = mapped_column(
        default=True,
        comment="是否可行"
    )
    
    quality_score: Mapped[Optional[float]] = mapped_column(
        Float,
        comment="质量评分"
    )
    
    # 生成信息
    generation: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        comment="生成代数"
    )
    
    parent_solutions: Mapped[Optional[List[str]]] = mapped_column(
        JSON,
        comment="父解ID列表"
    )
    
    # 关联关系
    task: Mapped["MultiObjectiveTask"] = relationship(
        "MultiObjectiveTask",
        back_populates="solutions"
    )


class ObjectiveFunction(BaseModel):
    """
    目标函数定义数据模型
    
    存储目标函数的详细定义：
    - 函数表达式和参数
    - 优化方向和权重
    - 函数特性和约束
    """
    
    __tablename__ = "objective_functions"
    
    # 函数基本信息
    function_name: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        comment="函数名称"
    )
    
    function_description: Mapped[Optional[str]] = mapped_column(
        Text,
        comment="函数描述"
    )
    
    function_type: Mapped[ObjectiveFunctionTypeEnum] = mapped_column(
        SQLEnum(ObjectiveFunctionTypeEnum),
        nullable=False,
        comment="优化方向"
    )
    
    # 函数定义
    function_expression: Mapped[str] = mapped_column(
        Text,
        nullable=False,
        comment="函数表达式"
    )
    
    function_parameters: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        comment="函数参数"
    )
    
    # 函数属性
    is_linear: Mapped[bool] = mapped_column(
        default=False,
        comment="是否线性函数"
    )
    
    is_convex: Mapped[bool] = mapped_column(
        default=False,
        comment="是否凸函数"
    )
    
    is_differentiable: Mapped[bool] = mapped_column(
        default=True,
        comment="是否可微"
    )
    
    # 取值范围
    value_range: Mapped[Optional[Dict[str, float]]] = mapped_column(
        JSON,
        comment="函数值范围"
    )
    
    # 权重和优先级
    default_weight: Mapped[float] = mapped_column(
        Float,
        default=1.0,
        comment="默认权重"
    )
    
    priority_level: Mapped[int] = mapped_column(
        Integer,
        default=1,
        comment="优先级"
    )
    
    # 状态信息
    is_active: Mapped[bool] = mapped_column(
        default=True,
        comment="是否启用"
    )
    
    created_by: Mapped[Optional[str]] = mapped_column(
        String(100),
        comment="创建者"
    )


# 在MultiObjectiveTask中添加反向关系
MultiObjectiveTask.solutions = relationship(
    "ParetoSolution",
    back_populates="task",
    cascade="all, delete-orphan"
)
