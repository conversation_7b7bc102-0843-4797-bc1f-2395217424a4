"""
高级资源调度API接口

遵循base-rules.md规范：
- 函数复杂度控制，每个函数圈复杂度不超过10
- 函数参数不超过5个
- 使用描述性的函数命名
"""

from typing import List, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession

from src.api.dependencies import get_db_session
from src.services.advanced_scheduling import AdvancedSchedulingService
from src.schemas.optimization import (
    OptimizationTaskCreateSchema,
    OptimizationTaskResponseSchema,
    OptimizationResultSchema,
    OptimizationExecutionRequestSchema,
    OptimizationExecutionResponseSchema
)

router = APIRouter(prefix="/advanced-scheduling", tags=["高级资源调度"])
scheduling_service = AdvancedSchedulingService()


@router.post("/tasks",
             response_model=Dict[str, str],
             status_code=status.HTTP_201_CREATED,
             summary="创建高级调度任务")
async def create_scheduling_task(
    task_data: OptimizationTaskCreateSchema,
    session: AsyncSession = Depends(get_db_session)
) -> Dict[str, str]:
    """
    创建新的高级资源调度任务
    
    Args:
        task_data: 任务创建数据
        session: 数据库会话
        
    Returns:
        Dict[str, str]: 包含任务ID的响应
        
    Raises:
        HTTPException: 创建失败时抛出异常
    """
    try:
        task_id = await scheduling_service.create_scheduling_task(
            session=session,
            task_data=task_data.dict()
        )
        
        return {
            "task_id": task_id,
            "message": "高级调度任务创建成功",
            "status": "created"
        }
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"任务创建失败: {str(e)}"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"服务器内部错误: {str(e)}"
        )


@router.post("/tasks/{task_id}/execute",
             response_model=OptimizationExecutionResponseSchema,
             summary="执行高级调度优化")
async def execute_scheduling_optimization(
    task_id: str,
    background_tasks: BackgroundTasks,
    execution_request: OptimizationExecutionRequestSchema,
    session: AsyncSession = Depends(get_db_session)
) -> OptimizationExecutionResponseSchema:
    """
    执行指定的高级调度优化任务
    
    Args:
        task_id: 任务ID
        background_tasks: 后台任务
        execution_request: 执行请求参数
        session: 数据库会话
        
    Returns:
        OptimizationExecutionResponseSchema: 执行响应
        
    Raises:
        HTTPException: 执行失败时抛出异常
    """
    try:
        # 验证任务ID匹配
        if execution_request.task_id != task_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="请求中的任务ID与URL中的任务ID不匹配"
            )
        
        # 添加后台任务执行调度优化
        background_tasks.add_task(
            scheduling_service.execute_scheduling_optimization,
            session,
            task_id
        )
        
        return OptimizationExecutionResponseSchema(
            task_id=task_id,
            execution_status="started",
            message="高级调度优化任务已开始执行",
            estimated_completion_time=None
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"启动调度优化失败: {str(e)}"
        )


@router.get("/algorithms",
            response_model=List[Dict[str, Any]],
            summary="获取可用调度算法列表")
async def get_scheduling_algorithms() -> List[Dict[str, Any]]:
    """
    获取可用的高级调度算法列表
    
    Returns:
        List[Dict[str, Any]]: 算法列表
    """
    algorithms = [
        {
            "algorithm_type": "genetic_algorithm",
            "algorithm_name": "遗传算法",
            "description": "基于自然选择和遗传机制的进化算法，适用于复杂调度问题",
            "suitable_for": ["大规模调度", "多约束优化", "非线性问题"],
            "parameters": {
                "population_size": {"type": "int", "default": 50, "range": [20, 200]},
                "crossover_rate": {"type": "float", "default": 0.8, "range": [0.5, 1.0]},
                "mutation_rate": {"type": "float", "default": 0.1, "range": [0.01, 0.3]},
                "selection_method": {"type": "str", "default": "tournament", "options": ["tournament", "roulette", "rank"]},
                "encoding_type": {"type": "str", "default": "real", "options": ["binary", "real", "permutation"]}
            },
            "performance": {
                "convergence_speed": "medium",
                "solution_quality": "high",
                "computational_complexity": "medium"
            }
        },
        {
            "algorithm_type": "particle_swarm",
            "algorithm_name": "粒子群优化算法",
            "description": "模拟鸟群觅食行为的群体智能优化算法",
            "suitable_for": ["连续优化", "参数调优", "快速收敛需求"],
            "parameters": {
                "population_size": {"type": "int", "default": 30, "range": [10, 100]},
                "inertia_weight": {"type": "float", "default": 0.9, "range": [0.1, 1.0]},
                "cognitive_coefficient": {"type": "float", "default": 2.0, "range": [0.5, 4.0]},
                "social_coefficient": {"type": "float", "default": 2.0, "range": [0.5, 4.0]},
                "max_velocity": {"type": "float", "default": None, "description": "最大速度限制"}
            },
            "performance": {
                "convergence_speed": "fast",
                "solution_quality": "medium",
                "computational_complexity": "low"
            }
        },
        {
            "algorithm_type": "simulated_annealing",
            "algorithm_name": "模拟退火算法",
            "description": "模拟金属退火过程的局部搜索算法，能跳出局部最优",
            "suitable_for": ["组合优化", "离散问题", "局部搜索改进"],
            "parameters": {
                "initial_temperature": {"type": "float", "default": 1000.0, "range": [100, 10000]},
                "final_temperature": {"type": "float", "default": 0.01, "range": [0.001, 1.0]},
                "cooling_rate": {"type": "float", "default": 0.95, "range": [0.8, 0.99]},
                "cooling_schedule": {"type": "str", "default": "exponential", "options": ["exponential", "linear", "logarithmic", "adaptive"]},
                "markov_chain_length": {"type": "int", "default": 100, "range": [50, 500]}
            },
            "performance": {
                "convergence_speed": "slow",
                "solution_quality": "high",
                "computational_complexity": "low"
            }
        }
    ]
    
    return algorithms


@router.get("/problem-templates",
            response_model=List[Dict[str, Any]],
            summary="获取调度问题模板")
async def get_scheduling_problem_templates() -> List[Dict[str, Any]]:
    """
    获取预定义的调度问题模板
    
    Returns:
        List[Dict[str, Any]]: 问题模板列表
    """
    templates = [
        {
            "template_id": "job_shop_scheduling",
            "template_name": "作业车间调度问题",
            "description": "经典的作业车间调度问题，多个作业在多台机器上加工",
            "problem_structure": {
                "resources": [
                    {"id": "machine_1", "name": "机器1", "capacity": 1, "efficiency": 1.0, "cost_per_hour": 10.0},
                    {"id": "machine_2", "name": "机器2", "capacity": 1, "efficiency": 1.2, "cost_per_hour": 12.0},
                    {"id": "machine_3", "name": "机器3", "capacity": 1, "efficiency": 0.8, "cost_per_hour": 8.0}
                ],
                "tasks": [
                    {"id": "job_1", "name": "作业1", "duration": 5.0, "priority": 1},
                    {"id": "job_2", "name": "作业2", "duration": 3.0, "priority": 2},
                    {"id": "job_3", "name": "作业3", "duration": 7.0, "priority": 1}
                ],
                "constraints": [
                    {"type": "resource_capacity", "description": "每台机器同时只能处理一个作业"},
                    {"type": "task_dependency", "description": "某些作业之间存在先后顺序"}
                ]
            },
            "optimization_objectives": ["minimize_makespan", "minimize_total_cost", "maximize_resource_utilization"]
        },
        {
            "template_id": "vehicle_routing",
            "template_name": "车辆路径问题",
            "description": "多辆车服务多个客户的路径优化问题",
            "problem_structure": {
                "resources": [
                    {"id": "vehicle_1", "name": "车辆1", "capacity": 100, "cost_per_km": 2.0},
                    {"id": "vehicle_2", "name": "车辆2", "capacity": 150, "cost_per_km": 2.5}
                ],
                "tasks": [
                    {"id": "customer_1", "name": "客户1", "demand": 30, "location": [10, 20]},
                    {"id": "customer_2", "name": "客户2", "demand": 50, "location": [30, 40]},
                    {"id": "customer_3", "name": "客户3", "demand": 25, "location": [50, 10]}
                ],
                "constraints": [
                    {"type": "vehicle_capacity", "description": "车辆载重限制"},
                    {"type": "time_window", "description": "客户服务时间窗口"}
                ]
            },
            "optimization_objectives": ["minimize_total_distance", "minimize_number_of_vehicles", "minimize_total_time"]
        },
        {
            "template_id": "resource_allocation",
            "template_name": "资源分配问题",
            "description": "有限资源在多个项目间的最优分配",
            "problem_structure": {
                "resources": [
                    {"id": "budget", "name": "预算", "total_amount": 1000000, "unit": "元"},
                    {"id": "personnel", "name": "人员", "total_amount": 50, "unit": "人"},
                    {"id": "equipment", "name": "设备", "total_amount": 20, "unit": "台"}
                ],
                "tasks": [
                    {"id": "project_1", "name": "项目1", "expected_return": 150000, "resource_requirements": {"budget": 200000, "personnel": 10, "equipment": 5}},
                    {"id": "project_2", "name": "项目2", "expected_return": 200000, "resource_requirements": {"budget": 300000, "personnel": 15, "equipment": 8}},
                    {"id": "project_3", "name": "项目3", "expected_return": 120000, "resource_requirements": {"budget": 150000, "personnel": 8, "equipment": 3}}
                ],
                "constraints": [
                    {"type": "resource_limit", "description": "资源总量限制"},
                    {"type": "project_dependency", "description": "项目间的依赖关系"}
                ]
            },
            "optimization_objectives": ["maximize_total_return", "minimize_resource_waste", "balance_risk"]
        }
    ]
    
    return templates


@router.post("/validate-problem",
             response_model=Dict[str, Any],
             summary="验证调度问题定义")
async def validate_scheduling_problem(
    problem_definition: Dict[str, Any]
) -> Dict[str, Any]:
    """
    验证调度问题定义的正确性
    
    Args:
        problem_definition: 问题定义
        
    Returns:
        Dict[str, Any]: 验证结果
        
    Raises:
        HTTPException: 验证失败时抛出异常
    """
    try:
        validation_result = {
            "is_valid": True,
            "errors": [],
            "warnings": [],
            "suggestions": []
        }
        
        # 验证必需字段
        required_fields = ["resources", "tasks", "constraints"]
        for field in required_fields:
            if field not in problem_definition:
                validation_result["errors"].append(f"缺少必需字段: {field}")
                validation_result["is_valid"] = False
        
        # 验证资源定义
        if "resources" in problem_definition:
            resources = problem_definition["resources"]
            if not isinstance(resources, list) or len(resources) == 0:
                validation_result["errors"].append("资源列表不能为空")
                validation_result["is_valid"] = False
            else:
                for i, resource in enumerate(resources):
                    if "id" not in resource:
                        validation_result["errors"].append(f"资源{i}缺少id字段")
                        validation_result["is_valid"] = False
        
        # 验证任务定义
        if "tasks" in problem_definition:
            tasks = problem_definition["tasks"]
            if not isinstance(tasks, list) or len(tasks) == 0:
                validation_result["errors"].append("任务列表不能为空")
                validation_result["is_valid"] = False
            else:
                for i, task in enumerate(tasks):
                    if "id" not in task:
                        validation_result["errors"].append(f"任务{i}缺少id字段")
                        validation_result["is_valid"] = False
        
        # 添加建议
        if validation_result["is_valid"]:
            validation_result["suggestions"].append("问题定义格式正确，建议进行小规模测试")
            
            # 检查问题规模
            num_resources = len(problem_definition.get("resources", []))
            num_tasks = len(problem_definition.get("tasks", []))
            
            if num_resources * num_tasks > 1000:
                validation_result["warnings"].append("问题规模较大，建议使用高性能算法")
            
            if num_resources < num_tasks / 10:
                validation_result["warnings"].append("资源数量相对较少，可能存在资源瓶颈")
        
        return validation_result
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"验证过程中发生错误: {str(e)}"
        )
