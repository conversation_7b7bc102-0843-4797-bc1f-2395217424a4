"""
多目标优化API接口

遵循base-rules.md规范：
- 函数复杂度控制，每个函数圈复杂度不超过10
- 函数参数不超过5个
- 使用描述性的函数命名
"""

from typing import List, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession

from src.api.dependencies import get_db_session
from src.services.multi_objective_optimization import MultiObjectiveOptimizationService
from src.schemas.multi_objective import (
    MultiObjectiveTaskCreateSchema,
    MultiObjectiveTaskResponseSchema,
    MultiObjectiveExecutionRequestSchema,
    MultiObjectiveExecutionResponseSchema,
    ParetoFrontAnalysisResponseSchema
)

router = APIRouter(prefix="/multi-objective", tags=["多目标优化"])
mo_service = MultiObjectiveOptimizationService()


@router.post("/tasks",
             response_model=Dict[str, str],
             status_code=status.HTTP_201_CREATED,
             summary="创建多目标优化任务")
async def create_multi_objective_task(
    task_data: MultiObjectiveTaskCreateSchema,
    session: AsyncSession = Depends(get_db_session)
) -> Dict[str, str]:
    """
    创建新的多目标优化任务
    
    Args:
        task_data: 任务创建数据
        session: 数据库会话
        
    Returns:
        Dict[str, str]: 包含任务ID的响应
        
    Raises:
        HTTPException: 创建失败时抛出异常
    """
    try:
        task_id = await mo_service.create_multi_objective_task(
            session=session,
            task_data=task_data.dict()
        )
        
        return {
            "task_id": task_id,
            "message": "多目标优化任务创建成功",
            "status": "created"
        }
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"任务创建失败: {str(e)}"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"服务器内部错误: {str(e)}"
        )


@router.post("/tasks/{task_id}/execute",
             response_model=MultiObjectiveExecutionResponseSchema,
             summary="执行多目标优化")
async def execute_multi_objective_optimization(
    task_id: str,
    background_tasks: BackgroundTasks,
    execution_request: MultiObjectiveExecutionRequestSchema,
    session: AsyncSession = Depends(get_db_session)
) -> MultiObjectiveExecutionResponseSchema:
    """
    执行指定的多目标优化任务
    
    Args:
        task_id: 任务ID
        background_tasks: 后台任务
        execution_request: 执行请求参数
        session: 数据库会话
        
    Returns:
        MultiObjectiveExecutionResponseSchema: 执行响应
        
    Raises:
        HTTPException: 执行失败时抛出异常
    """
    try:
        # 验证任务ID匹配
        if execution_request.task_id != task_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="请求中的任务ID与URL中的任务ID不匹配"
            )
        
        # 添加后台任务执行多目标优化
        background_tasks.add_task(
            mo_service.execute_multi_objective_optimization,
            session,
            task_id
        )
        
        return MultiObjectiveExecutionResponseSchema(
            task_id=task_id,
            execution_status="started",
            message="多目标优化任务已开始执行",
            estimated_completion_time=None
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"启动多目标优化失败: {str(e)}"
        )


@router.get("/algorithms",
            response_model=List[Dict[str, Any]],
            summary="获取可用多目标优化算法列表")
async def get_multi_objective_algorithms() -> List[Dict[str, Any]]:
    """
    获取可用的多目标优化算法列表
    
    Returns:
        List[Dict[str, Any]]: 算法列表
    """
    algorithms = [
        {
            "algorithm_type": "nsga2",
            "algorithm_name": "NSGA-II",
            "description": "非支配排序遗传算法II，经典的多目标进化算法",
            "suitable_for": ["2-3个目标", "连续优化", "混合整数优化"],
            "features": ["非支配排序", "拥挤距离", "精英保留", "帕累托前沿"],
            "parameters": {
                "population_size": {"type": "int", "default": 100, "range": [50, 500]},
                "max_generations": {"type": "int", "default": 1000, "range": [100, 5000]},
                "crossover_rate": {"type": "float", "default": 0.9, "range": [0.5, 1.0]},
                "mutation_rate": {"type": "float", "default": 0.1, "range": [0.01, 0.5]},
                "tournament_size": {"type": "int", "default": 2, "range": [2, 10]}
            },
            "performance": {
                "convergence_speed": "medium",
                "diversity_maintenance": "good",
                "scalability": "medium"
            }
        },
        {
            "algorithm_type": "nsga3",
            "algorithm_name": "NSGA-III",
            "description": "非支配排序遗传算法III，适用于多目标优化（3个以上目标）",
            "suitable_for": ["3个以上目标", "高维目标空间", "参考点导向"],
            "features": ["参考点机制", "归一化", "关联操作", "生态位保持"],
            "parameters": {
                "population_size": {"type": "int", "default": 100, "range": [50, 500]},
                "max_generations": {"type": "int", "default": 1000, "range": [100, 5000]},
                "crossover_rate": {"type": "float", "default": 0.9, "range": [0.5, 1.0]},
                "mutation_rate": {"type": "float", "default": 0.1, "range": [0.01, 0.5]},
                "reference_points": {"type": "array", "description": "参考点集合"}
            },
            "performance": {
                "convergence_speed": "medium",
                "diversity_maintenance": "excellent",
                "scalability": "high"
            }
        },
        {
            "algorithm_type": "spea2",
            "algorithm_name": "SPEA2",
            "description": "强度帕累托进化算法2，基于强度值的多目标优化算法",
            "suitable_for": ["2-4个目标", "档案机制", "密度估计"],
            "features": ["强度值计算", "档案维护", "密度估计", "截断操作"],
            "parameters": {
                "population_size": {"type": "int", "default": 100, "range": [50, 500]},
                "archive_size": {"type": "int", "default": 100, "range": [50, 500]},
                "max_generations": {"type": "int", "default": 1000, "range": [100, 5000]},
                "crossover_rate": {"type": "float", "default": 0.9, "range": [0.5, 1.0]},
                "mutation_rate": {"type": "float", "default": 0.1, "range": [0.01, 0.5]}
            },
            "performance": {
                "convergence_speed": "good",
                "diversity_maintenance": "good",
                "scalability": "medium"
            }
        },
        {
            "algorithm_type": "moead",
            "algorithm_name": "MOEA/D",
            "description": "基于分解的多目标进化算法，将多目标问题分解为多个单目标子问题",
            "suitable_for": ["任意数量目标", "分解策略", "邻域协作"],
            "features": ["问题分解", "权重向量", "邻域结构", "协作进化"],
            "parameters": {
                "population_size": {"type": "int", "default": 100, "range": [50, 500]},
                "max_generations": {"type": "int", "default": 1000, "range": [100, 5000]},
                "neighborhood_size": {"type": "int", "default": 20, "range": [5, 50]},
                "crossover_rate": {"type": "float", "default": 0.9, "range": [0.5, 1.0]},
                "mutation_rate": {"type": "float", "default": 0.1, "range": [0.01, 0.5]},
                "decomposition_method": {"type": "str", "default": "tchebycheff", "options": ["tchebycheff", "weighted_sum", "pbi"]}
            },
            "performance": {
                "convergence_speed": "fast",
                "diversity_maintenance": "excellent",
                "scalability": "high"
            }
        }
    ]
    
    return algorithms


@router.get("/objective-functions",
            response_model=List[Dict[str, Any]],
            summary="获取预定义目标函数")
async def get_predefined_objective_functions() -> List[Dict[str, Any]]:
    """
    获取预定义的目标函数列表
    
    Returns:
        List[Dict[str, Any]]: 目标函数列表
    """
    objective_functions = [
        {
            "function_id": "sphere",
            "function_name": "球面函数",
            "description": "经典的单峰函数，全局最优解在原点",
            "expression": "sum(x**2 for x in genes)",
            "type": "minimize",
            "characteristics": ["单峰", "可分离", "连续可微"],
            "optimal_value": 0.0,
            "search_domain": "[-5.12, 5.12]^n"
        },
        {
            "function_id": "rosenbrock",
            "function_name": "Rosenbrock函数",
            "description": "经典的多峰函数，具有狭窄的弯曲山谷",
            "expression": "sum(100*(genes[i+1] - genes[i]**2)**2 + (1 - genes[i])**2 for i in range(len(genes)-1))",
            "type": "minimize",
            "characteristics": ["多峰", "不可分离", "狭窄山谷"],
            "optimal_value": 0.0,
            "search_domain": "[-2.048, 2.048]^n"
        },
        {
            "function_id": "rastrigin",
            "function_name": "Rastrigin函数",
            "description": "高度多峰函数，具有大量局部最优解",
            "expression": "10*len(genes) + sum(x**2 - 10*cos(2*pi*x) for x in genes)",
            "type": "minimize",
            "characteristics": ["高度多峰", "可分离", "大量局部最优"],
            "optimal_value": 0.0,
            "search_domain": "[-5.12, 5.12]^n"
        },
        {
            "function_id": "ackley",
            "function_name": "Ackley函数",
            "description": "多峰函数，具有指数和三角函数特性",
            "expression": "-20*exp(-0.2*sqrt(sum(x**2 for x in genes)/len(genes))) - exp(sum(cos(2*pi*x) for x in genes)/len(genes)) + 20 + e",
            "type": "minimize",
            "characteristics": ["多峰", "不可分离", "指数衰减"],
            "optimal_value": 0.0,
            "search_domain": "[-32.768, 32.768]^n"
        },
        {
            "function_id": "zdt1",
            "function_name": "ZDT1测试函数",
            "description": "经典的双目标测试函数，凸帕累托前沿",
            "objectives": [
                {"expression": "genes[0]", "type": "minimize"},
                {"expression": "g * (1 - sqrt(genes[0]/g)) where g = 1 + 9*sum(genes[1:])/len(genes[1:])", "type": "minimize"}
            ],
            "characteristics": ["凸前沿", "连续", "单峰"],
            "pareto_front": "convex",
            "search_domain": "[0, 1]^n"
        },
        {
            "function_id": "zdt2",
            "function_name": "ZDT2测试函数",
            "description": "经典的双目标测试函数，凹帕累托前沿",
            "objectives": [
                {"expression": "genes[0]", "type": "minimize"},
                {"expression": "g * (1 - (genes[0]/g)**2) where g = 1 + 9*sum(genes[1:])/len(genes[1:])", "type": "minimize"}
            ],
            "characteristics": ["凹前沿", "连续", "单峰"],
            "pareto_front": "concave",
            "search_domain": "[0, 1]^n"
        }
    ]
    
    return objective_functions


@router.get("/quality-indicators",
            response_model=List[Dict[str, Any]],
            summary="获取质量指标说明")
async def get_quality_indicators() -> List[Dict[str, Any]]:
    """
    获取多目标优化质量指标的说明
    
    Returns:
        List[Dict[str, Any]]: 质量指标列表
    """
    indicators = [
        {
            "indicator_name": "hypervolume",
            "indicator_description": "超体积指标，衡量帕累托前沿覆盖的目标空间体积",
            "calculation_method": "计算帕累托前沿与参考点围成的超体积",
            "interpretation": {
                "higher_is_better": True,
                "meaning": "值越大表示帕累托前沿质量越好",
                "range": "[0, +∞)"
            },
            "advantages": ["单调性", "帕累托兼容", "综合性强"],
            "disadvantages": ["计算复杂度高", "需要参考点", "高维困难"],
            "suitable_for": ["2-4个目标", "综合评估", "算法比较"]
        },
        {
            "indicator_name": "spacing",
            "indicator_description": "间距指标，衡量帕累托前沿解的分布均匀性",
            "calculation_method": "计算相邻解之间距离的标准差",
            "interpretation": {
                "higher_is_better": False,
                "meaning": "值越小表示解分布越均匀",
                "range": "[0, +∞)"
            },
            "advantages": ["计算简单", "直观易懂", "维度无关"],
            "disadvantages": ["不考虑收敛性", "对异常值敏感"],
            "suitable_for": ["分布性评估", "均匀性检查", "任意维度"]
        },
        {
            "indicator_name": "spread",
            "indicator_description": "分布性指标，衡量帕累托前沿的扩展程度",
            "calculation_method": "计算前沿端点距离和解间距离的综合度量",
            "interpretation": {
                "higher_is_better": False,
                "meaning": "值越小表示分布越好",
                "range": "[0, +∞)"
            },
            "advantages": ["考虑端点", "分布全面", "标准化"],
            "disadvantages": ["计算复杂", "参数敏感"],
            "suitable_for": ["分布评估", "覆盖性检查", "2-3个目标"]
        },
        {
            "indicator_name": "igd",
            "indicator_description": "反向世代距离，衡量帕累托前沿与真实前沿的距离",
            "calculation_method": "计算真实前沿点到近似前沿的平均最小距离",
            "interpretation": {
                "higher_is_better": False,
                "meaning": "值越小表示前沿质量越好",
                "range": "[0, +∞)"
            },
            "advantages": ["收敛性和分布性", "标准测试", "理论基础"],
            "disadvantages": ["需要真实前沿", "计算复杂"],
            "suitable_for": ["算法评估", "基准测试", "理论研究"]
        },
        {
            "indicator_name": "gd",
            "indicator_description": "世代距离，衡量近似前沿与真实前沿的距离",
            "calculation_method": "计算近似前沿点到真实前沿的平均最小距离",
            "interpretation": {
                "higher_is_better": False,
                "meaning": "值越小表示收敛性越好",
                "range": "[0, +∞)"
            },
            "advantages": ["收敛性度量", "计算简单", "直观理解"],
            "disadvantages": ["不考虑分布性", "需要真实前沿"],
            "suitable_for": ["收敛性评估", "算法比较", "理论分析"]
        }
    ]
    
    return indicators


@router.post("/analyze-pareto-front",
             response_model=Dict[str, Any],
             summary="分析帕累托前沿")
async def analyze_pareto_front(
    pareto_data: Dict[str, Any]
) -> Dict[str, Any]:
    """
    分析帕累托前沿的质量和特性
    
    Args:
        pareto_data: 帕累托前沿数据
        
    Returns:
        Dict[str, Any]: 分析结果
        
    Raises:
        HTTPException: 分析失败时抛出异常
    """
    try:
        import numpy as np
        
        solutions = pareto_data.get("solutions", [])
        if not solutions:
            raise ValueError("帕累托前沿数据为空")
        
        # 提取目标值
        objectives = []
        for solution in solutions:
            if "objectives" in solution:
                objectives.append(solution["objectives"])
            else:
                raise ValueError("解缺少目标值信息")
        
        objectives_array = np.array(objectives)
        num_objectives = objectives_array.shape[1]
        num_solutions = objectives_array.shape[0]
        
        # 计算基本统计信息
        obj_stats = {}
        for i in range(num_objectives):
            obj_values = objectives_array[:, i]
            obj_stats[f"objective_{i}"] = {
                "min": float(np.min(obj_values)),
                "max": float(np.max(obj_values)),
                "mean": float(np.mean(obj_values)),
                "std": float(np.std(obj_values)),
                "range": float(np.max(obj_values) - np.min(obj_values))
            }
        
        # 计算质量指标
        quality_indicators = {}
        
        # 简化的超体积计算（仅支持2D）
        if num_objectives == 2:
            ref_point = np.max(objectives_array, axis=0) + 1
            sorted_indices = np.argsort(objectives_array[:, 0])
            sorted_objectives = objectives_array[sorted_indices]
            
            hypervolume = 0.0
            for i in range(len(sorted_objectives)):
                if i == 0:
                    width = ref_point[0] - sorted_objectives[i, 0]
                else:
                    width = sorted_objectives[i-1, 0] - sorted_objectives[i, 0]
                height = ref_point[1] - sorted_objectives[i, 1]
                hypervolume += width * height
            
            quality_indicators["hypervolume"] = float(hypervolume)
        
        # 计算间距指标
        distances = []
        for i in range(num_solutions):
            min_distance = float('inf')
            for j in range(num_solutions):
                if i != j:
                    distance = np.linalg.norm(objectives_array[i] - objectives_array[j])
                    min_distance = min(min_distance, distance)
            distances.append(min_distance)
        
        spacing = float(np.std(distances))
        quality_indicators["spacing"] = spacing
        
        # 计算分布性指标
        ranges = np.max(objectives_array, axis=0) - np.min(objectives_array, axis=0)
        spread = float(np.mean(ranges))
        quality_indicators["spread"] = spread
        
        # 前沿特性分析
        front_characteristics = {
            "num_solutions": num_solutions,
            "num_objectives": num_objectives,
            "is_convex": None,  # 需要更复杂的算法判断
            "is_connected": None,  # 需要更复杂的算法判断
            "coverage": {
                "objective_ranges": [float(r) for r in ranges],
                "total_coverage": float(np.prod(ranges))
            }
        }
        
        return {
            "pareto_front_analysis": {
                "basic_statistics": obj_stats,
                "quality_indicators": quality_indicators,
                "front_characteristics": front_characteristics,
                "recommendations": [
                    "建议增加种群大小以提高前沿密度" if num_solutions < 50 else "前沿解数量适中",
                    "建议调整算法参数以改善分布性" if spacing > 1.0 else "解分布较为均匀",
                    "前沿覆盖范围良好" if spread > 0.1 else "建议扩大搜索范围"
                ]
            }
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"帕累托前沿分析失败: {str(e)}"
        )
