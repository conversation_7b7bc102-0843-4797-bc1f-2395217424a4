"""
路径规划API接口

遵循base-rules.md规范：
- 函数复杂度控制，每个函数圈复杂度不超过10
- 函数参数不超过5个
- 使用描述性的函数命名
"""

from typing import List, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession

from src.api.dependencies import get_db_session
from src.services.path_planning import PathPlanningService
from src.schemas.path_planning import (
    PathPlanningTaskCreateSchema,
    PathPlanningTaskUpdateSchema,
    PathPlanningTaskResponseSchema,
    PathPlanningResultSchema,
    WorkAreaTopologyCreateSchema,
    WorkAreaTopologyResponseSchema,
    PathPlanningExecutionRequestSchema,
    PathPlanningExecutionResponseSchema,
    PathComparisonRequestSchema,
    PathComparisonResponseSchema
)

router = APIRouter(prefix="/path-planning", tags=["路径规划"])
path_planning_service = PathPlanningService()


@router.post("/tasks", 
             response_model=Dict[str, str],
             status_code=status.HTTP_201_CREATED,
             summary="创建路径规划任务")
async def create_planning_task(
    task_data: PathPlanningTaskCreateSchema,
    session: AsyncSession = Depends(get_db_session)
) -> Dict[str, str]:
    """
    创建新的路径规划任务
    
    Args:
        task_data: 任务创建数据
        session: 数据库会话
        
    Returns:
        Dict[str, str]: 包含任务ID的响应
        
    Raises:
        HTTPException: 创建失败时抛出异常
    """
    try:
        task_id = await path_planning_service.create_planning_task(
            session=session,
            task_data=task_data.dict()
        )
        
        return {
            "task_id": task_id,
            "message": "路径规划任务创建成功",
            "status": "created"
        }
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"任务创建失败: {str(e)}"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"服务器内部错误: {str(e)}"
        )


@router.get("/tasks/{task_id}",
            response_model=PathPlanningTaskResponseSchema,
            summary="获取路径规划任务详情")
async def get_planning_task(
    task_id: str,
    session: AsyncSession = Depends(get_db_session)
) -> PathPlanningTaskResponseSchema:
    """
    获取指定路径规划任务的详细信息
    
    Args:
        task_id: 任务ID
        session: 数据库会话
        
    Returns:
        PathPlanningTaskResponseSchema: 任务详情
        
    Raises:
        HTTPException: 任务不存在时抛出异常
    """
    try:
        task = await path_planning_service._get_task_by_id(session, task_id)
        
        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"任务不存在: {task_id}"
            )
        
        return PathPlanningTaskResponseSchema(
            task_id=task.id,
            task_name=task.task_name,
            task_description=task.task_description,
            path_type=task.path_type,
            start_point=task.start_point,
            end_point=task.end_point,
            waypoints=task.waypoints,
            optimization_objectives=task.optimization_objectives,
            algorithm_type=task.algorithm_type,
            status=task.status,
            progress_percentage=task.progress_percentage,
            started_at=task.started_at,
            completed_at=task.completed_at,
            planning_duration=task.planning_duration,
            optimal_path=task.optimal_path,
            performance_metrics=task.performance_metrics,
            error_message=task.error_message,
            created_by=task.created_by,
            created_at=task.created_at,
            updated_at=task.updated_at
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取任务信息失败: {str(e)}"
        )


@router.post("/tasks/{task_id}/execute",
             response_model=PathPlanningExecutionResponseSchema,
             summary="执行路径规划")
async def execute_planning_task(
    task_id: str,
    background_tasks: BackgroundTasks,
    execution_request: PathPlanningExecutionRequestSchema,
    session: AsyncSession = Depends(get_db_session)
) -> PathPlanningExecutionResponseSchema:
    """
    执行指定的路径规划任务
    
    Args:
        task_id: 任务ID
        background_tasks: 后台任务
        execution_request: 执行请求参数
        session: 数据库会话
        
    Returns:
        PathPlanningExecutionResponseSchema: 执行响应
        
    Raises:
        HTTPException: 执行失败时抛出异常
    """
    try:
        # 验证任务ID匹配
        if execution_request.task_id != task_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="请求中的任务ID与URL中的任务ID不匹配"
            )
        
        # 添加后台任务执行路径规划
        background_tasks.add_task(
            path_planning_service.execute_planning,
            session,
            task_id
        )
        
        return PathPlanningExecutionResponseSchema(
            task_id=task_id,
            execution_status="started",
            message="路径规划任务已开始执行",
            estimated_completion_time=None
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"启动路径规划失败: {str(e)}"
        )


@router.get("/tasks/{task_id}/results",
            response_model=List[PathPlanningResultSchema],
            summary="获取路径规划结果")
async def get_planning_results(
    task_id: str,
    session: AsyncSession = Depends(get_db_session)
) -> List[PathPlanningResultSchema]:
    """
    获取指定任务的路径规划结果
    
    Args:
        task_id: 任务ID
        session: 数据库会话
        
    Returns:
        List[PathPlanningResultSchema]: 规划结果列表
        
    Raises:
        HTTPException: 获取失败时抛出异常
    """
    try:
        # 这里需要实现获取结果的逻辑
        # 暂时返回空列表，后续完善
        return []
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取规划结果失败: {str(e)}"
        )


@router.post("/topology",
             response_model=Dict[str, str],
             status_code=status.HTTP_201_CREATED,
             summary="创建作业区域拓扑")
async def create_work_area_topology(
    topology_data: WorkAreaTopologyCreateSchema,
    session: AsyncSession = Depends(get_db_session)
) -> Dict[str, str]:
    """
    创建新的作业区域拓扑数据
    
    Args:
        topology_data: 拓扑创建数据
        session: 数据库会话
        
    Returns:
        Dict[str, str]: 包含拓扑ID的响应
        
    Raises:
        HTTPException: 创建失败时抛出异常
    """
    try:
        # 这里需要实现创建拓扑的逻辑
        # 暂时返回模拟响应，后续完善
        return {
            "topology_id": "mock_topology_id",
            "message": "作业区域拓扑创建成功",
            "status": "created"
        }
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"拓扑创建失败: {str(e)}"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"服务器内部错误: {str(e)}"
        )


@router.get("/topology",
            response_model=List[WorkAreaTopologyResponseSchema],
            summary="获取作业区域拓扑列表")
async def get_work_area_topologies(
    area_type: str = None,
    is_active: bool = True,
    session: AsyncSession = Depends(get_db_session)
) -> List[WorkAreaTopologyResponseSchema]:
    """
    获取作业区域拓扑列表
    
    Args:
        area_type: 区域类型过滤
        is_active: 是否只获取活跃的拓扑
        session: 数据库会话
        
    Returns:
        List[WorkAreaTopologyResponseSchema]: 拓扑列表
        
    Raises:
        HTTPException: 获取失败时抛出异常
    """
    try:
        # 这里需要实现获取拓扑列表的逻辑
        # 暂时返回空列表，后续完善
        return []
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取拓扑列表失败: {str(e)}"
        )


@router.post("/compare",
             response_model=PathComparisonResponseSchema,
             summary="比较路径方案")
async def compare_paths(
    comparison_request: PathComparisonRequestSchema,
    session: AsyncSession = Depends(get_db_session)
) -> PathComparisonResponseSchema:
    """
    比较多个路径规划方案
    
    Args:
        comparison_request: 比较请求参数
        session: 数据库会话
        
    Returns:
        PathComparisonResponseSchema: 比较结果
        
    Raises:
        HTTPException: 比较失败时抛出异常
    """
    try:
        # 这里需要实现路径比较的逻辑
        # 暂时返回模拟响应，后续完善
        from datetime import datetime
        
        return PathComparisonResponseSchema(
            comparison_id="mock_comparison_id",
            best_path_id=comparison_request.path_results[0],
            comparison_results=[],
            ranking=[],
            analysis_summary={},
            created_at=datetime.utcnow()
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"路径比较失败: {str(e)}"
        )


@router.get("/algorithms",
            response_model=List[Dict[str, Any]],
            summary="获取可用算法列表")
async def get_available_algorithms() -> List[Dict[str, Any]]:
    """
    获取可用的路径规划算法列表
    
    Returns:
        List[Dict[str, Any]]: 算法列表
    """
    algorithms = [
        {
            "algorithm_type": "dijkstra",
            "algorithm_name": "Dijkstra最短路径算法",
            "description": "经典的单源最短路径算法，保证找到最优解",
            "suitable_for": ["小规模图", "精确解需求"],
            "parameters": {
                "max_iterations": {"type": "int", "default": 1000, "description": "最大迭代次数"},
                "convergence_tolerance": {"type": "float", "default": 1e-6, "description": "收敛容差"}
            }
        },
        {
            "algorithm_type": "a_star",
            "algorithm_name": "A*启发式搜索算法",
            "description": "使用启发式函数的最短路径算法，搜索效率高",
            "suitable_for": ["中等规模图", "有明确目标的搜索"],
            "parameters": {
                "heuristic": {"type": "str", "default": "euclidean", "options": ["euclidean", "manhattan", "chebyshev"]},
                "max_iterations": {"type": "int", "default": 1000, "description": "最大迭代次数"}
            }
        },
        {
            "algorithm_type": "dynamic_programming",
            "algorithm_name": "动态规划算法",
            "description": "适用于多阶段决策的路径优化问题",
            "suitable_for": ["阶段性路径规划", "全局最优解需求"],
            "parameters": {
                "stages": {"type": "list", "description": "阶段划分"},
                "max_iterations": {"type": "int", "default": 1000, "description": "最大迭代次数"}
            }
        }
    ]
    
    return algorithms
