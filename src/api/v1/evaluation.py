"""
综合评估API接口

遵循base-rules.md规范：
- 函数复杂度控制，每个函数圈复杂度不超过10
- 函数参数不超过5个
- 使用描述性的函数命名
"""

from typing import List, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession

from src.api.dependencies import get_db_session
from src.services.comprehensive_evaluation import ComprehensiveEvaluationService
from src.schemas.evaluation import (
    EvaluationTaskCreateSchema,
    EvaluationTaskResponseSchema,
    WeightConfigurationCreateSchema,
    WeightConfigurationResponseSchema,
    EvaluationExecutionRequestSchema,
    EvaluationExecutionResponseSchema,
    SchemeComparisonRequestSchema,
    SchemeComparisonResponseSchema
)

router = APIRouter(prefix="/evaluation", tags=["综合评估"])
evaluation_service = ComprehensiveEvaluationService()


@router.post("/tasks",
             response_model=Dict[str, str],
             status_code=status.HTTP_201_CREATED,
             summary="创建综合评估任务")
async def create_evaluation_task(
    task_data: EvaluationTaskCreateSchema,
    session: AsyncSession = Depends(get_db_session)
) -> Dict[str, str]:
    """
    创建新的综合评估任务
    
    Args:
        task_data: 任务创建数据
        session: 数据库会话
        
    Returns:
        Dict[str, str]: 包含任务ID的响应
        
    Raises:
        HTTPException: 创建失败时抛出异常
    """
    try:
        task_id = await evaluation_service.create_evaluation_task(
            session=session,
            task_data=task_data.dict()
        )
        
        return {
            "task_id": task_id,
            "message": "综合评估任务创建成功",
            "status": "created"
        }
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"任务创建失败: {str(e)}"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"服务器内部错误: {str(e)}"
        )


@router.get("/tasks/{task_id}",
            response_model=EvaluationTaskResponseSchema,
            summary="获取综合评估任务详情")
async def get_evaluation_task(
    task_id: str,
    session: AsyncSession = Depends(get_db_session)
) -> EvaluationTaskResponseSchema:
    """
    获取指定综合评估任务的详细信息
    
    Args:
        task_id: 任务ID
        session: 数据库会话
        
    Returns:
        EvaluationTaskResponseSchema: 任务详情
        
    Raises:
        HTTPException: 任务不存在时抛出异常
    """
    try:
        task = await evaluation_service._get_task_by_id(session, task_id)
        
        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"任务不存在: {task_id}"
            )
        
        # 构建方案排序数据
        scheme_ranking = []
        if task.scheme_ranking:
            scheme_ranking = [
                {
                    "rank": item["rank"],
                    "scheme_id": item["scheme_id"],
                    "scheme_name": item["scheme_name"],
                    "score": item["score"],
                    "normalized_score": item["normalized_score"]
                }
                for item in task.scheme_ranking
            ]
        
        return EvaluationTaskResponseSchema(
            task_id=task.id,
            task_name=task.task_name,
            task_description=task.task_description,
            scheme_count=len(task.scheme_ids),
            evaluation_method=task.evaluation_method,
            status=task.status,
            progress_percentage=task.progress_percentage,
            started_at=task.started_at,
            completed_at=task.completed_at,
            evaluation_duration=task.evaluation_duration,
            scheme_ranking=scheme_ranking,
            error_message=task.error_message,
            created_by=task.created_by,
            created_at=task.created_at,
            updated_at=task.updated_at
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取任务信息失败: {str(e)}"
        )


@router.post("/tasks/{task_id}/execute",
             response_model=EvaluationExecutionResponseSchema,
             summary="执行综合评估")
async def execute_evaluation_task(
    task_id: str,
    background_tasks: BackgroundTasks,
    execution_request: EvaluationExecutionRequestSchema,
    session: AsyncSession = Depends(get_db_session)
) -> EvaluationExecutionResponseSchema:
    """
    执行指定的综合评估任务
    
    Args:
        task_id: 任务ID
        background_tasks: 后台任务
        execution_request: 执行请求参数
        session: 数据库会话
        
    Returns:
        EvaluationExecutionResponseSchema: 执行响应
        
    Raises:
        HTTPException: 执行失败时抛出异常
    """
    try:
        # 验证任务ID匹配
        if execution_request.task_id != task_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="请求中的任务ID与URL中的任务ID不匹配"
            )
        
        # 添加后台任务执行综合评估
        background_tasks.add_task(
            evaluation_service.execute_evaluation,
            session,
            task_id
        )
        
        return EvaluationExecutionResponseSchema(
            task_id=task_id,
            execution_status="started",
            message="综合评估任务已开始执行",
            estimated_completion_time=None
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"启动综合评估失败: {str(e)}"
        )


@router.post("/weight-configurations",
             response_model=Dict[str, str],
             status_code=status.HTTP_201_CREATED,
             summary="创建权重配置")
async def create_weight_configuration(
    config_data: WeightConfigurationCreateSchema,
    session: AsyncSession = Depends(get_db_session)
) -> Dict[str, str]:
    """
    创建新的权重配置
    
    Args:
        config_data: 配置创建数据
        session: 数据库会话
        
    Returns:
        Dict[str, str]: 包含配置ID的响应
        
    Raises:
        HTTPException: 创建失败时抛出异常
    """
    try:
        # 这里需要实现创建权重配置的逻辑
        # 暂时返回模拟响应，后续完善
        return {
            "config_id": "mock_config_id",
            "message": "权重配置创建成功",
            "status": "created"
        }
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"配置创建失败: {str(e)}"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"服务器内部错误: {str(e)}"
        )


@router.get("/weight-configurations",
            response_model=List[WeightConfigurationResponseSchema],
            summary="获取权重配置列表")
async def get_weight_configurations(
    scenario_type: str = None,
    is_active: bool = True,
    session: AsyncSession = Depends(get_db_session)
) -> List[WeightConfigurationResponseSchema]:
    """
    获取权重配置列表
    
    Args:
        scenario_type: 场景类型过滤
        is_active: 是否只获取活跃的配置
        session: 数据库会话
        
    Returns:
        List[WeightConfigurationResponseSchema]: 配置列表
        
    Raises:
        HTTPException: 获取失败时抛出异常
    """
    try:
        # 这里需要实现获取权重配置列表的逻辑
        # 暂时返回空列表，后续完善
        return []
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取配置列表失败: {str(e)}"
        )


@router.post("/compare-schemes",
             response_model=SchemeComparisonResponseSchema,
             summary="比较方案")
async def compare_schemes(
    comparison_request: SchemeComparisonRequestSchema,
    session: AsyncSession = Depends(get_db_session)
) -> SchemeComparisonResponseSchema:
    """
    比较多个方案
    
    Args:
        comparison_request: 比较请求参数
        session: 数据库会话
        
    Returns:
        SchemeComparisonResponseSchema: 比较结果
        
    Raises:
        HTTPException: 比较失败时抛出异常
    """
    try:
        # 这里需要实现方案比较的逻辑
        # 暂时返回模拟响应，后续完善
        from datetime import datetime
        
        return SchemeComparisonResponseSchema(
            comparison_id="mock_comparison_id",
            scheme_ranking=[],
            pairwise_comparisons=[],
            dominance_analysis={},
            similarity_analysis={},
            trade_off_analysis={},
            recommendation={},
            created_at=datetime.utcnow()
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"方案比较失败: {str(e)}"
        )


@router.get("/methods",
            response_model=List[Dict[str, Any]],
            summary="获取可用评估方法列表")
async def get_evaluation_methods() -> List[Dict[str, Any]]:
    """
    获取可用的评估方法列表
    
    Returns:
        List[Dict[str, Any]]: 评估方法列表
    """
    methods = [
        {
            "method_type": "weighted_average",
            "method_name": "加权平均法",
            "description": "基于指标权重的线性加权平均评估方法",
            "suitable_for": ["多指标综合评估", "权重明确的场景"],
            "parameters": {
                "normalization": {"type": "str", "default": "min_max", "options": ["min_max", "z_score"]},
                "aggregation": {"type": "str", "default": "weighted_sum", "options": ["weighted_sum", "weighted_product"]}
            }
        },
        {
            "method_type": "fuzzy_evaluation",
            "method_name": "模糊综合评价法",
            "description": "处理模糊和不确定信息的评估方法",
            "suitable_for": ["定性指标较多", "不确定性较大的评估"],
            "parameters": {
                "membership_function": {"type": "str", "default": "triangular", "options": ["triangular", "trapezoidal"]},
                "defuzzification": {"type": "str", "default": "centroid", "options": ["centroid", "max_membership"]}
            }
        },
        {
            "method_type": "ahp",
            "method_name": "层次分析法",
            "description": "基于层次结构的多准则决策分析方法",
            "suitable_for": ["层次结构清晰", "专家判断可靠"],
            "parameters": {
                "consistency_threshold": {"type": "float", "default": 0.1, "description": "一致性检验阈值"},
                "hierarchy_levels": {"type": "int", "default": 3, "description": "层次数量"}
            }
        },
        {
            "method_type": "topsis",
            "method_name": "TOPSIS法",
            "description": "基于理想解的逼近排序方法",
            "suitable_for": ["多属性决策", "量化指标为主"],
            "parameters": {
                "distance_metric": {"type": "str", "default": "euclidean", "options": ["euclidean", "manhattan"]},
                "normalization": {"type": "str", "default": "vector", "options": ["vector", "linear"]}
            }
        },
        {
            "method_type": "grey_relational",
            "method_name": "灰色关联分析法",
            "description": "基于灰色系统理论的关联度分析方法",
            "suitable_for": ["小样本数据", "信息不完全的评估"],
            "parameters": {
                "resolution_coefficient": {"type": "float", "default": 0.5, "description": "分辨系数"},
                "reference_sequence": {"type": "str", "default": "max", "options": ["max", "min", "average"]}
            }
        }
    ]
    
    return methods
