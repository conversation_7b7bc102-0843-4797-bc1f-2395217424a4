"""
鲁棒优化API接口

遵循base-rules.md规范：
- 函数复杂度控制，每个函数圈复杂度不超过10
- 函数参数不超过5个
- 使用描述性的函数命名
"""

from typing import List, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession

from src.api.dependencies import get_db_session
from src.services.robust_optimization import RobustOptimizationService
from src.schemas.robust import (
    RobustOptimizationTaskCreateSchema,
    RobustOptimizationTaskResponseSchema,
    RobustOptimizationExecutionRequestSchema,
    RobustOptimizationExecutionResponseSchema,
    UncertaintyAnalysisResponseSchema
)

router = APIRouter(prefix="/robust-optimization", tags=["鲁棒优化"])
robust_service = RobustOptimizationService()


@router.post("/tasks",
             response_model=Dict[str, str],
             status_code=status.HTTP_201_CREATED,
             summary="创建鲁棒优化任务")
async def create_robust_optimization_task(
    task_data: RobustOptimizationTaskCreateSchema,
    session: AsyncSession = Depends(get_db_session)
) -> Dict[str, str]:
    """
    创建新的鲁棒优化任务
    
    Args:
        task_data: 任务创建数据
        session: 数据库会话
        
    Returns:
        Dict[str, str]: 包含任务ID的响应
        
    Raises:
        HTTPException: 创建失败时抛出异常
    """
    try:
        task_id = await robust_service.create_robust_optimization_task(
            session=session,
            task_data=task_data.dict()
        )
        
        return {
            "task_id": task_id,
            "message": "鲁棒优化任务创建成功",
            "status": "created"
        }
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"任务创建失败: {str(e)}"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"服务器内部错误: {str(e)}"
        )


@router.post("/tasks/{task_id}/execute",
             response_model=RobustOptimizationExecutionResponseSchema,
             summary="执行鲁棒优化")
async def execute_robust_optimization(
    task_id: str,
    background_tasks: BackgroundTasks,
    execution_request: RobustOptimizationExecutionRequestSchema,
    session: AsyncSession = Depends(get_db_session)
) -> RobustOptimizationExecutionResponseSchema:
    """
    执行指定的鲁棒优化任务
    
    Args:
        task_id: 任务ID
        background_tasks: 后台任务
        execution_request: 执行请求参数
        session: 数据库会话
        
    Returns:
        RobustOptimizationExecutionResponseSchema: 执行响应
        
    Raises:
        HTTPException: 执行失败时抛出异常
    """
    try:
        # 验证任务ID匹配
        if execution_request.task_id != task_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="请求中的任务ID与URL中的任务ID不匹配"
            )
        
        # 添加后台任务执行鲁棒优化
        background_tasks.add_task(
            robust_service.execute_robust_optimization,
            session,
            task_id,
            execution_request.optimization_method
        )
        
        return RobustOptimizationExecutionResponseSchema(
            task_id=task_id,
            execution_status="started",
            message="鲁棒优化任务已开始执行",
            optimization_method=execution_request.optimization_method,
            estimated_completion_time=None
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"启动鲁棒优化失败: {str(e)}"
        )


@router.get("/tasks/{task_id}/uncertainty-analysis",
            response_model=UncertaintyAnalysisResponseSchema,
            summary="获取不确定性分析结果")
async def get_uncertainty_analysis(
    task_id: str,
    session: AsyncSession = Depends(get_db_session)
) -> UncertaintyAnalysisResponseSchema:
    """
    获取指定任务的不确定性分析结果
    
    Args:
        task_id: 任务ID
        session: 数据库会话
        
    Returns:
        UncertaintyAnalysisResponseSchema: 不确定性分析结果
        
    Raises:
        HTTPException: 获取失败时抛出异常
    """
    try:
        analysis_result = await robust_service.get_uncertainty_analysis(session, task_id)
        
        if not analysis_result:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"任务不存在或尚未完成: {task_id}"
            )
        
        return UncertaintyAnalysisResponseSchema(**analysis_result)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取不确定性分析失败: {str(e)}"
        )


@router.get("/methods",
            response_model=List[Dict[str, Any]],
            summary="获取可用鲁棒优化方法列表")
async def get_robust_optimization_methods() -> List[Dict[str, Any]]:
    """
    获取可用的鲁棒优化方法列表
    
    Returns:
        List[Dict[str, Any]]: 方法列表
    """
    methods = [
        {
            "method_type": "worst_case",
            "method_name": "最坏情况鲁棒优化",
            "description": "基于最坏情况分析的鲁棒优化方法，保证在最不利情况下的性能",
            "suitable_for": ["保守决策", "风险规避", "安全关键系统"],
            "uncertainty_types": ["box", "ellipsoidal", "polyhedral"],
            "parameters": {
                "robustness_level": {"type": "float", "default": 0.1, "range": [0.01, 0.5], "description": "鲁棒性水平"},
                "uncertainty_budget": {"type": "float", "default": 1.0, "range": [0.1, 2.0], "description": "不确定性预算"},
                "max_inner_iterations": {"type": "int", "default": 100, "range": [50, 500], "description": "内层优化最大迭代次数"}
            },
            "advantages": ["保守性强", "理论保证", "适用于风险规避"],
            "disadvantages": ["可能过于保守", "计算复杂度高"]
        },
        {
            "method_type": "stochastic_programming",
            "method_name": "随机规划",
            "description": "基于概率分布的随机优化方法，考虑不确定参数的概率特性",
            "suitable_for": ["概率信息可用", "期望优化", "风险管理"],
            "uncertainty_types": ["normal", "uniform", "exponential", "custom"],
            "parameters": {
                "num_scenarios": {"type": "int", "default": 100, "range": [50, 1000], "description": "场景数量"},
                "confidence_level": {"type": "float", "default": 0.95, "range": [0.8, 0.99], "description": "置信水平"},
                "risk_measure": {"type": "str", "default": "expected_value", "options": ["expected_value", "var", "cvar"], "description": "风险度量"},
                "scenario_generation": {"type": "str", "default": "monte_carlo", "options": ["monte_carlo", "latin_hypercube"], "description": "场景生成方法"}
            },
            "advantages": ["考虑概率信息", "期望性能优化", "风险度量灵活"],
            "disadvantages": ["需要概率分布信息", "计算量大"]
        },
        {
            "method_type": "distributionally_robust",
            "method_name": "分布鲁棒优化",
            "description": "在分布不确定性下的鲁棒优化方法，不需要精确的概率分布",
            "suitable_for": ["分布不确定", "数据驱动决策", "机器学习应用"],
            "uncertainty_types": ["moment_based", "wasserstein", "phi_divergence"],
            "parameters": {
                "ambiguity_radius": {"type": "float", "default": 0.1, "range": [0.01, 1.0], "description": "模糊半径"},
                "reference_distribution": {"type": "str", "default": "empirical", "options": ["empirical", "uniform", "normal"], "description": "参考分布"},
                "divergence_type": {"type": "str", "default": "wasserstein", "options": ["wasserstein", "kl", "chi_square"], "description": "散度类型"}
            },
            "advantages": ["分布假设弱", "数据驱动", "理论基础强"],
            "disadvantages": ["理论复杂", "实现难度高"]
        }
    ]
    
    return methods


@router.get("/uncertainty-sets",
            response_model=List[Dict[str, Any]],
            summary="获取不确定性集合类型")
async def get_uncertainty_set_types() -> List[Dict[str, Any]]:
    """
    获取支持的不确定性集合类型
    
    Returns:
        List[Dict[str, Any]]: 不确定性集合类型列表
    """
    uncertainty_sets = [
        {
            "set_type": "box",
            "set_name": "盒式不确定性集合",
            "description": "每个不确定参数在独立的区间内变化",
            "mathematical_form": "U = {u : l_i ≤ u_i ≤ u_i, i = 1,...,n}",
            "parameters": {
                "lower_bounds": {"type": "array", "description": "下界向量"},
                "upper_bounds": {"type": "array", "description": "上界向量"}
            },
            "advantages": ["简单直观", "计算效率高", "易于理解"],
            "disadvantages": ["可能过于保守", "不考虑参数相关性"],
            "example": {
                "parameter_names": ["demand", "cost"],
                "nominal_values": [100, 1.0],
                "uncertainty_bounds": [(-10, 10), (-0.2, 0.2)]
            }
        },
        {
            "set_type": "ellipsoidal",
            "set_name": "椭球不确定性集合",
            "description": "不确定参数在椭球区域内变化，可以考虑参数间的相关性",
            "mathematical_form": "U = {u : (u-μ)ᵀΣ⁻¹(u-μ) ≤ ρ²}",
            "parameters": {
                "center": {"type": "array", "description": "椭球中心"},
                "covariance_matrix": {"type": "matrix", "description": "协方差矩阵"},
                "radius": {"type": "float", "description": "椭球半径"}
            },
            "advantages": ["考虑相关性", "几何意义清晰", "理论性质好"],
            "disadvantages": ["参数估计复杂", "计算相对复杂"],
            "example": {
                "parameter_names": ["demand", "cost"],
                "center": [100, 1.0],
                "covariance_matrix": [[25, 2], [2, 0.04]],
                "radius": 1.0
            }
        },
        {
            "set_type": "polyhedral",
            "set_name": "多面体不确定性集合",
            "description": "由线性不等式约束定义的多面体区域",
            "mathematical_form": "U = {u : Au ≤ b}",
            "parameters": {
                "constraint_matrix": {"type": "matrix", "description": "约束矩阵A"},
                "constraint_vector": {"type": "array", "description": "约束向量b"}
            },
            "advantages": ["灵活性高", "可表达复杂约束", "线性规划可解"],
            "disadvantages": ["定义复杂", "可能非凸"],
            "example": {
                "parameter_names": ["demand", "cost"],
                "constraints": [
                    {"coefficients": [1, 0], "bound": 110, "type": "<="},
                    {"coefficients": [-1, 0], "bound": -90, "type": "<="},
                    {"coefficients": [0, 1], "bound": 1.2, "type": "<="},
                    {"coefficients": [0, -1], "bound": -0.8, "type": "<="}
                ]
            }
        }
    ]
    
    return uncertainty_sets


@router.post("/generate-scenarios",
             response_model=Dict[str, Any],
             summary="生成随机场景")
async def generate_scenarios(
    scenario_config: Dict[str, Any]
) -> Dict[str, Any]:
    """
    根据配置生成随机场景
    
    Args:
        scenario_config: 场景生成配置
        
    Returns:
        Dict[str, Any]: 生成的场景数据
        
    Raises:
        HTTPException: 生成失败时抛出异常
    """
    try:
        import numpy as np
        
        num_scenarios = scenario_config.get("num_scenarios", 100)
        parameters = scenario_config.get("parameters", {})
        generation_method = scenario_config.get("method", "monte_carlo")
        
        scenarios = []
        
        for i in range(num_scenarios):
            scenario = {"scenario_id": f"scenario_{i}", "parameters": {}}
            
            for param_name, param_config in parameters.items():
                dist_type = param_config.get("distribution", "normal")
                
                if dist_type == "normal":
                    mean = param_config.get("mean", 0.0)
                    std = param_config.get("std", 1.0)
                    value = np.random.normal(mean, std)
                elif dist_type == "uniform":
                    low = param_config.get("low", 0.0)
                    high = param_config.get("high", 1.0)
                    value = np.random.uniform(low, high)
                elif dist_type == "exponential":
                    scale = param_config.get("scale", 1.0)
                    value = np.random.exponential(scale)
                else:
                    value = param_config.get("default", 1.0)
                
                scenario["parameters"][param_name] = float(value)
            
            scenarios.append(scenario)
        
        return {
            "scenarios": scenarios,
            "num_scenarios": len(scenarios),
            "generation_method": generation_method,
            "statistics": {
                param_name: {
                    "mean": np.mean([s["parameters"][param_name] for s in scenarios]),
                    "std": np.std([s["parameters"][param_name] for s in scenarios]),
                    "min": np.min([s["parameters"][param_name] for s in scenarios]),
                    "max": np.max([s["parameters"][param_name] for s in scenarios])
                }
                for param_name in parameters.keys()
            }
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"场景生成失败: {str(e)}"
        )
