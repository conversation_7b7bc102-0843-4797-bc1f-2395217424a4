"""
API v1路由汇总

遵循base-rules.md规范：
- 模块职责单一性：只负责路由汇总
- 使用描述性的函数命名
"""

from fastapi import APIRouter
from src.api.v1.scenarios import router as scenarios_router
from src.api.v1.equipment import router as equipment_router
from src.api.v1.aircraft import router as aircraft_router
from src.api.v1.vehicle import router as vehicle_router
from src.api.v1.configuration import router as configuration_router
from src.api.v1.efficiency import router as efficiency_router
from src.api.v1.loading_efficiency import router as loading_efficiency_router
from src.api.v1.path_planning import router as path_planning_router
from src.api.v1.evaluation import router as evaluation_router
from src.api.v1.advanced_scheduling import router as advanced_scheduling_router
from src.api.v1.robust_optimization import router as robust_optimization_router
from src.api.v1.multi_objective import router as multi_objective_router

# 创建v1版本的主路由器
v1_router = APIRouter(prefix="/v1")

# 注册各模块路由
v1_router.include_router(scenarios_router)
v1_router.include_router(equipment_router)
v1_router.include_router(aircraft_router)
v1_router.include_router(vehicle_router)
v1_router.include_router(configuration_router)
v1_router.include_router(efficiency_router)
v1_router.include_router(loading_efficiency_router)
v1_router.include_router(path_planning_router)
v1_router.include_router(evaluation_router)
v1_router.include_router(advanced_scheduling_router)
v1_router.include_router(robust_optimization_router)
v1_router.include_router(multi_objective_router)

# 导出路由器
__all__ = ["v1_router"]
