"""
综合评估Schema定义

遵循base-rules.md规范：
- 使用描述性的类和属性命名
- 类文档字符串规则：说明类的用途、主要功能
"""

from typing import Optional, List, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field, validator

from src.database.models.evaluation import (
    EvaluationStatusEnum,
    EvaluationMethodEnum,
    ComparisonTypeEnum
)


class EvaluationScenarioSchema(BaseModel):
    """评估场景Schema"""
    scenario_id: str = Field(..., description="场景ID")
    scenario_name: str = Field(..., description="场景名称")
    scenario_description: Optional[str] = Field(None, description="场景描述")
    scenario_parameters: Dict[str, Any] = Field(..., description="场景参数")
    weight: float = Field(1.0, ge=0.0, description="场景权重")


class EvaluationTaskCreateSchema(BaseModel):
    """创建综合评估任务Schema"""
    task_name: str = Field(..., min_length=1, max_length=255, description="任务名称")
    task_description: Optional[str] = Field(None, description="任务描述")
    scheme_ids: List[str] = Field(..., min_items=2, description="参与评估的方案ID列表")
    scheme_names: List[str] = Field(..., min_items=2, description="方案名称列表")
    evaluation_scenarios: List[EvaluationScenarioSchema] = Field(..., description="评估场景列表")
    evaluation_indicators: List[str] = Field(..., min_items=1, description="评估指标ID列表")
    indicator_weights: Dict[str, float] = Field(..., description="指标权重配置")
    weight_config_id: Optional[str] = Field(None, description="权重配置ID")
    evaluation_method: EvaluationMethodEnum = Field(..., description="评估方法")
    method_parameters: Optional[Dict[str, Any]] = Field(None, description="方法参数")
    sensitivity_analysis: bool = Field(False, description="是否进行敏感性分析")
    uncertainty_analysis: bool = Field(False, description="是否进行不确定性分析")
    comparison_type: ComparisonTypeEnum = Field(ComparisonTypeEnum.RANKING, description="比较类型")
    created_by: Optional[str] = Field(None, description="创建者")
    
    @validator('scheme_names')
    def validate_scheme_names(cls, v, values):
        """验证方案名称与ID数量匹配"""
        scheme_ids = values.get('scheme_ids', [])
        if len(v) != len(scheme_ids):
            raise ValueError("方案名称数量必须与方案ID数量匹配")
        return v
    
    @validator('indicator_weights')
    def validate_indicator_weights(cls, v, values):
        """验证指标权重"""
        indicators = values.get('evaluation_indicators', [])
        for indicator_id in indicators:
            if indicator_id not in v:
                raise ValueError(f"缺少指标{indicator_id}的权重设置")
        
        # 检查权重是否为正数
        for weight in v.values():
            if weight <= 0:
                raise ValueError("指标权重必须大于0")
        
        return v


class SchemeRankingSchema(BaseModel):
    """方案排序Schema"""
    rank: int = Field(..., ge=1, description="排名")
    scheme_id: str = Field(..., description="方案ID")
    scheme_name: str = Field(..., description="方案名称")
    score: float = Field(..., description="总得分")
    normalized_score: float = Field(..., ge=0.0, le=1.0, description="标准化得分")
    category_scores: Optional[Dict[str, float]] = Field(None, description="分类得分")
    strength_areas: Optional[List[str]] = Field(None, description="优势领域")
    weakness_areas: Optional[List[str]] = Field(None, description="劣势领域")


class EvaluationSummarySchema(BaseModel):
    """评估摘要Schema"""
    total_schemes: int = Field(..., description="参与评估的方案总数")
    evaluation_method: EvaluationMethodEnum = Field(..., description="评估方法")
    best_scheme: SchemeRankingSchema = Field(..., description="最佳方案")
    worst_scheme: SchemeRankingSchema = Field(..., description="最差方案")
    score_distribution: Dict[str, float] = Field(..., description="得分分布统计")
    evaluation_quality: Dict[str, Any] = Field(..., description="评估质量指标")


class SensitivityAnalysisSchema(BaseModel):
    """敏感性分析Schema"""
    weight_sensitivity: Dict[str, Dict[str, float]] = Field(..., description="权重敏感性")
    parameter_sensitivity: Dict[str, Dict[str, float]] = Field(..., description="参数敏感性")
    robustness_score: float = Field(..., ge=0.0, le=1.0, description="鲁棒性评分")
    critical_factors: List[str] = Field(..., description="关键影响因素")
    stability_analysis: Dict[str, Any] = Field(..., description="稳定性分析")


class UncertaintyAnalysisSchema(BaseModel):
    """不确定性分析Schema"""
    uncertainty_bounds: Dict[str, Dict[str, float]] = Field(..., description="不确定性边界")
    confidence_intervals: Dict[str, Dict[str, List[float]]] = Field(..., description="置信区间")
    risk_assessment: Dict[str, Any] = Field(..., description="风险评估")
    monte_carlo_results: Optional[Dict[str, Any]] = Field(None, description="蒙特卡洛分析结果")


class ImprovementSuggestionSchema(BaseModel):
    """改进建议Schema"""
    suggestion_id: str = Field(..., description="建议ID")
    suggestion_type: str = Field(..., description="建议类型")
    target_scheme: str = Field(..., description="目标方案")
    target_indicator: Optional[str] = Field(None, description="目标指标")
    suggestion_content: str = Field(..., description="建议内容")
    expected_improvement: Optional[float] = Field(None, description="预期改进幅度")
    implementation_difficulty: str = Field(..., description="实施难度")
    priority: str = Field(..., description="优先级")


class EvaluationTaskResponseSchema(BaseModel):
    """综合评估任务响应Schema"""
    task_id: str = Field(..., description="任务ID")
    task_name: str = Field(..., description="任务名称")
    task_description: Optional[str] = Field(None, description="任务描述")
    scheme_count: int = Field(..., description="方案数量")
    evaluation_method: EvaluationMethodEnum = Field(..., description="评估方法")
    status: EvaluationStatusEnum = Field(..., description="任务状态")
    progress_percentage: int = Field(..., description="进度百分比")
    started_at: Optional[datetime] = Field(None, description="开始时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")
    evaluation_duration: Optional[int] = Field(None, description="评估耗时（秒）")
    scheme_ranking: Optional[List[SchemeRankingSchema]] = Field(None, description="方案排序")
    evaluation_summary: Optional[EvaluationSummarySchema] = Field(None, description="评估摘要")
    sensitivity_analysis: Optional[SensitivityAnalysisSchema] = Field(None, description="敏感性分析")
    uncertainty_analysis: Optional[UncertaintyAnalysisSchema] = Field(None, description="不确定性分析")
    improvement_suggestions: Optional[List[ImprovementSuggestionSchema]] = Field(None, description="改进建议")
    error_message: Optional[str] = Field(None, description="错误信息")
    created_by: Optional[str] = Field(None, description="创建者")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")


class WeightConfigurationCreateSchema(BaseModel):
    """创建权重配置Schema"""
    config_name: str = Field(..., min_length=1, max_length=255, description="配置名称")
    config_description: Optional[str] = Field(None, description="配置描述")
    applicable_scenarios: List[str] = Field(..., min_items=1, description="适用场景类型列表")
    scenario_conditions: Optional[Dict[str, Any]] = Field(None, description="场景条件限制")
    indicator_weights: Dict[str, float] = Field(..., description="指标权重配置")
    category_weights: Optional[Dict[str, float]] = Field(None, description="指标分类权重")
    weight_source: str = Field(..., description="权重来源")
    source_details: Optional[Dict[str, Any]] = Field(None, description="来源详细信息")
    is_default: bool = Field(False, description="是否默认配置")
    valid_from: Optional[datetime] = Field(None, description="生效时间")
    valid_until: Optional[datetime] = Field(None, description="失效时间")
    created_by: Optional[str] = Field(None, description="创建者")
    
    @validator('indicator_weights')
    def validate_weights(cls, v):
        """验证权重值"""
        for weight in v.values():
            if weight <= 0:
                raise ValueError("权重值必须大于0")
        return v


class WeightConfigurationResponseSchema(BaseModel):
    """权重配置响应Schema"""
    config_id: str = Field(..., description="配置ID")
    config_name: str = Field(..., description="配置名称")
    config_description: Optional[str] = Field(None, description="配置描述")
    applicable_scenarios: List[str] = Field(..., description="适用场景")
    indicator_count: int = Field(..., description="指标数量")
    weight_source: str = Field(..., description="权重来源")
    consistency_ratio: Optional[float] = Field(None, description="一致性比率")
    version: str = Field(..., description="配置版本")
    is_active: bool = Field(..., description="是否启用")
    is_default: bool = Field(..., description="是否默认配置")
    valid_from: Optional[datetime] = Field(None, description="生效时间")
    valid_until: Optional[datetime] = Field(None, description="失效时间")
    created_by: Optional[str] = Field(None, description="创建者")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")


class EvaluationExecutionRequestSchema(BaseModel):
    """评估执行请求Schema"""
    task_id: str = Field(..., description="任务ID")
    force_restart: bool = Field(False, description="是否强制重新开始")
    parallel_execution: bool = Field(False, description="是否并行执行")


class EvaluationExecutionResponseSchema(BaseModel):
    """评估执行响应Schema"""
    task_id: str = Field(..., description="任务ID")
    execution_status: str = Field(..., description="执行状态")
    message: str = Field(..., description="执行消息")
    estimated_completion_time: Optional[datetime] = Field(None, description="预计完成时间")


class SchemeComparisonRequestSchema(BaseModel):
    """方案比较请求Schema"""
    scheme_ids: List[str] = Field(..., min_items=2, description="待比较的方案ID列表")
    comparison_indicators: List[str] = Field(..., description="比较指标")
    comparison_scenarios: List[str] = Field(..., description="比较场景")
    comparison_method: EvaluationMethodEnum = Field(EvaluationMethodEnum.WEIGHTED_AVERAGE, description="比较方法")
    weights: Optional[Dict[str, float]] = Field(None, description="比较权重")


class SchemeComparisonResponseSchema(BaseModel):
    """方案比较响应Schema"""
    comparison_id: str = Field(..., description="比较ID")
    scheme_ranking: List[SchemeRankingSchema] = Field(..., description="方案排序")
    pairwise_comparisons: List[Dict[str, Any]] = Field(..., description="两两比较结果")
    dominance_analysis: Dict[str, Any] = Field(..., description="支配关系分析")
    similarity_analysis: Dict[str, Any] = Field(..., description="相似性分析")
    trade_off_analysis: Dict[str, Any] = Field(..., description="权衡分析")
    recommendation: Dict[str, Any] = Field(..., description="推荐结果")
    created_at: datetime = Field(..., description="创建时间")
