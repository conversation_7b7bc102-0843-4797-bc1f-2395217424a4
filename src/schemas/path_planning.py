"""
路径规划Schema定义

遵循base-rules.md规范：
- 使用描述性的类和属性命名
- 类文档字符串规则：说明类的用途、主要功能
"""

from typing import Optional, List, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field, validator

from src.database.models.path_planning import (
    PathPlanningStatusEnum,
    PathTypeEnum,
    OptimizationObjectiveEnum
)


class PathPointSchema(BaseModel):
    """路径点Schema"""
    node_id: str = Field(..., description="节点ID")
    coordinates: List[float] = Field(..., description="坐标[x, y]")
    node_type: Optional[str] = Field(None, description="节点类型")
    properties: Optional[Dict[str, Any]] = Field(None, description="节点属性")


class PathPlanningTaskCreateSchema(BaseModel):
    """创建路径规划任务Schema"""
    task_name: str = Field(..., min_length=1, max_length=255, description="任务名称")
    task_description: Optional[str] = Field(None, description="任务描述")
    path_type: PathTypeEnum = Field(..., description="路径类型")
    start_point: PathPointSchema = Field(..., description="起点信息")
    end_point: PathPointSchema = Field(..., description="终点信息")
    waypoints: Optional[List[PathPointSchema]] = Field(None, description="中间路径点")
    constraints: Optional[Dict[str, Any]] = Field(None, description="约束条件")
    optimization_objectives: List[OptimizationObjectiveEnum] = Field(..., description="优化目标")
    objective_weights: Optional[Dict[str, float]] = Field(None, description="目标权重")
    algorithm_type: str = Field(..., description="算法类型")
    algorithm_parameters: Optional[Dict[str, Any]] = Field(None, description="算法参数")
    created_by: Optional[str] = Field(None, description="创建者")
    
    @validator('optimization_objectives')
    def validate_objectives(cls, v):
        """验证优化目标"""
        if not v:
            raise ValueError("至少需要指定一个优化目标")
        return v
    
    @validator('objective_weights')
    def validate_weights(cls, v, values):
        """验证目标权重"""
        if v is not None:
            objectives = values.get('optimization_objectives', [])
            for obj in objectives:
                if obj.value not in v:
                    raise ValueError(f"缺少目标{obj.value}的权重设置")
        return v


class PathPlanningTaskUpdateSchema(BaseModel):
    """更新路径规划任务Schema"""
    task_name: Optional[str] = Field(None, min_length=1, max_length=255, description="任务名称")
    task_description: Optional[str] = Field(None, description="任务描述")
    constraints: Optional[Dict[str, Any]] = Field(None, description="约束条件")
    objective_weights: Optional[Dict[str, float]] = Field(None, description="目标权重")
    algorithm_parameters: Optional[Dict[str, Any]] = Field(None, description="算法参数")


class PathNodeDetailSchema(BaseModel):
    """路径节点详情Schema"""
    node_id: str = Field(..., description="节点ID")
    coordinates: List[float] = Field(..., description="坐标")
    distance_from_start: float = Field(..., description="距离起点的距离")
    estimated_time: Optional[float] = Field(None, description="预计到达时间")
    node_properties: Optional[Dict[str, Any]] = Field(None, description="节点属性")


class PathAnalysisSchema(BaseModel):
    """路径分析Schema"""
    total_distance: float = Field(..., description="总距离")
    estimated_time: float = Field(..., description="预计时间（分钟）")
    estimated_cost: Optional[float] = Field(None, description="预计成本")
    path_efficiency: float = Field(..., description="路径效率")
    bottleneck_points: Optional[List[str]] = Field(None, description="瓶颈点")
    risk_assessment: Optional[Dict[str, Any]] = Field(None, description="风险评估")


class PathPlanningResultSchema(BaseModel):
    """路径规划结果Schema"""
    result_id: str = Field(..., description="结果ID")
    task_id: str = Field(..., description="任务ID")
    optimal_path: List[str] = Field(..., description="最优路径节点序列")
    path_details: List[PathNodeDetailSchema] = Field(..., description="路径节点详情")
    path_analysis: PathAnalysisSchema = Field(..., description="路径分析")
    alternative_paths: Optional[List[Dict[str, Any]]] = Field(None, description="备选路径")
    constraint_satisfaction: Dict[str, Any] = Field(..., description="约束满足情况")
    resource_requirements: Dict[str, Any] = Field(..., description="资源需求")
    created_at: datetime = Field(..., description="创建时间")


class PathPlanningTaskResponseSchema(BaseModel):
    """路径规划任务响应Schema"""
    task_id: str = Field(..., description="任务ID")
    task_name: str = Field(..., description="任务名称")
    task_description: Optional[str] = Field(None, description="任务描述")
    path_type: PathTypeEnum = Field(..., description="路径类型")
    start_point: PathPointSchema = Field(..., description="起点信息")
    end_point: PathPointSchema = Field(..., description="终点信息")
    waypoints: Optional[List[PathPointSchema]] = Field(None, description="中间路径点")
    optimization_objectives: List[OptimizationObjectiveEnum] = Field(..., description="优化目标")
    algorithm_type: str = Field(..., description="算法类型")
    status: PathPlanningStatusEnum = Field(..., description="任务状态")
    progress_percentage: int = Field(..., description="进度百分比")
    started_at: Optional[datetime] = Field(None, description="开始时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")
    planning_duration: Optional[int] = Field(None, description="规划耗时（秒）")
    optimal_path: Optional[Dict[str, Any]] = Field(None, description="最优路径")
    performance_metrics: Optional[Dict[str, Any]] = Field(None, description="性能指标")
    error_message: Optional[str] = Field(None, description="错误信息")
    created_by: Optional[str] = Field(None, description="创建者")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")


class WorkAreaTopologyCreateSchema(BaseModel):
    """创建作业区域拓扑Schema"""
    area_name: str = Field(..., min_length=1, max_length=255, description="区域名称")
    area_type: str = Field(..., description="区域类型")
    area_description: Optional[str] = Field(None, description="区域描述")
    nodes: List[Dict[str, Any]] = Field(..., description="节点信息列表")
    edges: List[Dict[str, Any]] = Field(..., description="边信息列表")
    area_properties: Optional[Dict[str, Any]] = Field(None, description="区域属性")
    boundary_coordinates: Optional[List[List[float]]] = Field(None, description="区域边界坐标")
    center_coordinates: Optional[List[float]] = Field(None, description="区域中心坐标")
    created_by: Optional[str] = Field(None, description="创建者")
    
    @validator('nodes')
    def validate_nodes(cls, v):
        """验证节点数据"""
        if not v:
            raise ValueError("至少需要定义一个节点")
        
        for node in v:
            if 'id' not in node or 'x' not in node or 'y' not in node:
                raise ValueError("节点必须包含id、x、y字段")
        
        return v
    
    @validator('edges')
    def validate_edges(cls, v, values):
        """验证边数据"""
        if not v:
            return v
        
        nodes = values.get('nodes', [])
        node_ids = {node['id'] for node in nodes}
        
        for edge in v:
            if 'from' not in edge or 'to' not in edge or 'weight' not in edge:
                raise ValueError("边必须包含from、to、weight字段")
            
            if edge['from'] not in node_ids or edge['to'] not in node_ids:
                raise ValueError("边引用的节点不存在")
            
            if edge['weight'] <= 0:
                raise ValueError("边权重必须大于0")
        
        return v


class WorkAreaTopologyResponseSchema(BaseModel):
    """作业区域拓扑响应Schema"""
    topology_id: str = Field(..., description="拓扑ID")
    area_name: str = Field(..., description="区域名称")
    area_type: str = Field(..., description="区域类型")
    area_description: Optional[str] = Field(None, description="区域描述")
    nodes_count: int = Field(..., description="节点数量")
    edges_count: int = Field(..., description="边数量")
    area_properties: Dict[str, Any] = Field(..., description="区域属性")
    current_status: Dict[str, Any] = Field(..., description="当前状态")
    version: str = Field(..., description="版本")
    is_active: bool = Field(..., description="是否启用")
    created_by: Optional[str] = Field(None, description="创建者")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")


class PathPlanningExecutionRequestSchema(BaseModel):
    """路径规划执行请求Schema"""
    task_id: str = Field(..., description="任务ID")
    force_restart: bool = Field(False, description="是否强制重新开始")
    priority: Optional[str] = Field(None, description="执行优先级")


class PathPlanningExecutionResponseSchema(BaseModel):
    """路径规划执行响应Schema"""
    task_id: str = Field(..., description="任务ID")
    execution_status: str = Field(..., description="执行状态")
    message: str = Field(..., description="执行消息")
    estimated_completion_time: Optional[datetime] = Field(None, description="预计完成时间")


class PathComparisonRequestSchema(BaseModel):
    """路径比较请求Schema"""
    path_results: List[str] = Field(..., min_items=2, description="待比较的路径结果ID列表")
    comparison_criteria: List[str] = Field(..., description="比较标准")
    weights: Optional[Dict[str, float]] = Field(None, description="比较权重")


class PathComparisonResponseSchema(BaseModel):
    """路径比较响应Schema"""
    comparison_id: str = Field(..., description="比较ID")
    best_path_id: str = Field(..., description="最佳路径ID")
    comparison_results: List[Dict[str, Any]] = Field(..., description="比较结果")
    ranking: List[Dict[str, Any]] = Field(..., description="路径排名")
    analysis_summary: Dict[str, Any] = Field(..., description="分析摘要")
    created_at: datetime = Field(..., description="创建时间")
