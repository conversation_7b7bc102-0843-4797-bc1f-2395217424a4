"""
多目标优化Schema定义

遵循base-rules.md规范：
- 使用描述性的类和属性命名
- 类文档字符串规则：说明类的用途、主要功能
"""

from typing import Optional, List, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field, validator


class ObjectiveFunctionSchema(BaseModel):
    """目标函数Schema"""
    function_id: str = Field(..., description="函数ID")
    function_name: str = Field(..., description="函数名称")
    expression: str = Field(..., description="函数表达式")
    type: str = Field("minimize", description="优化类型")
    weight: float = Field(1.0, ge=0.0, description="权重")
    priority: int = Field(1, ge=1, description="优先级")


class DecisionVariableSchema(BaseModel):
    """决策变量Schema"""
    variable_name: str = Field(..., description="变量名称")
    variable_type: str = Field("continuous", description="变量类型")
    lower_bound: float = Field(..., description="下界")
    upper_bound: float = Field(..., description="上界")
    initial_value: Optional[float] = Field(None, description="初始值")


class MultiObjectiveTaskCreateSchema(BaseModel):
    """创建多目标优化任务Schema"""
    task_name: str = Field(..., min_length=1, max_length=255, description="任务名称")
    task_description: Optional[str] = Field(None, description="任务描述")
    objective_functions: List[ObjectiveFunctionSchema] = Field(..., min_items=2, description="目标函数列表")
    objective_weights: Optional[Dict[str, float]] = Field(None, description="目标权重")
    decision_variables: List[str] = Field(..., min_items=1, description="决策变量名称列表")
    variable_bounds: Dict[str, Dict[str, float]] = Field(..., description="变量边界")
    constraints: Optional[List[Dict[str, Any]]] = Field(None, description="约束条件")
    algorithm_type: str = Field("nsga2", description="算法类型")
    algorithm_parameters: Optional[Dict[str, Any]] = Field(None, description="算法参数")
    population_size: int = Field(100, ge=10, le=1000, description="种群大小")
    max_generations: int = Field(1000, ge=100, le=10000, description="最大代数")
    termination_criteria: Optional[Dict[str, Any]] = Field(None, description="终止条件")
    created_by: Optional[str] = Field(None, description="创建者")
    optimization_task_id: Optional[str] = Field(None, description="关联的优化任务ID")
    
    @validator('objective_functions')
    def validate_objectives(cls, v):
        """验证目标函数"""
        if len(v) < 2:
            raise ValueError("多目标优化至少需要2个目标函数")
        return v


class MultiObjectiveTaskResponseSchema(BaseModel):
    """多目标优化任务响应Schema"""
    task_id: str = Field(..., description="任务ID")
    task_name: str = Field(..., description="任务名称")
    task_description: Optional[str] = Field(None, description="任务描述")
    num_objectives: int = Field(..., description="目标函数数量")
    num_variables: int = Field(..., description="决策变量数量")
    algorithm_type: str = Field(..., description="算法类型")
    status: str = Field(..., description="任务状态")
    population_size: int = Field(..., description="种群大小")
    max_generations: int = Field(..., description="最大代数")
    current_generation: Optional[int] = Field(None, description="当前代数")
    started_at: Optional[datetime] = Field(None, description="开始时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")
    optimization_duration: Optional[int] = Field(None, description="优化耗时（秒）")
    pareto_front_size: Optional[int] = Field(None, description="帕累托前沿大小")
    hypervolume: Optional[float] = Field(None, description="超体积指标")
    spacing: Optional[float] = Field(None, description="间距指标")
    spread: Optional[float] = Field(None, description="分布性指标")
    error_message: Optional[str] = Field(None, description="错误信息")
    created_by: Optional[str] = Field(None, description="创建者")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")


class ParetoSolutionSchema(BaseModel):
    """帕累托解Schema"""
    solution_id: str = Field(..., description="解ID")
    decision_variables: List[float] = Field(..., description="决策变量值")
    objective_values: List[float] = Field(..., description="目标函数值")
    dominance_rank: int = Field(..., description="支配等级")
    crowding_distance: float = Field(..., description="拥挤距离")
    feasibility: bool = Field(..., description="可行性")
    generation: int = Field(..., description="产生代数")


class MultiObjectiveExecutionRequestSchema(BaseModel):
    """多目标优化执行请求Schema"""
    task_id: str = Field(..., description="任务ID")
    force_restart: bool = Field(False, description="是否强制重新开始")
    save_intermediate_results: bool = Field(False, description="是否保存中间结果")


class MultiObjectiveExecutionResponseSchema(BaseModel):
    """多目标优化执行响应Schema"""
    task_id: str = Field(..., description="任务ID")
    execution_status: str = Field(..., description="执行状态")
    message: str = Field(..., description="执行消息")
    estimated_completion_time: Optional[datetime] = Field(None, description="预计完成时间")


class ParetoFrontAnalysisRequestSchema(BaseModel):
    """帕累托前沿分析请求Schema"""
    solutions: List[ParetoSolutionSchema] = Field(..., min_items=1, description="帕累托解列表")
    reference_point: Optional[List[float]] = Field(None, description="参考点")
    true_pareto_front: Optional[List[List[float]]] = Field(None, description="真实帕累托前沿")


class ParetoFrontAnalysisResponseSchema(BaseModel):
    """帕累托前沿分析响应Schema"""
    pareto_front_size: int = Field(..., description="帕累托前沿大小")
    quality_indicators: Dict[str, float] = Field(..., description="质量指标")
    objective_statistics: Dict[str, Dict[str, float]] = Field(..., description="目标统计")
    front_characteristics: Dict[str, Any] = Field(..., description="前沿特性")
    recommendations: List[str] = Field(..., description="改进建议")
    analysis_time: Optional[float] = Field(None, description="分析耗时（秒）")


class MultiObjectiveComparisonRequestSchema(BaseModel):
    """多目标优化比较请求Schema"""
    task_ids: List[str] = Field(..., min_items=2, description="待比较的任务ID列表")
    comparison_metrics: List[str] = Field(..., description="比较指标")
    reference_point: Optional[List[float]] = Field(None, description="参考点")


class MultiObjectiveComparisonResponseSchema(BaseModel):
    """多目标优化比较响应Schema"""
    comparison_id: str = Field(..., description="比较ID")
    task_comparison: List[Dict[str, Any]] = Field(..., description="任务比较结果")
    ranking: List[Dict[str, Any]] = Field(..., description="算法排名")
    statistical_tests: Dict[str, Any] = Field(..., description="统计检验结果")
    visualization_data: Dict[str, Any] = Field(..., description="可视化数据")
    summary: Dict[str, Any] = Field(..., description="比较摘要")
    created_at: datetime = Field(..., description="创建时间")


class ObjectiveFunctionTemplateSchema(BaseModel):
    """目标函数模板Schema"""
    template_id: str = Field(..., description="模板ID")
    template_name: str = Field(..., description="模板名称")
    description: str = Field(..., description="描述")
    category: str = Field(..., description="类别")
    objectives: List[Dict[str, Any]] = Field(..., description="目标函数定义")
    variable_constraints: Dict[str, Any] = Field(..., description="变量约束")
    problem_characteristics: List[str] = Field(..., description="问题特性")
    recommended_algorithms: List[str] = Field(..., description="推荐算法")
    difficulty_level: str = Field(..., description="难度等级")


class AlgorithmPerformanceSchema(BaseModel):
    """算法性能Schema"""
    algorithm_name: str = Field(..., description="算法名称")
    convergence_speed: str = Field(..., description="收敛速度")
    solution_quality: str = Field(..., description="解质量")
    diversity_maintenance: str = Field(..., description="多样性维护")
    scalability: str = Field(..., description="可扩展性")
    computational_complexity: str = Field(..., description="计算复杂度")
    memory_usage: str = Field(..., description="内存使用")
    suitable_objectives: List[str] = Field(..., description="适用目标数量")
    problem_types: List[str] = Field(..., description="适用问题类型")
