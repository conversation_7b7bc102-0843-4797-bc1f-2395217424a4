"""
鲁棒优化Schema定义

遵循base-rules.md规范：
- 使用描述性的类和属性命名
- 类文档字符串规则：说明类的用途、主要功能
"""

from typing import Optional, List, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field, validator


class UncertaintySetSchema(BaseModel):
    """不确定性集合Schema"""
    parameter_names: List[str] = Field(..., description="不确定参数名称列表")
    nominal_values: List[float] = Field(..., description="标称值")
    uncertainty_bounds: List[List[float]] = Field(..., description="不确定性边界")
    uncertainty_type: str = Field("box", description="不确定性集合类型")
    correlation_matrix: Optional[List[List[float]]] = Field(None, description="相关性矩阵")


class RobustOptimizationTaskCreateSchema(BaseModel):
    """创建鲁棒优化任务Schema"""
    task_name: str = Field(..., min_length=1, max_length=255, description="任务名称")
    task_description: Optional[str] = Field(None, description="任务描述")
    problem_definition: Dict[str, Any] = Field(..., description="问题定义")
    algorithm_config: Optional[Dict[str, Any]] = Field(None, description="算法配置")
    created_by: Optional[str] = Field(None, description="创建者")
    scenario_id: Optional[str] = Field(None, description="场景ID")
    
    @validator('problem_definition')
    def validate_problem_definition(cls, v):
        """验证问题定义"""
        if "uncertainty_set" not in v and "random_parameters_distribution" not in v:
            raise ValueError("必须定义不确定性集合或随机参数分布")
        return v


class RobustOptimizationTaskResponseSchema(BaseModel):
    """鲁棒优化任务响应Schema"""
    task_id: str = Field(..., description="任务ID")
    task_name: str = Field(..., description="任务名称")
    task_description: Optional[str] = Field(None, description="任务描述")
    status: str = Field(..., description="任务状态")
    optimization_method: Optional[str] = Field(None, description="优化方法")
    robustness_level: Optional[float] = Field(None, description="鲁棒性水平")
    started_at: Optional[datetime] = Field(None, description="开始时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")
    execution_duration: Optional[int] = Field(None, description="执行耗时（秒）")
    robust_solution: Optional[Dict[str, Any]] = Field(None, description="鲁棒解")
    robustness_analysis: Optional[Dict[str, Any]] = Field(None, description="鲁棒性分析")
    error_message: Optional[str] = Field(None, description="错误信息")
    created_by: Optional[str] = Field(None, description="创建者")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")


class RobustOptimizationExecutionRequestSchema(BaseModel):
    """鲁棒优化执行请求Schema"""
    task_id: str = Field(..., description="任务ID")
    optimization_method: str = Field("worst_case", description="优化方法")
    force_restart: bool = Field(False, description="是否强制重新开始")


class RobustOptimizationExecutionResponseSchema(BaseModel):
    """鲁棒优化执行响应Schema"""
    task_id: str = Field(..., description="任务ID")
    execution_status: str = Field(..., description="执行状态")
    message: str = Field(..., description="执行消息")
    optimization_method: str = Field(..., description="优化方法")
    estimated_completion_time: Optional[datetime] = Field(None, description="预计完成时间")


class UncertaintyAnalysisResponseSchema(BaseModel):
    """不确定性分析响应Schema"""
    task_id: str = Field(..., description="任务ID")
    optimization_method: str = Field(..., description="优化方法")
    robustness_measure: float = Field(..., description="鲁棒性度量")
    sensitivity_analysis: Dict[str, Any] = Field(..., description="敏感性分析")
    uncertainty_impact: Dict[str, Any] = Field(..., description="不确定性影响")
    scenario_analysis: Optional[Dict[str, Any]] = Field(None, description="场景分析")
    confidence_intervals: Dict[str, Any] = Field(..., description="置信区间")


class ScenarioGenerationRequestSchema(BaseModel):
    """场景生成请求Schema"""
    num_scenarios: int = Field(100, ge=10, le=10000, description="场景数量")
    parameters: Dict[str, Dict[str, Any]] = Field(..., description="参数配置")
    method: str = Field("monte_carlo", description="生成方法")
    random_seed: Optional[int] = Field(None, description="随机种子")


class ScenarioGenerationResponseSchema(BaseModel):
    """场景生成响应Schema"""
    scenarios: List[Dict[str, Any]] = Field(..., description="生成的场景")
    num_scenarios: int = Field(..., description="场景数量")
    generation_method: str = Field(..., description="生成方法")
    statistics: Dict[str, Dict[str, float]] = Field(..., description="统计信息")
    generation_time: Optional[float] = Field(None, description="生成耗时（秒）")
