"""
模拟退火算法实现

遵循base-rules.md规范：
- 函数复杂度控制，每个函数圈复杂度不超过10
- 函数参数不超过5个
- 使用描述性的函数命名
"""

import random
import math
import numpy as np
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass

from src.algorithms.base import BaseOptimizationAlgorithm, AlgorithmConfig, OptimizationResult


@dataclass
class AnnealingState:
    """退火状态数据类"""
    solution: Dict[str, Any]
    fitness: float
    temperature: float
    iteration: int
    accepted_moves: int = 0
    rejected_moves: int = 0


class SimulatedAnnealing(BaseOptimizationAlgorithm):
    """
    模拟退火算法实现
    
    实现经典的模拟退火算法用于资源调度优化：
    - 支持多种降温策略
    - 自适应邻域搜索
    - 重启机制避免局部最优
    """
    
    def __init__(self, config: AlgorithmConfig) -> None:
        """
        初始化模拟退火算法
        
        Args:
            config: 算法配置参数
        """
        super().__init__(config)
        
        # 模拟退火参数
        self.initial_temperature = config.algorithm_params.get("initial_temperature", 1000.0)
        self.final_temperature = config.algorithm_params.get("final_temperature", 0.01)
        self.cooling_rate = config.algorithm_params.get("cooling_rate", 0.95)
        self.cooling_schedule = config.algorithm_params.get("cooling_schedule", "exponential")
        self.markov_chain_length = config.algorithm_params.get("markov_chain_length", 100)
        self.restart_threshold = config.algorithm_params.get("restart_threshold", 1000)
        
        # 邻域搜索参数
        self.neighborhood_size = config.algorithm_params.get("neighborhood_size", 10)
        self.perturbation_strength = config.algorithm_params.get("perturbation_strength", 0.1)
        
        # 算法状态
        self.current_state: Optional[AnnealingState] = None
        self.best_state: Optional[AnnealingState] = None
        self.temperature_history: List[float] = []
        self.acceptance_history: List[float] = []
        self.restart_count = 0
        
        # 问题相关参数
        self.solution_bounds = []
        self.solution_dimension = 0
    
    def initialize_population(self, problem_definition: Dict[str, Any]) -> None:
        """
        初始化算法状态
        
        Args:
            problem_definition: 问题定义
        """
        # 解析问题参数
        self.solution_bounds = problem_definition.get("solution_bounds", [(0, 1)] * 10)
        self.solution_dimension = len(self.solution_bounds)
        
        # 生成初始解
        initial_solution = self._generate_random_solution()
        initial_fitness = self.evaluate_solution(initial_solution)
        
        # 初始化状态
        self.current_state = AnnealingState(
            solution=initial_solution,
            fitness=initial_fitness,
            temperature=self.initial_temperature,
            iteration=0
        )
        
        self.best_state = AnnealingState(
            solution=initial_solution.copy(),
            fitness=initial_fitness,
            temperature=self.initial_temperature,
            iteration=0
        )
    
    def _generate_random_solution(self) -> Dict[str, Any]:
        """
        生成随机解
        
        Returns:
            Dict[str, Any]: 随机解
        """
        solution_vector = []
        for i in range(self.solution_dimension):
            lower, upper = self.solution_bounds[i]
            value = random.uniform(lower, upper)
            solution_vector.append(value)
        
        return {
            "variables": solution_vector,
            "dimension": self.solution_dimension
        }
    
    def evaluate_solution(self, solution: Dict[str, Any]) -> float:
        """
        评估解的适应度
        
        Args:
            solution: 解
            
        Returns:
            float: 适应度值
        """
        if self.objective_function is None:
            return float('inf')
        
        return self.objective_function(solution)
    
    def generate_new_solutions(self) -> List[Dict[str, Any]]:
        """
        执行模拟退火迭代
        
        Returns:
            List[Dict[str, Any]]: 当前解列表
        """
        if self.current_state is None:
            return []
        
        # 执行马尔可夫链
        for _ in range(self.markov_chain_length):
            self._execute_annealing_step()
            
            # 检查是否需要重启
            if self._should_restart():
                self._restart_algorithm()
                break
        
        # 更新温度
        self._update_temperature()
        
        # 记录历史
        self.temperature_history.append(self.current_state.temperature)
        acceptance_rate = self._calculate_acceptance_rate()
        self.acceptance_history.append(acceptance_rate)
        
        return [self.current_state.solution]
    
    def _execute_annealing_step(self) -> None:
        """执行单步退火操作"""
        # 生成邻域解
        neighbor_solution = self._generate_neighbor_solution(self.current_state.solution)
        neighbor_fitness = self.evaluate_solution(neighbor_solution)
        
        # 计算适应度差
        delta_fitness = neighbor_fitness - self.current_state.fitness
        
        # 决定是否接受新解
        if self._accept_solution(delta_fitness, self.current_state.temperature):
            # 接受新解
            self.current_state.solution = neighbor_solution
            self.current_state.fitness = neighbor_fitness
            self.current_state.accepted_moves += 1
            
            # 更新最佳解
            if neighbor_fitness < self.best_state.fitness:
                self.best_state.solution = neighbor_solution.copy()
                self.best_state.fitness = neighbor_fitness
                self.best_state.iteration = self.current_iteration
        else:
            # 拒绝新解
            self.current_state.rejected_moves += 1
        
        self.current_state.iteration += 1
    
    def _generate_neighbor_solution(self, current_solution: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成邻域解
        
        Args:
            current_solution: 当前解
            
        Returns:
            Dict[str, Any]: 邻域解
        """
        neighbor = current_solution.copy()
        variables = neighbor["variables"].copy()
        
        # 随机选择要扰动的变量
        num_perturbations = random.randint(1, min(self.neighborhood_size, len(variables)))
        indices_to_perturb = random.sample(range(len(variables)), num_perturbations)
        
        for i in indices_to_perturb:
            lower, upper = self.solution_bounds[i]
            range_size = upper - lower
            
            # 生成扰动
            perturbation = np.random.normal(0, self.perturbation_strength * range_size)
            new_value = variables[i] + perturbation
            
            # 确保在边界内
            variables[i] = np.clip(new_value, lower, upper)
        
        neighbor["variables"] = variables
        return neighbor
    
    def _accept_solution(self, delta_fitness: float, temperature: float) -> bool:
        """
        判断是否接受新解
        
        Args:
            delta_fitness: 适应度差值
            temperature: 当前温度
            
        Returns:
            bool: 是否接受
        """
        # 如果新解更好，直接接受
        if delta_fitness <= 0:
            return True
        
        # 如果温度过低，拒绝
        if temperature <= 0:
            return False
        
        # 根据Metropolis准则计算接受概率
        acceptance_probability = math.exp(-delta_fitness / temperature)
        return random.random() < acceptance_probability
    
    def _update_temperature(self) -> None:
        """更新温度"""
        if self.cooling_schedule == "exponential":
            self.current_state.temperature *= self.cooling_rate
        elif self.cooling_schedule == "linear":
            self._linear_cooling()
        elif self.cooling_schedule == "logarithmic":
            self._logarithmic_cooling()
        elif self.cooling_schedule == "adaptive":
            self._adaptive_cooling()
        
        # 确保温度不低于最终温度
        self.current_state.temperature = max(
            self.current_state.temperature, 
            self.final_temperature
        )
    
    def _linear_cooling(self) -> None:
        """线性降温"""
        temperature_range = self.initial_temperature - self.final_temperature
        progress = self.current_iteration / self.config.max_iterations
        self.current_state.temperature = (
            self.initial_temperature - progress * temperature_range
        )
    
    def _logarithmic_cooling(self) -> None:
        """对数降温"""
        if self.current_iteration > 0:
            self.current_state.temperature = (
                self.initial_temperature / math.log(1 + self.current_iteration)
            )
    
    def _adaptive_cooling(self) -> None:
        """自适应降温"""
        acceptance_rate = self._calculate_acceptance_rate()
        
        # 根据接受率调整降温速度
        if acceptance_rate > 0.8:
            # 接受率过高，加快降温
            self.current_state.temperature *= 0.9
        elif acceptance_rate < 0.2:
            # 接受率过低，减慢降温
            self.current_state.temperature *= 0.99
        else:
            # 正常降温
            self.current_state.temperature *= self.cooling_rate
    
    def _calculate_acceptance_rate(self) -> float:
        """计算当前接受率"""
        total_moves = self.current_state.accepted_moves + self.current_state.rejected_moves
        if total_moves == 0:
            return 0.0
        
        return self.current_state.accepted_moves / total_moves
    
    def _should_restart(self) -> bool:
        """判断是否需要重启"""
        # 如果长时间没有改进，考虑重启
        stagnation_iterations = self.current_iteration - self.best_state.iteration
        return stagnation_iterations > self.restart_threshold
    
    def _restart_algorithm(self) -> None:
        """重启算法"""
        self.restart_count += 1
        
        # 生成新的初始解
        new_solution = self._generate_random_solution()
        new_fitness = self.evaluate_solution(new_solution)
        
        # 重置状态
        self.current_state = AnnealingState(
            solution=new_solution,
            fitness=new_fitness,
            temperature=self.initial_temperature,
            iteration=self.current_iteration
        )
        
        # 如果新解更好，更新最佳解
        if new_fitness < self.best_state.fitness:
            self.best_state.solution = new_solution.copy()
            self.best_state.fitness = new_fitness
            self.best_state.iteration = self.current_iteration
    
    def select_solutions(self, solutions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """选择最优解"""
        if not solutions:
            return []
        
        return [solutions[0]]  # 模拟退火只维护一个解
    
    def _get_best_solution(self) -> Dict[str, Any]:
        """获取当前最佳解"""
        if self.best_state is None:
            return {"variables": [], "fitness": float('inf')}
        
        return {
            "variables": self.best_state.solution["variables"],
            "fitness": self.best_state.fitness,
            "temperature": self.current_state.temperature if self.current_state else 0.0,
            "iteration": self.best_state.iteration,
            "restart_count": self.restart_count,
            "acceptance_rate": self._calculate_acceptance_rate(),
            "cooling_progress": self._calculate_cooling_progress()
        }
    
    def _calculate_cooling_progress(self) -> float:
        """计算降温进度"""
        if self.current_state is None:
            return 0.0
        
        temperature_range = self.initial_temperature - self.final_temperature
        current_drop = self.initial_temperature - self.current_state.temperature
        
        return min(current_drop / temperature_range, 1.0)
    
    def get_algorithm_statistics(self) -> Dict[str, Any]:
        """
        获取算法统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        return {
            "temperature_history": self.temperature_history[-100:],  # 最近100次的温度历史
            "acceptance_history": self.acceptance_history[-100:],    # 最近100次的接受率历史
            "restart_count": self.restart_count,
            "current_temperature": self.current_state.temperature if self.current_state else 0.0,
            "cooling_progress": self._calculate_cooling_progress(),
            "best_fitness": self.best_state.fitness if self.best_state else float('inf'),
            "current_fitness": self.current_state.fitness if self.current_state else float('inf')
        }
