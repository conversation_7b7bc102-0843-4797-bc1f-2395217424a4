"""
Dijkstra最短路径算法实现

遵循base-rules.md规范：
- 函数复杂度控制，每个函数圈复杂度不超过10
- 函数参数不超过5个
- 使用描述性的函数命名
"""

import heapq
from typing import Dict, Any, List, Optional, Tuple, Set
from dataclasses import dataclass

from src.algorithms.base import BaseOptimizationAlgorithm, AlgorithmConfig, OptimizationResult


@dataclass
class GraphNode:
    """图节点数据类"""
    node_id: str
    coordinates: Tuple[float, float]
    node_type: str = "normal"
    properties: Dict[str, Any] = None
    
    def __post_init__(self) -> None:
        if self.properties is None:
            self.properties = {}


@dataclass
class GraphEdge:
    """图边数据类"""
    from_node: str
    to_node: str
    weight: float
    edge_type: str = "normal"
    properties: Dict[str, Any] = None
    
    def __post_init__(self) -> None:
        if self.properties is None:
            self.properties = {}


class DijkstraAlgorithm(BaseOptimizationAlgorithm):
    """
    Dijkstra最短路径算法
    
    实现经典的Dijkstra算法用于单源最短路径问题：
    - 支持加权有向图和无向图
    - 处理多种约束条件
    - 提供路径重构和分析功能
    """
    
    def __init__(self, config: AlgorithmConfig) -> None:
        """
        初始化Dijkstra算法
        
        Args:
            config: 算法配置参数
        """
        super().__init__(config)
        self.graph_nodes: Dict[str, GraphNode] = {}
        self.graph_edges: List[GraphEdge] = []
        self.adjacency_list: Dict[str, List[Tuple[str, float]]] = {}
        self.start_node: Optional[str] = None
        self.end_node: Optional[str] = None
        self.shortest_distances: Dict[str, float] = {}
        self.previous_nodes: Dict[str, Optional[str]] = {}
        self.optimal_path: List[str] = []
    
    def initialize_population(self, problem_definition: Dict[str, Any]) -> None:
        """
        初始化图结构
        
        Args:
            problem_definition: 问题定义，包含图节点、边和起终点信息
        """
        # 解析节点信息
        nodes_data = problem_definition.get("nodes", [])
        for node_data in nodes_data:
            node = GraphNode(
                node_id=node_data["id"],
                coordinates=(node_data["x"], node_data["y"]),
                node_type=node_data.get("type", "normal"),
                properties=node_data.get("properties", {})
            )
            self.graph_nodes[node.node_id] = node
        
        # 解析边信息
        edges_data = problem_definition.get("edges", [])
        for edge_data in edges_data:
            edge = GraphEdge(
                from_node=edge_data["from"],
                to_node=edge_data["to"],
                weight=edge_data["weight"],
                edge_type=edge_data.get("type", "normal"),
                properties=edge_data.get("properties", {})
            )
            self.graph_edges.append(edge)
        
        # 构建邻接表
        self._build_adjacency_list()
        
        # 设置起终点
        self.start_node = problem_definition.get("start_node")
        self.end_node = problem_definition.get("end_node")
        
        if not self.start_node or not self.end_node:
            raise ValueError("必须指定起点和终点")
    
    def _build_adjacency_list(self) -> None:
        """构建邻接表"""
        self.adjacency_list = {node_id: [] for node_id in self.graph_nodes.keys()}
        
        for edge in self.graph_edges:
            # 检查边的约束条件
            if self._is_edge_valid(edge):
                self.adjacency_list[edge.from_node].append((edge.to_node, edge.weight))
                
                # 如果是无向图，添加反向边
                if edge.edge_type == "undirected":
                    self.adjacency_list[edge.to_node].append((edge.from_node, edge.weight))
    
    def _is_edge_valid(self, edge: GraphEdge) -> bool:
        """
        检查边是否满足约束条件
        
        Args:
            edge: 图边
            
        Returns:
            bool: 是否有效
        """
        # 检查节点是否存在
        if edge.from_node not in self.graph_nodes or edge.to_node not in self.graph_nodes:
            return False
        
        # 检查权重是否为正
        if edge.weight <= 0:
            return False
        
        # 检查其他约束条件
        for constraint in self.constraints:
            if not constraint(edge):
                return False
        
        return True
    
    def evaluate_solution(self, solution: Dict[str, Any]) -> float:
        """
        评估路径的总成本
        
        Args:
            solution: 路径解，包含节点序列
            
        Returns:
            float: 路径总成本
        """
        path = solution.get("path", [])
        if len(path) < 2:
            return float('inf')
        
        total_cost = 0.0
        for i in range(len(path) - 1):
            current_node = path[i]
            next_node = path[i + 1]
            
            # 查找边的权重
            edge_weight = self._get_edge_weight(current_node, next_node)
            if edge_weight is None:
                return float('inf')  # 路径不可达
            
            total_cost += edge_weight
        
        return total_cost
    
    def _get_edge_weight(self, from_node: str, to_node: str) -> Optional[float]:
        """
        获取两个节点间的边权重
        
        Args:
            from_node: 起始节点
            to_node: 目标节点
            
        Returns:
            Optional[float]: 边权重，如果不存在则返回None
        """
        if from_node not in self.adjacency_list:
            return None
        
        for neighbor, weight in self.adjacency_list[from_node]:
            if neighbor == to_node:
                return weight
        
        return None
    
    def generate_new_solutions(self) -> List[Dict[str, Any]]:
        """
        执行Dijkstra算法生成最短路径
        
        Returns:
            List[Dict[str, Any]]: 包含最优路径的解列表
        """
        # 初始化距离和前驱节点
        self.shortest_distances = {node: float('inf') for node in self.graph_nodes.keys()}
        self.previous_nodes = {node: None for node in self.graph_nodes.keys()}
        self.shortest_distances[self.start_node] = 0.0
        
        # 优先队列：(距离, 节点ID)
        priority_queue = [(0.0, self.start_node)]
        visited: Set[str] = set()
        
        while priority_queue:
            current_distance, current_node = heapq.heappop(priority_queue)
            
            # 如果已访问过，跳过
            if current_node in visited:
                continue
            
            visited.add(current_node)
            
            # 如果到达终点，可以提前结束
            if current_node == self.end_node:
                break
            
            # 更新邻居节点的距离
            self._update_neighbor_distances(current_node, current_distance, priority_queue)
        
        # 重构最短路径
        self.optimal_path = self._reconstruct_path()
        
        return [{
            "path": self.optimal_path,
            "total_distance": self.shortest_distances.get(self.end_node, float('inf')),
            "path_valid": len(self.optimal_path) > 1
        }]
    
    def _update_neighbor_distances(self, current_node: str, current_distance: float, 
                                 priority_queue: List[Tuple[float, str]]) -> None:
        """
        更新邻居节点的最短距离
        
        Args:
            current_node: 当前节点
            current_distance: 当前距离
            priority_queue: 优先队列
        """
        if current_node not in self.adjacency_list:
            return
        
        for neighbor, edge_weight in self.adjacency_list[current_node]:
            new_distance = current_distance + edge_weight
            
            if new_distance < self.shortest_distances[neighbor]:
                self.shortest_distances[neighbor] = new_distance
                self.previous_nodes[neighbor] = current_node
                heapq.heappush(priority_queue, (new_distance, neighbor))
    
    def _reconstruct_path(self) -> List[str]:
        """
        重构最短路径
        
        Returns:
            List[str]: 从起点到终点的节点序列
        """
        if self.shortest_distances[self.end_node] == float('inf'):
            return []  # 无法到达终点
        
        path = []
        current_node = self.end_node
        
        while current_node is not None:
            path.append(current_node)
            current_node = self.previous_nodes[current_node]
        
        path.reverse()
        return path
    
    def select_solutions(self, solutions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        选择最优解（Dijkstra算法只产生一个最优解）
        
        Args:
            solutions: 候选解列表
            
        Returns:
            List[Dict[str, Any]]: 选择的解列表
        """
        if not solutions:
            return []
        
        # Dijkstra算法保证找到最优解
        return [solutions[0]]
    
    def _get_best_solution(self) -> Dict[str, Any]:
        """
        获取当前最佳解
        
        Returns:
            Dict[str, Any]: 最佳路径解
        """
        return {
            "path": self.optimal_path,
            "total_distance": self.shortest_distances.get(self.end_node, float('inf')),
            "path_nodes": len(self.optimal_path),
            "path_valid": len(self.optimal_path) > 1,
            "node_details": [
                {
                    "node_id": node_id,
                    "coordinates": self.graph_nodes[node_id].coordinates,
                    "distance_from_start": self.shortest_distances.get(node_id, float('inf'))
                }
                for node_id in self.optimal_path
            ]
        }
    
    def get_alternative_paths(self, k: int = 3) -> List[Dict[str, Any]]:
        """
        获取K条最短路径（简化实现）

        Args:
            k: 路径数量

        Returns:
            List[Dict[str, Any]]: 备选路径列表
        """
        # 简化实现：只返回主路径
        # 完整的K最短路径算法需要更复杂的实现
        if self.optimal_path:
            return [self._get_best_solution()]
        return []

    def get_path_analysis(self) -> Dict[str, Any]:
        """
        获取路径分析结果

        Returns:
            Dict[str, Any]: 路径分析数据
        """
        if not self.optimal_path:
            return {"error": "无有效路径"}

        total_distance = self.shortest_distances.get(self.end_node, 0.0)

        return {
            "total_distance": total_distance,
            "path_length": len(self.optimal_path),
            "average_edge_weight": total_distance / max(len(self.optimal_path) - 1, 1),
            "start_node": self.start_node,
            "end_node": self.end_node,
            "path_nodes": self.optimal_path,
            "visited_nodes_count": len([d for d in self.shortest_distances.values() if d != float('inf')])
        }
