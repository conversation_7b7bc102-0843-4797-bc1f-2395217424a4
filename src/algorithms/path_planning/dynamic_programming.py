"""
动态规划路径优化算法实现

遵循base-rules.md规范：
- 函数复杂度控制，每个函数圈复杂度不超过10
- 函数参数不超过5个
- 使用描述性的函数命名
"""

from typing import Dict, Any, List, Optional, Tuple
import numpy as np

from src.algorithms.base import BaseOptimizationAlgorithm, AlgorithmConfig
from src.algorithms.path_planning.dijkstra import GraphNode, GraphEdge


class DynamicProgrammingAlgorithm(BaseOptimizationAlgorithm):
    """
    动态规划路径优化算法
    
    使用动态规划方法解决多阶段决策路径优化问题：
    - 适用于具有阶段性特征的路径规划
    - 支持多目标优化和约束处理
    - 提供全局最优解保证
    """
    
    def __init__(self, config: AlgorithmConfig) -> None:
        """
        初始化动态规划算法
        
        Args:
            config: 算法配置参数
        """
        super().__init__(config)
        self.graph_nodes: Dict[str, GraphNode] = {}
        self.graph_edges: List[GraphEdge] = []
        self.stages: List[List[str]] = []  # 按阶段组织的节点
        self.stage_transitions: Dict[Tuple[str, str], float] = {}  # 阶段间转移成本
        self.start_node: Optional[str] = None
        self.end_node: Optional[str] = None
        self.dp_table: Dict[Tuple[int, str], float] = {}  # DP表：(阶段, 节点) -> 最小成本
        self.decision_table: Dict[Tuple[int, str], str] = {}  # 决策表：(阶段, 节点) -> 下一节点
        self.optimal_path: List[str] = []
        self.num_stages: int = 0
    
    def initialize_population(self, problem_definition: Dict[str, Any]) -> None:
        """
        初始化动态规划问题结构
        
        Args:
            problem_definition: 问题定义，包含阶段划分和转移成本
        """
        # 解析节点信息
        nodes_data = problem_definition.get("nodes", [])
        for node_data in nodes_data:
            node = GraphNode(
                node_id=node_data["id"],
                coordinates=(node_data["x"], node_data["y"]),
                node_type=node_data.get("type", "normal"),
                properties=node_data.get("properties", {})
            )
            self.graph_nodes[node.node_id] = node
        
        # 解析阶段划分
        stages_data = problem_definition.get("stages", [])
        if not stages_data:
            # 如果没有明确的阶段划分，按坐标自动划分
            self._auto_divide_stages()
        else:
            self.stages = stages_data
        
        self.num_stages = len(self.stages)
        
        # 解析转移成本
        transitions_data = problem_definition.get("transitions", [])
        for transition in transitions_data:
            from_node = transition["from"]
            to_node = transition["to"]
            cost = transition["cost"]
            self.stage_transitions[(from_node, to_node)] = cost
        
        # 如果没有明确的转移成本，使用边权重
        if not self.stage_transitions:
            self._build_transitions_from_edges(problem_definition.get("edges", []))
        
        # 设置起终点
        self.start_node = problem_definition.get("start_node")
        self.end_node = problem_definition.get("end_node")
        
        if not self.start_node or not self.end_node:
            raise ValueError("必须指定起点和终点")
    
    def _auto_divide_stages(self) -> None:
        """
        根据节点坐标自动划分阶段
        """
        if not self.graph_nodes:
            return
        
        # 按x坐标排序节点
        sorted_nodes = sorted(self.graph_nodes.items(), key=lambda x: x[1].coordinates[0])
        
        # 简单划分：按x坐标均匀分成若干阶段
        num_stages = min(5, len(sorted_nodes))  # 最多5个阶段
        nodes_per_stage = len(sorted_nodes) // num_stages
        
        self.stages = []
        for i in range(num_stages):
            start_idx = i * nodes_per_stage
            end_idx = start_idx + nodes_per_stage if i < num_stages - 1 else len(sorted_nodes)
            stage_nodes = [node_id for node_id, _ in sorted_nodes[start_idx:end_idx]]
            self.stages.append(stage_nodes)
    
    def _build_transitions_from_edges(self, edges_data: List[Dict[str, Any]]) -> None:
        """
        从边数据构建阶段转移成本
        
        Args:
            edges_data: 边数据列表
        """
        for edge_data in edges_data:
            from_node = edge_data["from"]
            to_node = edge_data["to"]
            weight = edge_data["weight"]
            
            # 检查是否为有效的阶段间转移
            if self._is_valid_stage_transition(from_node, to_node):
                self.stage_transitions[(from_node, to_node)] = weight
    
    def _is_valid_stage_transition(self, from_node: str, to_node: str) -> bool:
        """
        检查是否为有效的阶段间转移
        
        Args:
            from_node: 起始节点
            to_node: 目标节点
            
        Returns:
            bool: 是否有效
        """
        from_stage = self._get_node_stage(from_node)
        to_stage = self._get_node_stage(to_node)
        
        # 只允许相邻阶段间的转移
        return to_stage == from_stage + 1
    
    def _get_node_stage(self, node_id: str) -> int:
        """
        获取节点所属的阶段
        
        Args:
            node_id: 节点ID
            
        Returns:
            int: 阶段编号，-1表示未找到
        """
        for stage_idx, stage_nodes in enumerate(self.stages):
            if node_id in stage_nodes:
                return stage_idx
        return -1
    
    def evaluate_solution(self, solution: Dict[str, Any]) -> float:
        """
        评估路径的总成本
        
        Args:
            solution: 路径解
            
        Returns:
            float: 路径总成本
        """
        path = solution.get("path", [])
        if len(path) < 2:
            return float('inf')
        
        total_cost = 0.0
        for i in range(len(path) - 1):
            current_node = path[i]
            next_node = path[i + 1]
            
            transition_cost = self.stage_transitions.get((current_node, next_node))
            if transition_cost is None:
                return float('inf')
            
            total_cost += transition_cost
        
        return total_cost
    
    def generate_new_solutions(self) -> List[Dict[str, Any]]:
        """
        执行动态规划算法生成最优路径
        
        Returns:
            List[Dict[str, Any]]: 包含最优路径的解列表
        """
        # 初始化DP表和决策表
        self.dp_table = {}
        self.decision_table = {}
        
        # 从最后一个阶段开始反向计算
        self._backward_dynamic_programming()
        
        # 前向构造最优路径
        self.optimal_path = self._construct_optimal_path()
        
        if not self.optimal_path:
            return [{"path": [], "total_cost": float('inf'), "path_valid": False}]
        
        total_cost = self.dp_table.get((0, self.start_node), float('inf'))
        
        return [{
            "path": self.optimal_path,
            "total_cost": total_cost,
            "path_valid": len(self.optimal_path) > 1,
            "stages_count": self.num_stages
        }]
    
    def _backward_dynamic_programming(self) -> None:
        """执行反向动态规划计算"""
        # 初始化最后一个阶段
        last_stage = self.num_stages - 1
        for node in self.stages[last_stage]:
            if node == self.end_node:
                self.dp_table[(last_stage, node)] = 0.0
            else:
                self.dp_table[(last_stage, node)] = float('inf')
        
        # 从倒数第二个阶段开始反向计算
        for stage in range(last_stage - 1, -1, -1):
            for node in self.stages[stage]:
                min_cost = float('inf')
                best_next_node = None
                
                # 考虑所有可能的下一阶段节点
                for next_node in self.stages[stage + 1]:
                    transition_cost = self.stage_transitions.get((node, next_node))
                    if transition_cost is not None:
                        future_cost = self.dp_table.get((stage + 1, next_node), float('inf'))
                        total_cost = transition_cost + future_cost
                        
                        if total_cost < min_cost:
                            min_cost = total_cost
                            best_next_node = next_node
                
                self.dp_table[(stage, node)] = min_cost
                if best_next_node:
                    self.decision_table[(stage, node)] = best_next_node
    
    def _construct_optimal_path(self) -> List[str]:
        """
        构造最优路径
        
        Returns:
            List[str]: 最优路径节点序列
        """
        if (0, self.start_node) not in self.dp_table:
            return []
        
        if self.dp_table[(0, self.start_node)] == float('inf'):
            return []
        
        path = [self.start_node]
        current_node = self.start_node
        
        for stage in range(self.num_stages - 1):
            next_node = self.decision_table.get((stage, current_node))
            if next_node is None:
                return []  # 路径中断
            
            path.append(next_node)
            current_node = next_node
        
        return path
    
    def select_solutions(self, solutions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """选择最优解"""
        if not solutions:
            return []
        return [solutions[0]]
    
    def _get_best_solution(self) -> Dict[str, Any]:
        """获取当前最佳解"""
        total_cost = self.dp_table.get((0, self.start_node), float('inf'))
        
        return {
            "path": self.optimal_path,
            "total_cost": total_cost,
            "path_nodes": len(self.optimal_path),
            "path_valid": len(self.optimal_path) > 1,
            "stages_count": self.num_stages,
            "stage_details": [
                {
                    "stage": i,
                    "nodes": stage_nodes,
                    "selected_node": self.optimal_path[i] if i < len(self.optimal_path) else None
                }
                for i, stage_nodes in enumerate(self.stages)
            ]
        }
    
    def get_stage_analysis(self) -> Dict[str, Any]:
        """
        获取阶段分析结果
        
        Returns:
            Dict[str, Any]: 阶段分析数据
        """
        stage_costs = []
        for i in range(len(self.optimal_path) - 1):
            current_node = self.optimal_path[i]
            next_node = self.optimal_path[i + 1]
            cost = self.stage_transitions.get((current_node, next_node), 0.0)
            stage_costs.append(cost)
        
        return {
            "total_stages": self.num_stages,
            "stage_costs": stage_costs,
            "average_stage_cost": sum(stage_costs) / max(len(stage_costs), 1),
            "max_stage_cost": max(stage_costs) if stage_costs else 0.0,
            "min_stage_cost": min(stage_costs) if stage_costs else 0.0,
            "stages_distribution": [len(stage) for stage in self.stages]
        }
