"""
A*启发式搜索算法实现

遵循base-rules.md规范：
- 函数复杂度控制，每个函数圈复杂度不超过10
- 函数参数不超过5个
- 使用描述性的函数命名
"""

import heapq
import math
from typing import Dict, Any, List, Optional, Tuple, Set, Callable

from src.algorithms.base import BaseOptimizationAlgorithm, AlgorithmConfig
from src.algorithms.path_planning.dijkstra import GraphNode, GraphEdge


class AStarAlgorithm(BaseOptimizationAlgorithm):
    """
    A*启发式搜索算法
    
    实现A*算法用于最短路径搜索：
    - 使用启发式函数加速搜索
    - 支持多种距离度量方式
    - 保证找到最优解（在启发式函数可接受的条件下）
    """
    
    def __init__(self, config: AlgorithmConfig) -> None:
        """
        初始化A*算法
        
        Args:
            config: 算法配置参数
        """
        super().__init__(config)
        self.graph_nodes: Dict[str, GraphNode] = {}
        self.graph_edges: List[GraphEdge] = []
        self.adjacency_list: Dict[str, List[Tuple[str, float]]] = {}
        self.start_node: Optional[str] = None
        self.end_node: Optional[str] = None
        self.heuristic_function: Callable[[str, str], float] = self._euclidean_distance
        self.g_scores: Dict[str, float] = {}  # 从起点到节点的实际距离
        self.f_scores: Dict[str, float] = {}  # g + h 的总评估距离
        self.previous_nodes: Dict[str, Optional[str]] = {}
        self.optimal_path: List[str] = []
        self.nodes_explored: int = 0
    
    def initialize_population(self, problem_definition: Dict[str, Any]) -> None:
        """
        初始化图结构和启发式函数
        
        Args:
            problem_definition: 问题定义，包含图节点、边和启发式配置
        """
        # 解析节点信息
        nodes_data = problem_definition.get("nodes", [])
        for node_data in nodes_data:
            node = GraphNode(
                node_id=node_data["id"],
                coordinates=(node_data["x"], node_data["y"]),
                node_type=node_data.get("type", "normal"),
                properties=node_data.get("properties", {})
            )
            self.graph_nodes[node.node_id] = node
        
        # 解析边信息
        edges_data = problem_definition.get("edges", [])
        for edge_data in edges_data:
            edge = GraphEdge(
                from_node=edge_data["from"],
                to_node=edge_data["to"],
                weight=edge_data["weight"],
                edge_type=edge_data.get("type", "normal"),
                properties=edge_data.get("properties", {})
            )
            self.graph_edges.append(edge)
        
        # 构建邻接表
        self._build_adjacency_list()
        
        # 设置起终点
        self.start_node = problem_definition.get("start_node")
        self.end_node = problem_definition.get("end_node")
        
        # 设置启发式函数
        heuristic_type = problem_definition.get("heuristic", "euclidean")
        self._set_heuristic_function(heuristic_type)
        
        if not self.start_node or not self.end_node:
            raise ValueError("必须指定起点和终点")
    
    def _build_adjacency_list(self) -> None:
        """构建邻接表"""
        self.adjacency_list = {node_id: [] for node_id in self.graph_nodes.keys()}
        
        for edge in self.graph_edges:
            if self._is_edge_valid(edge):
                self.adjacency_list[edge.from_node].append((edge.to_node, edge.weight))
                
                # 如果是无向图，添加反向边
                if edge.edge_type == "undirected":
                    self.adjacency_list[edge.to_node].append((edge.from_node, edge.weight))
    
    def _is_edge_valid(self, edge: GraphEdge) -> bool:
        """检查边是否满足约束条件"""
        if edge.from_node not in self.graph_nodes or edge.to_node not in self.graph_nodes:
            return False
        
        if edge.weight <= 0:
            return False
        
        for constraint in self.constraints:
            if not constraint(edge):
                return False
        
        return True
    
    def _set_heuristic_function(self, heuristic_type: str) -> None:
        """
        设置启发式函数
        
        Args:
            heuristic_type: 启发式函数类型
        """
        if heuristic_type == "euclidean":
            self.heuristic_function = self._euclidean_distance
        elif heuristic_type == "manhattan":
            self.heuristic_function = self._manhattan_distance
        elif heuristic_type == "chebyshev":
            self.heuristic_function = self._chebyshev_distance
        else:
            self.heuristic_function = self._euclidean_distance
    
    def _euclidean_distance(self, node1: str, node2: str) -> float:
        """
        计算欧几里得距离
        
        Args:
            node1: 节点1 ID
            node2: 节点2 ID
            
        Returns:
            float: 欧几里得距离
        """
        if node1 not in self.graph_nodes or node2 not in self.graph_nodes:
            return float('inf')
        
        coord1 = self.graph_nodes[node1].coordinates
        coord2 = self.graph_nodes[node2].coordinates
        
        return math.sqrt((coord1[0] - coord2[0])**2 + (coord1[1] - coord2[1])**2)
    
    def _manhattan_distance(self, node1: str, node2: str) -> float:
        """计算曼哈顿距离"""
        if node1 not in self.graph_nodes or node2 not in self.graph_nodes:
            return float('inf')
        
        coord1 = self.graph_nodes[node1].coordinates
        coord2 = self.graph_nodes[node2].coordinates
        
        return abs(coord1[0] - coord2[0]) + abs(coord1[1] - coord2[1])
    
    def _chebyshev_distance(self, node1: str, node2: str) -> float:
        """计算切比雪夫距离"""
        if node1 not in self.graph_nodes or node2 not in self.graph_nodes:
            return float('inf')
        
        coord1 = self.graph_nodes[node1].coordinates
        coord2 = self.graph_nodes[node2].coordinates
        
        return max(abs(coord1[0] - coord2[0]), abs(coord1[1] - coord2[1]))
    
    def evaluate_solution(self, solution: Dict[str, Any]) -> float:
        """
        评估路径的总成本
        
        Args:
            solution: 路径解
            
        Returns:
            float: 路径总成本
        """
        path = solution.get("path", [])
        if len(path) < 2:
            return float('inf')
        
        total_cost = 0.0
        for i in range(len(path) - 1):
            current_node = path[i]
            next_node = path[i + 1]
            
            edge_weight = self._get_edge_weight(current_node, next_node)
            if edge_weight is None:
                return float('inf')
            
            total_cost += edge_weight
        
        return total_cost
    
    def _get_edge_weight(self, from_node: str, to_node: str) -> Optional[float]:
        """获取两个节点间的边权重"""
        if from_node not in self.adjacency_list:
            return None
        
        for neighbor, weight in self.adjacency_list[from_node]:
            if neighbor == to_node:
                return weight
        
        return None
    
    def generate_new_solutions(self) -> List[Dict[str, Any]]:
        """
        执行A*算法生成最短路径
        
        Returns:
            List[Dict[str, Any]]: 包含最优路径的解列表
        """
        # 初始化评分
        self.g_scores = {node: float('inf') for node in self.graph_nodes.keys()}
        self.f_scores = {node: float('inf') for node in self.graph_nodes.keys()}
        self.previous_nodes = {node: None for node in self.graph_nodes.keys()}
        
        self.g_scores[self.start_node] = 0.0
        self.f_scores[self.start_node] = self.heuristic_function(self.start_node, self.end_node)
        
        # 开放列表：(f_score, node_id)
        open_set = [(self.f_scores[self.start_node], self.start_node)]
        closed_set: Set[str] = set()
        self.nodes_explored = 0
        
        while open_set:
            current_f_score, current_node = heapq.heappop(open_set)
            
            if current_node in closed_set:
                continue
            
            closed_set.add(current_node)
            self.nodes_explored += 1
            
            # 到达目标节点
            if current_node == self.end_node:
                self.optimal_path = self._reconstruct_path()
                break
            
            # 探索邻居节点
            self._explore_neighbors(current_node, open_set, closed_set)
        
        if not self.optimal_path:
            return [{"path": [], "total_distance": float('inf'), "path_valid": False}]
        
        return [{
            "path": self.optimal_path,
            "total_distance": self.g_scores.get(self.end_node, float('inf')),
            "path_valid": len(self.optimal_path) > 1,
            "nodes_explored": self.nodes_explored
        }]
    
    def _explore_neighbors(self, current_node: str, open_set: List[Tuple[float, str]], 
                          closed_set: Set[str]) -> None:
        """
        探索当前节点的邻居
        
        Args:
            current_node: 当前节点
            open_set: 开放列表
            closed_set: 关闭列表
        """
        if current_node not in self.adjacency_list:
            return
        
        for neighbor, edge_weight in self.adjacency_list[current_node]:
            if neighbor in closed_set:
                continue
            
            tentative_g_score = self.g_scores[current_node] + edge_weight
            
            if tentative_g_score < self.g_scores[neighbor]:
                self.previous_nodes[neighbor] = current_node
                self.g_scores[neighbor] = tentative_g_score
                self.f_scores[neighbor] = tentative_g_score + self.heuristic_function(neighbor, self.end_node)
                
                heapq.heappush(open_set, (self.f_scores[neighbor], neighbor))
    
    def _reconstruct_path(self) -> List[str]:
        """重构最短路径"""
        if self.g_scores[self.end_node] == float('inf'):
            return []
        
        path = []
        current_node = self.end_node
        
        while current_node is not None:
            path.append(current_node)
            current_node = self.previous_nodes[current_node]
        
        path.reverse()
        return path
    
    def select_solutions(self, solutions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """选择最优解"""
        if not solutions:
            return []
        return [solutions[0]]
    
    def _get_best_solution(self) -> Dict[str, Any]:
        """获取当前最佳解"""
        return {
            "path": self.optimal_path,
            "total_distance": self.g_scores.get(self.end_node, float('inf')),
            "path_nodes": len(self.optimal_path),
            "path_valid": len(self.optimal_path) > 1,
            "nodes_explored": self.nodes_explored,
            "search_efficiency": len(self.optimal_path) / max(self.nodes_explored, 1),
            "node_details": [
                {
                    "node_id": node_id,
                    "coordinates": self.graph_nodes[node_id].coordinates,
                    "g_score": self.g_scores.get(node_id, float('inf')),
                    "f_score": self.f_scores.get(node_id, float('inf'))
                }
                for node_id in self.optimal_path
            ]
        }
