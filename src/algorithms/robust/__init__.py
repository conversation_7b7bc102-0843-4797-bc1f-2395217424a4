"""
鲁棒优化算法模块

遵循base-rules.md规范：
- 模块职责单一性：只包含鲁棒优化算法相关的功能
- 统一的算法接口
"""

from src.algorithms.robust.worst_case import WorstCaseOptimization
from src.algorithms.robust.stochastic_programming import StochasticProgramming
from src.algorithms.robust.distributionally_robust import DistributionallyRobustOptimization

__all__ = [
    "WorstCaseOptimization",
    "StochasticProgramming",
    "DistributionallyRobustOptimization"
]
