"""
随机规划算法实现

遵循base-rules.md规范：
- 函数复杂度控制，每个函数圈复杂度不超过10
- 函数参数不超过5个
- 使用描述性的函数命名
"""

import numpy as np
from typing import Dict, Any, List, Optional, Callable, Tuple
from dataclasses import dataclass
from scipy import stats

from src.algorithms.base import BaseOptimizationAlgorithm, AlgorithmConfig, OptimizationResult


@dataclass
class StochasticScenario:
    """随机场景数据类"""
    scenario_id: str
    parameters: np.ndarray  # 随机参数值
    probability: float  # 场景概率
    objective_value: float = 0.0  # 场景目标值


@dataclass
class StochasticSolution:
    """随机规划解数据类"""
    first_stage_variables: np.ndarray  # 第一阶段决策变量
    second_stage_variables: Dict[str, np.ndarray]  # 第二阶段决策变量（按场景）
    expected_objective: float  # 期望目标值
    objective_variance: float  # 目标值方差
    value_at_risk: float = 0.0  # 风险价值
    conditional_value_at_risk: float = 0.0  # 条件风险价值


class StochasticProgramming(BaseOptimizationAlgorithm):
    """
    随机规划算法实现
    
    实现两阶段随机规划方法：
    - 支持多种概率分布
    - 场景生成和缩减
    - 风险度量优化
    """
    
    def __init__(self, config: AlgorithmConfig) -> None:
        """
        初始化随机规划算法
        
        Args:
            config: 算法配置参数
        """
        super().__init__(config)
        
        # 随机规划参数
        self.num_scenarios = config.algorithm_params.get("num_scenarios", 100)
        self.confidence_level = config.algorithm_params.get("confidence_level", 0.95)
        self.risk_measure = config.algorithm_params.get("risk_measure", "expected_value")
        self.scenario_generation_method = config.algorithm_params.get("scenario_generation", "monte_carlo")
        self.scenario_reduction = config.algorithm_params.get("scenario_reduction", False)
        
        # 算法状态
        self.scenarios: List[StochasticScenario] = []
        self.current_solution: Optional[StochasticSolution] = None
        self.best_solution: Optional[StochasticSolution] = None
        
        # 问题相关参数
        self.first_stage_dimension = 0
        self.second_stage_dimension = 0
        self.first_stage_bounds = []
        self.second_stage_bounds = []
        self.random_parameters_distribution = {}
    
    def initialize_population(self, problem_definition: Dict[str, Any]) -> None:
        """
        初始化随机规划问题
        
        Args:
            problem_definition: 问题定义
        """
        # 解析问题维度
        self.first_stage_dimension = problem_definition.get("first_stage_dimension", 3)
        self.second_stage_dimension = problem_definition.get("second_stage_dimension", 2)
        
        # 解析变量边界
        self.first_stage_bounds = problem_definition.get(
            "first_stage_bounds", [(0, 1)] * self.first_stage_dimension
        )
        self.second_stage_bounds = problem_definition.get(
            "second_stage_bounds", [(0, 1)] * self.second_stage_dimension
        )
        
        # 解析随机参数分布
        self.random_parameters_distribution = problem_definition.get(
            "random_parameters_distribution", {
                "demand": {"type": "normal", "params": {"mean": 100, "std": 20}},
                "cost": {"type": "uniform", "params": {"low": 0.8, "high": 1.2}}
            }
        )
        
        # 生成场景
        self.scenarios = self._generate_scenarios()
        
        # 场景缩减（如果启用）
        if self.scenario_reduction and len(self.scenarios) > self.num_scenarios:
            self.scenarios = self._reduce_scenarios(self.scenarios, self.num_scenarios)
        
        # 生成初始解
        initial_first_stage = self._generate_random_first_stage()
        initial_solution = self._evaluate_stochastic_solution(initial_first_stage)
        
        self.current_solution = initial_solution
        self.best_solution = initial_solution
    
    def _generate_scenarios(self) -> List[StochasticScenario]:
        """
        生成随机场景
        
        Returns:
            List[StochasticScenario]: 场景列表
        """
        scenarios = []
        
        for i in range(self.num_scenarios):
            # 从各个随机参数分布中采样
            parameters = {}
            
            for param_name, distribution_info in self.random_parameters_distribution.items():
                dist_type = distribution_info["type"]
                dist_params = distribution_info["params"]
                
                if dist_type == "normal":
                    value = np.random.normal(dist_params["mean"], dist_params["std"])
                elif dist_type == "uniform":
                    value = np.random.uniform(dist_params["low"], dist_params["high"])
                elif dist_type == "exponential":
                    value = np.random.exponential(dist_params["scale"])
                else:
                    value = 1.0  # 默认值
                
                parameters[param_name] = value
            
            # 创建场景
            scenario = StochasticScenario(
                scenario_id=f"scenario_{i}",
                parameters=np.array(list(parameters.values())),
                probability=1.0 / self.num_scenarios  # 等概率
            )
            
            scenarios.append(scenario)
        
        return scenarios
    
    def _reduce_scenarios(self, scenarios: List[StochasticScenario], target_count: int) -> List[StochasticScenario]:
        """
        场景缩减
        
        Args:
            scenarios: 原始场景列表
            target_count: 目标场景数量
            
        Returns:
            List[StochasticScenario]: 缩减后的场景列表
        """
        # 简化实现：随机选择场景
        # 实际应该使用更复杂的场景缩减算法
        selected_indices = np.random.choice(len(scenarios), target_count, replace=False)
        
        reduced_scenarios = []
        for idx in selected_indices:
            scenario = scenarios[idx]
            # 调整概率
            scenario.probability = 1.0 / target_count
            reduced_scenarios.append(scenario)
        
        return reduced_scenarios
    
    def _generate_random_first_stage(self) -> np.ndarray:
        """
        生成随机第一阶段决策变量
        
        Returns:
            np.ndarray: 第一阶段决策变量
        """
        first_stage = np.zeros(self.first_stage_dimension)
        
        for i in range(self.first_stage_dimension):
            lower, upper = self.first_stage_bounds[i]
            first_stage[i] = np.random.uniform(lower, upper)
        
        return first_stage
    
    def _evaluate_stochastic_solution(self, first_stage_variables: np.ndarray) -> StochasticSolution:
        """
        评估随机规划解
        
        Args:
            first_stage_variables: 第一阶段决策变量
            
        Returns:
            StochasticSolution: 随机规划解评估结果
        """
        second_stage_variables = {}
        scenario_objectives = []
        
        # 对每个场景求解第二阶段问题
        for scenario in self.scenarios:
            second_stage_solution = self._solve_second_stage(first_stage_variables, scenario)
            second_stage_variables[scenario.scenario_id] = second_stage_solution
            
            # 计算场景目标值
            objective_value = self._evaluate_scenario_objective(
                first_stage_variables, second_stage_solution, scenario
            )
            scenario.objective_value = objective_value
            scenario_objectives.append(objective_value)
        
        # 计算期望目标值
        expected_objective = sum(
            scenario.probability * scenario.objective_value 
            for scenario in self.scenarios
        )
        
        # 计算目标值方差
        objective_variance = sum(
            scenario.probability * (scenario.objective_value - expected_objective) ** 2
            for scenario in self.scenarios
        )
        
        # 计算风险度量
        var, cvar = self._calculate_risk_measures(scenario_objectives)
        
        return StochasticSolution(
            first_stage_variables=first_stage_variables,
            second_stage_variables=second_stage_variables,
            expected_objective=expected_objective,
            objective_variance=objective_variance,
            value_at_risk=var,
            conditional_value_at_risk=cvar
        )
    
    def _solve_second_stage(self, 
                           first_stage_variables: np.ndarray,
                           scenario: StochasticScenario) -> np.ndarray:
        """
        求解第二阶段子问题
        
        Args:
            first_stage_variables: 第一阶段决策变量
            scenario: 随机场景
            
        Returns:
            np.ndarray: 第二阶段决策变量
        """
        # 简化实现：使用启发式方法求解第二阶段
        # 实际应该使用线性规划或其他优化方法
        
        second_stage = np.zeros(self.second_stage_dimension)
        
        for i in range(self.second_stage_dimension):
            lower, upper = self.second_stage_bounds[i]
            
            # 基于第一阶段变量和随机参数的简单启发式
            if i < len(first_stage_variables):
                base_value = first_stage_variables[i]
            else:
                base_value = 0.5
            
            # 考虑随机参数的影响
            if len(scenario.parameters) > 0:
                random_factor = scenario.parameters[0] / 100.0  # 标准化
                adjusted_value = base_value * (1 + random_factor)
            else:
                adjusted_value = base_value
            
            second_stage[i] = np.clip(adjusted_value, lower, upper)
        
        return second_stage
    
    def _evaluate_scenario_objective(self, 
                                   first_stage_variables: np.ndarray,
                                   second_stage_variables: np.ndarray,
                                   scenario: StochasticScenario) -> float:
        """
        评估场景目标函数
        
        Args:
            first_stage_variables: 第一阶段决策变量
            second_stage_variables: 第二阶段决策变量
            scenario: 随机场景
            
        Returns:
            float: 场景目标值
        """
        if self.objective_function is None:
            return 0.0
        
        solution = {
            "first_stage_variables": first_stage_variables.tolist(),
            "second_stage_variables": second_stage_variables.tolist(),
            "scenario_parameters": scenario.parameters.tolist(),
            "scenario_id": scenario.scenario_id
        }
        
        return self.objective_function(solution)
    
    def _calculate_risk_measures(self, scenario_objectives: List[float]) -> Tuple[float, float]:
        """
        计算风险度量
        
        Args:
            scenario_objectives: 场景目标值列表
            
        Returns:
            Tuple[float, float]: VaR和CVaR值
        """
        sorted_objectives = sorted(scenario_objectives, reverse=True)  # 降序排列
        
        # 计算VaR（Value at Risk）
        var_index = int((1 - self.confidence_level) * len(sorted_objectives))
        var = sorted_objectives[var_index] if var_index < len(sorted_objectives) else sorted_objectives[-1]
        
        # 计算CVaR（Conditional Value at Risk）
        tail_objectives = sorted_objectives[:var_index + 1]
        cvar = np.mean(tail_objectives) if tail_objectives else var
        
        return var, cvar
    
    def evaluate_solution(self, solution: Dict[str, Any]) -> float:
        """
        评估解的随机目标值
        
        Args:
            solution: 解
            
        Returns:
            float: 随机目标值
        """
        if "first_stage_variables" in solution:
            first_stage_variables = np.array(solution["first_stage_variables"])
        else:
            return float('inf')
        
        stochastic_solution = self._evaluate_stochastic_solution(first_stage_variables)
        
        # 根据风险度量选择返回值
        if self.risk_measure == "expected_value":
            return stochastic_solution.expected_objective
        elif self.risk_measure == "var":
            return stochastic_solution.value_at_risk
        elif self.risk_measure == "cvar":
            return stochastic_solution.conditional_value_at_risk
        else:
            return stochastic_solution.expected_objective
    
    def generate_new_solutions(self) -> List[Dict[str, Any]]:
        """
        生成新的候选解
        
        Returns:
            List[Dict[str, Any]]: 新候选解列表
        """
        candidate_solutions = []
        
        for _ in range(5):  # 生成5个候选解
            # 在当前解附近生成新解
            perturbation = np.random.normal(0, 0.1, self.first_stage_dimension)
            new_first_stage = self.current_solution.first_stage_variables + perturbation
            
            # 应用边界约束
            for i in range(self.first_stage_dimension):
                lower, upper = self.first_stage_bounds[i]
                new_first_stage[i] = np.clip(new_first_stage[i], lower, upper)
            
            # 评估新解
            new_solution = self._evaluate_stochastic_solution(new_first_stage)
            
            candidate_solutions.append({
                "first_stage_variables": new_first_stage.tolist(),
                "expected_objective": new_solution.expected_objective,
                "objective_variance": new_solution.objective_variance,
                "value_at_risk": new_solution.value_at_risk,
                "conditional_value_at_risk": new_solution.conditional_value_at_risk
            })
        
        return candidate_solutions
    
    def select_solutions(self, solutions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """选择最优解"""
        if not solutions:
            return []
        
        # 根据风险度量选择最优解
        if self.risk_measure == "expected_value":
            best_solution = min(solutions, key=lambda x: x["expected_objective"])
        elif self.risk_measure == "var":
            best_solution = min(solutions, key=lambda x: x["value_at_risk"])
        elif self.risk_measure == "cvar":
            best_solution = min(solutions, key=lambda x: x["conditional_value_at_risk"])
        else:
            best_solution = min(solutions, key=lambda x: x["expected_objective"])
        
        # 更新当前解
        first_stage_variables = np.array(best_solution["first_stage_variables"])
        self.current_solution = self._evaluate_stochastic_solution(first_stage_variables)
        
        # 更新最佳解
        current_objective = getattr(self.current_solution, f"{self.risk_measure.replace('_', '_')}", 
                                  self.current_solution.expected_objective)
        best_objective = getattr(self.best_solution, f"{self.risk_measure.replace('_', '_')}", 
                               self.best_solution.expected_objective)
        
        if current_objective < best_objective:
            self.best_solution = self.current_solution
        
        return [best_solution]
    
    def _get_best_solution(self) -> Dict[str, Any]:
        """获取当前最佳解"""
        if self.best_solution is None:
            return {"first_stage_variables": [], "expected_objective": float('inf')}
        
        return {
            "first_stage_variables": self.best_solution.first_stage_variables.tolist(),
            "expected_objective": self.best_solution.expected_objective,
            "objective_variance": self.best_solution.objective_variance,
            "objective_std": np.sqrt(self.best_solution.objective_variance),
            "value_at_risk": self.best_solution.value_at_risk,
            "conditional_value_at_risk": self.best_solution.conditional_value_at_risk,
            "num_scenarios": len(self.scenarios),
            "confidence_level": self.confidence_level,
            "risk_measure": self.risk_measure
        }
    
    def get_scenario_analysis(self) -> Dict[str, Any]:
        """
        获取场景分析结果
        
        Returns:
            Dict[str, Any]: 场景分析数据
        """
        if not self.scenarios:
            return {}
        
        scenario_objectives = [scenario.objective_value for scenario in self.scenarios]
        
        return {
            "num_scenarios": len(self.scenarios),
            "objective_statistics": {
                "mean": np.mean(scenario_objectives),
                "std": np.std(scenario_objectives),
                "min": np.min(scenario_objectives),
                "max": np.max(scenario_objectives),
                "median": np.median(scenario_objectives)
            },
            "risk_measures": {
                "value_at_risk": self.best_solution.value_at_risk if self.best_solution else 0.0,
                "conditional_value_at_risk": self.best_solution.conditional_value_at_risk if self.best_solution else 0.0,
                "confidence_level": self.confidence_level
            },
            "scenario_distribution": {
                "percentiles": {
                    "5%": np.percentile(scenario_objectives, 5),
                    "25%": np.percentile(scenario_objectives, 25),
                    "75%": np.percentile(scenario_objectives, 75),
                    "95%": np.percentile(scenario_objectives, 95)
                }
            }
        }
