"""
最坏情况鲁棒优化算法实现

遵循base-rules.md规范：
- 函数复杂度控制，每个函数圈复杂度不超过10
- 函数参数不超过5个
- 使用描述性的函数命名
"""

import numpy as np
from typing import Dict, Any, List, Optional, Callable, Tuple
from dataclasses import dataclass

from src.algorithms.base import BaseOptimizationAlgorithm, AlgorithmConfig, OptimizationResult


@dataclass
class UncertaintySet:
    """不确定性集合数据类"""
    parameter_names: List[str]  # 不确定参数名称
    nominal_values: np.ndarray  # 标称值
    uncertainty_bounds: List[Tuple[float, float]]  # 不确定性边界
    uncertainty_type: str = "box"  # 不确定性集合类型
    correlation_matrix: Optional[np.ndarray] = None  # 相关性矩阵


@dataclass
class RobustSolution:
    """鲁棒解数据类"""
    decision_variables: np.ndarray  # 决策变量
    worst_case_objective: float  # 最坏情况目标值
    worst_case_scenario: np.ndarray  # 最坏情况参数
    robustness_measure: float  # 鲁棒性度量
    feasibility_probability: float = 1.0  # 可行性概率


class WorstCaseOptimization(BaseOptimizationAlgorithm):
    """
    最坏情况鲁棒优化算法
    
    实现基于最坏情况的鲁棒优化方法：
    - 支持多种不确定性集合
    - 嵌套优化求解
    - 鲁棒性度量和分析
    """
    
    def __init__(self, config: AlgorithmConfig) -> None:
        """
        初始化最坏情况鲁棒优化算法
        
        Args:
            config: 算法配置参数
        """
        super().__init__(config)
        
        # 鲁棒优化参数
        self.robustness_level = config.algorithm_params.get("robustness_level", 0.1)
        self.max_inner_iterations = config.algorithm_params.get("max_inner_iterations", 100)
        self.inner_tolerance = config.algorithm_params.get("inner_tolerance", 1e-4)
        self.uncertainty_budget = config.algorithm_params.get("uncertainty_budget", 1.0)
        
        # 算法状态
        self.uncertainty_set: Optional[UncertaintySet] = None
        self.current_solution: Optional[RobustSolution] = None
        self.best_solution: Optional[RobustSolution] = None
        self.iteration_history: List[Dict[str, Any]] = []
        
        # 问题相关参数
        self.decision_dimension = 0
        self.uncertainty_dimension = 0
        self.decision_bounds = []
    
    def initialize_population(self, problem_definition: Dict[str, Any]) -> None:
        """
        初始化鲁棒优化问题
        
        Args:
            problem_definition: 问题定义
        """
        # 解析决策变量
        self.decision_dimension = problem_definition.get("decision_dimension", 5)
        self.decision_bounds = problem_definition.get("decision_bounds", [(0, 1)] * self.decision_dimension)
        
        # 解析不确定性集合
        uncertainty_def = problem_definition.get("uncertainty_set", {})
        self.uncertainty_set = UncertaintySet(
            parameter_names=uncertainty_def.get("parameter_names", [f"u_{i}" for i in range(3)]),
            nominal_values=np.array(uncertainty_def.get("nominal_values", [1.0, 1.0, 1.0])),
            uncertainty_bounds=uncertainty_def.get("uncertainty_bounds", [(-0.1, 0.1)] * 3),
            uncertainty_type=uncertainty_def.get("uncertainty_type", "box")
        )
        
        self.uncertainty_dimension = len(self.uncertainty_set.parameter_names)
        
        # 生成初始解
        initial_decision = self._generate_random_decision()
        initial_solution = self._evaluate_robust_solution(initial_decision)
        
        self.current_solution = initial_solution
        self.best_solution = initial_solution
    
    def _generate_random_decision(self) -> np.ndarray:
        """
        生成随机决策变量
        
        Returns:
            np.ndarray: 随机决策变量
        """
        decision = np.zeros(self.decision_dimension)
        for i in range(self.decision_dimension):
            lower, upper = self.decision_bounds[i]
            decision[i] = np.random.uniform(lower, upper)
        
        return decision
    
    def _evaluate_robust_solution(self, decision_variables: np.ndarray) -> RobustSolution:
        """
        评估鲁棒解
        
        Args:
            decision_variables: 决策变量
            
        Returns:
            RobustSolution: 鲁棒解评估结果
        """
        # 求解最坏情况子问题
        worst_case_scenario, worst_case_objective = self._solve_worst_case_subproblem(decision_variables)
        
        # 计算鲁棒性度量
        robustness_measure = self._calculate_robustness_measure(decision_variables, worst_case_scenario)
        
        return RobustSolution(
            decision_variables=decision_variables,
            worst_case_objective=worst_case_objective,
            worst_case_scenario=worst_case_scenario,
            robustness_measure=robustness_measure
        )
    
    def _solve_worst_case_subproblem(self, decision_variables: np.ndarray) -> Tuple[np.ndarray, float]:
        """
        求解最坏情况子问题
        
        Args:
            decision_variables: 固定的决策变量
            
        Returns:
            Tuple[np.ndarray, float]: 最坏情况参数和目标值
        """
        best_worst_case_objective = -float('inf')  # 最大化问题
        best_worst_case_scenario = self.uncertainty_set.nominal_values.copy()
        
        # 使用网格搜索求解子问题（简化实现）
        num_samples = self.max_inner_iterations
        
        for _ in range(num_samples):
            # 在不确定性集合中采样
            uncertain_scenario = self._sample_from_uncertainty_set()
            
            # 评估目标函数
            objective_value = self._evaluate_objective_under_uncertainty(
                decision_variables, uncertain_scenario
            )
            
            # 更新最坏情况
            if objective_value > best_worst_case_objective:
                best_worst_case_objective = objective_value
                best_worst_case_scenario = uncertain_scenario
        
        return best_worst_case_scenario, best_worst_case_objective
    
    def _sample_from_uncertainty_set(self) -> np.ndarray:
        """
        从不确定性集合中采样
        
        Returns:
            np.ndarray: 不确定参数样本
        """
        if self.uncertainty_set.uncertainty_type == "box":
            return self._sample_from_box_set()
        elif self.uncertainty_set.uncertainty_type == "ellipsoidal":
            return self._sample_from_ellipsoidal_set()
        else:
            return self.uncertainty_set.nominal_values.copy()
    
    def _sample_from_box_set(self) -> np.ndarray:
        """从盒式不确定性集合中采样"""
        sample = self.uncertainty_set.nominal_values.copy()
        
        for i, (lower_bound, upper_bound) in enumerate(self.uncertainty_set.uncertainty_bounds):
            perturbation = np.random.uniform(lower_bound, upper_bound)
            sample[i] += perturbation
        
        return sample
    
    def _sample_from_ellipsoidal_set(self) -> np.ndarray:
        """从椭球不确定性集合中采样"""
        # 简化实现：生成单位球面上的随机点，然后缩放
        random_direction = np.random.randn(self.uncertainty_dimension)
        random_direction /= np.linalg.norm(random_direction)
        
        # 随机半径
        random_radius = np.random.uniform(0, self.uncertainty_budget)
        
        # 计算扰动
        perturbation = random_radius * random_direction
        
        # 应用边界约束
        sample = self.uncertainty_set.nominal_values.copy()
        for i in range(self.uncertainty_dimension):
            lower_bound, upper_bound = self.uncertainty_set.uncertainty_bounds[i]
            sample[i] += np.clip(perturbation[i], lower_bound, upper_bound)
        
        return sample
    
    def _evaluate_objective_under_uncertainty(self, 
                                            decision_variables: np.ndarray,
                                            uncertain_parameters: np.ndarray) -> float:
        """
        在不确定参数下评估目标函数
        
        Args:
            decision_variables: 决策变量
            uncertain_parameters: 不确定参数
            
        Returns:
            float: 目标函数值
        """
        if self.objective_function is None:
            return 0.0
        
        solution = {
            "decision_variables": decision_variables.tolist(),
            "uncertain_parameters": uncertain_parameters.tolist()
        }
        
        return self.objective_function(solution)
    
    def _calculate_robustness_measure(self, 
                                    decision_variables: np.ndarray,
                                    worst_case_scenario: np.ndarray) -> float:
        """
        计算鲁棒性度量
        
        Args:
            decision_variables: 决策变量
            worst_case_scenario: 最坏情况参数
            
        Returns:
            float: 鲁棒性度量值
        """
        # 计算标称情况下的目标值
        nominal_objective = self._evaluate_objective_under_uncertainty(
            decision_variables, self.uncertainty_set.nominal_values
        )
        
        # 计算最坏情况下的目标值
        worst_case_objective = self._evaluate_objective_under_uncertainty(
            decision_variables, worst_case_scenario
        )
        
        # 鲁棒性度量：相对性能损失
        if abs(nominal_objective) > 1e-10:
            robustness = abs(worst_case_objective - nominal_objective) / abs(nominal_objective)
        else:
            robustness = abs(worst_case_objective - nominal_objective)
        
        return robustness
    
    def evaluate_solution(self, solution: Dict[str, Any]) -> float:
        """
        评估解的鲁棒目标值
        
        Args:
            solution: 解
            
        Returns:
            float: 鲁棒目标值（最坏情况目标值）
        """
        if "decision_variables" in solution:
            decision_variables = np.array(solution["decision_variables"])
        else:
            return float('inf')
        
        robust_solution = self._evaluate_robust_solution(decision_variables)
        return robust_solution.worst_case_objective
    
    def generate_new_solutions(self) -> List[Dict[str, Any]]:
        """
        生成新的候选解
        
        Returns:
            List[Dict[str, Any]]: 新候选解列表
        """
        # 使用简单的随机搜索策略
        candidate_solutions = []
        
        for _ in range(10):  # 生成10个候选解
            # 在当前解附近生成新解
            perturbation = np.random.normal(0, 0.1, self.decision_dimension)
            new_decision = self.current_solution.decision_variables + perturbation
            
            # 应用边界约束
            for i in range(self.decision_dimension):
                lower, upper = self.decision_bounds[i]
                new_decision[i] = np.clip(new_decision[i], lower, upper)
            
            # 评估新解
            new_solution = self._evaluate_robust_solution(new_decision)
            
            candidate_solutions.append({
                "decision_variables": new_decision.tolist(),
                "worst_case_objective": new_solution.worst_case_objective,
                "robustness_measure": new_solution.robustness_measure
            })
        
        return candidate_solutions
    
    def select_solutions(self, solutions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        选择最优解
        
        Args:
            solutions: 候选解列表
            
        Returns:
            List[Dict[str, Any]]: 选择的解列表
        """
        if not solutions:
            return []
        
        # 选择最坏情况目标值最小的解
        best_solution = min(solutions, key=lambda x: x["worst_case_objective"])
        
        # 更新当前解
        decision_variables = np.array(best_solution["decision_variables"])
        self.current_solution = self._evaluate_robust_solution(decision_variables)
        
        # 更新最佳解
        if self.current_solution.worst_case_objective < self.best_solution.worst_case_objective:
            self.best_solution = self.current_solution
        
        return [best_solution]
    
    def _get_best_solution(self) -> Dict[str, Any]:
        """获取当前最佳解"""
        if self.best_solution is None:
            return {"decision_variables": [], "worst_case_objective": float('inf')}
        
        return {
            "decision_variables": self.best_solution.decision_variables.tolist(),
            "worst_case_objective": self.best_solution.worst_case_objective,
            "worst_case_scenario": self.best_solution.worst_case_scenario.tolist(),
            "robustness_measure": self.best_solution.robustness_measure,
            "feasibility_probability": self.best_solution.feasibility_probability,
            "uncertainty_set_info": {
                "parameter_names": self.uncertainty_set.parameter_names,
                "nominal_values": self.uncertainty_set.nominal_values.tolist(),
                "uncertainty_bounds": self.uncertainty_set.uncertainty_bounds,
                "uncertainty_type": self.uncertainty_set.uncertainty_type
            }
        }
    
    def get_robustness_analysis(self) -> Dict[str, Any]:
        """
        获取鲁棒性分析结果
        
        Returns:
            Dict[str, Any]: 鲁棒性分析数据
        """
        if self.best_solution is None:
            return {}
        
        # 敏感性分析
        sensitivity_analysis = self._perform_sensitivity_analysis()
        
        # 不确定性影响分析
        uncertainty_impact = self._analyze_uncertainty_impact()
        
        return {
            "robustness_measure": self.best_solution.robustness_measure,
            "worst_case_scenario": self.best_solution.worst_case_scenario.tolist(),
            "sensitivity_analysis": sensitivity_analysis,
            "uncertainty_impact": uncertainty_impact,
            "robustness_level": self.robustness_level,
            "uncertainty_budget": self.uncertainty_budget
        }
    
    def _perform_sensitivity_analysis(self) -> Dict[str, Any]:
        """执行敏感性分析"""
        # 简化的敏感性分析实现
        sensitivities = {}
        
        for i, param_name in enumerate(self.uncertainty_set.parameter_names):
            # 计算参数扰动对目标函数的影响
            base_scenario = self.uncertainty_set.nominal_values.copy()
            perturbed_scenario = base_scenario.copy()
            
            # 小幅扰动
            perturbation = 0.01
            perturbed_scenario[i] += perturbation
            
            base_objective = self._evaluate_objective_under_uncertainty(
                self.best_solution.decision_variables, base_scenario
            )
            perturbed_objective = self._evaluate_objective_under_uncertainty(
                self.best_solution.decision_variables, perturbed_scenario
            )
            
            sensitivity = (perturbed_objective - base_objective) / perturbation
            sensitivities[param_name] = sensitivity
        
        return sensitivities
    
    def _analyze_uncertainty_impact(self) -> Dict[str, Any]:
        """分析不确定性影响"""
        # 计算各不确定参数的影响程度
        impact_analysis = {}
        
        for i, param_name in enumerate(self.uncertainty_set.parameter_names):
            lower_bound, upper_bound = self.uncertainty_set.uncertainty_bounds[i]
            uncertainty_range = upper_bound - lower_bound
            
            impact_analysis[param_name] = {
                "uncertainty_range": uncertainty_range,
                "relative_impact": uncertainty_range / (abs(self.uncertainty_set.nominal_values[i]) + 1e-10)
            }
        
        return impact_analysis
