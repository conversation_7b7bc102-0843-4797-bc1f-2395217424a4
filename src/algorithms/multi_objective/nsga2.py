"""
NSGA-II多目标优化算法实现

遵循base-rules.md规范：
- 函数复杂度控制，每个函数圈复杂度不超过10
- 函数参数不超过5个
- 使用描述性的函数命名
"""

import random
import numpy as np
from typing import Dict, Any, List, Optional, Tuple, Callable
from dataclasses import dataclass

from src.algorithms.base import BaseOptimizationAlgorithm, AlgorithmConfig, OptimizationResult


@dataclass
class MultiObjectiveIndividual:
    """多目标个体数据类"""
    genes: List[Any]  # 基因序列
    objectives: List[float] = None  # 目标函数值
    rank: int = 0  # 支配等级
    crowding_distance: float = 0.0  # 拥挤距离
    dominated_solutions: List['MultiObjectiveIndividual'] = None  # 被支配的解
    domination_count: int = 0  # 支配该解的解的数量
    
    def __post_init__(self) -> None:
        """初始化后处理"""
        if self.objectives is None:
            self.objectives = []
        if self.dominated_solutions is None:
            self.dominated_solutions = []
    
    def dominates(self, other: 'MultiObjectiveIndividual') -> bool:
        """判断是否支配另一个个体"""
        if not self.objectives or not other.objectives:
            return False
        
        at_least_one_better = False
        for i in range(len(self.objectives)):
            if self.objectives[i] > other.objectives[i]:
                return False  # 在某个目标上更差
            elif self.objectives[i] < other.objectives[i]:
                at_least_one_better = True
        
        return at_least_one_better
    
    def copy(self) -> 'MultiObjectiveIndividual':
        """复制个体"""
        return MultiObjectiveIndividual(
            genes=self.genes.copy(),
            objectives=self.objectives.copy() if self.objectives else [],
            rank=self.rank,
            crowding_distance=self.crowding_distance
        )


class NSGA2Algorithm(BaseOptimizationAlgorithm):
    """
    NSGA-II多目标优化算法实现
    
    实现经典的NSGA-II算法：
    - 非支配排序
    - 拥挤距离计算
    - 精英保留策略
    - 帕累托前沿维护
    """
    
    def __init__(self, config: AlgorithmConfig) -> None:
        """
        初始化NSGA-II算法
        
        Args:
            config: 算法配置参数
        """
        super().__init__(config)
        
        # NSGA-II参数
        self.crossover_rate = config.algorithm_params.get("crossover_rate", 0.9)
        self.mutation_rate = config.algorithm_params.get("mutation_rate", 0.1)
        self.num_objectives = config.algorithm_params.get("num_objectives", 2)
        
        # 算法状态
        self.population: List[MultiObjectiveIndividual] = []
        self.pareto_front: List[MultiObjectiveIndividual] = []
        self.generation = 0
        
        # 问题相关参数
        self.gene_length = 0
        self.gene_bounds = []
        self.objective_functions: List[Callable] = []
    
    def initialize_population(self, problem_definition: Dict[str, Any]) -> None:
        """
        初始化种群
        
        Args:
            problem_definition: 问题定义
        """
        # 解析问题参数
        self.gene_length = problem_definition.get("gene_length", 10)
        self.gene_bounds = problem_definition.get("gene_bounds", [(0, 1)] * self.gene_length)
        self.num_objectives = problem_definition.get("num_objectives", 2)
        
        # 解析目标函数
        self.objective_functions = problem_definition.get("objective_functions", [])
        if not self.objective_functions:
            # 创建默认目标函数
            self.objective_functions = [self._default_objective_1, self._default_objective_2]
        
        # 创建初始种群
        self.population = []
        for _ in range(self.config.population_size):
            individual = self._create_random_individual()
            self.population.append(individual)
        
        # 评估初始种群
        self._evaluate_population()
        
        # 执行非支配排序
        self._fast_non_dominated_sort()
        
        # 计算拥挤距离
        self._calculate_crowding_distance()
        
        # 更新帕累托前沿
        self._update_pareto_front()
    
    def _create_random_individual(self) -> MultiObjectiveIndividual:
        """创建随机个体"""
        genes = []
        for i in range(self.gene_length):
            lower, upper = self.gene_bounds[i]
            genes.append(random.uniform(lower, upper))
        
        return MultiObjectiveIndividual(genes=genes)
    
    def _default_objective_1(self, solution: Dict[str, Any]) -> float:
        """默认目标函数1：最小化基因平方和"""
        genes = solution.get("genes", [])
        return sum(x**2 for x in genes)
    
    def _default_objective_2(self, solution: Dict[str, Any]) -> float:
        """默认目标函数2：最小化基因和的平方"""
        genes = solution.get("genes", [])
        return (sum(genes) - len(genes)/2)**2
    
    def _evaluate_population(self) -> None:
        """评估种群中所有个体的目标函数值"""
        for individual in self.population:
            individual.objectives = []
            solution = {"genes": individual.genes}
            
            for obj_func in self.objective_functions:
                obj_value = obj_func(solution)
                individual.objectives.append(obj_value)
    
    def _fast_non_dominated_sort(self) -> List[List[MultiObjectiveIndividual]]:
        """快速非支配排序"""
        fronts = [[]]  # 第一个前沿
        
        for individual in self.population:
            individual.dominated_solutions = []
            individual.domination_count = 0
            
            for other in self.population:
                if individual.dominates(other):
                    individual.dominated_solutions.append(other)
                elif other.dominates(individual):
                    individual.domination_count += 1
            
            if individual.domination_count == 0:
                individual.rank = 0
                fronts[0].append(individual)
        
        i = 0
        while len(fronts[i]) > 0:
            next_front = []
            for individual in fronts[i]:
                for dominated in individual.dominated_solutions:
                    dominated.domination_count -= 1
                    if dominated.domination_count == 0:
                        dominated.rank = i + 1
                        next_front.append(dominated)
            
            i += 1
            fronts.append(next_front)
        
        return fronts[:-1]  # 移除最后一个空前沿
    
    def _calculate_crowding_distance(self) -> None:
        """计算拥挤距离"""
        fronts = self._fast_non_dominated_sort()
        
        for front in fronts:
            if len(front) <= 2:
                for individual in front:
                    individual.crowding_distance = float('inf')
                continue
            
            # 初始化拥挤距离
            for individual in front:
                individual.crowding_distance = 0.0
            
            # 对每个目标函数计算拥挤距离
            for obj_idx in range(self.num_objectives):
                # 按目标函数值排序
                front.sort(key=lambda x: x.objectives[obj_idx])
                
                # 边界解设为无穷大
                front[0].crowding_distance = float('inf')
                front[-1].crowding_distance = float('inf')
                
                # 计算目标函数值范围
                obj_range = front[-1].objectives[obj_idx] - front[0].objectives[obj_idx]
                if obj_range == 0:
                    continue
                
                # 计算中间解的拥挤距离
                for i in range(1, len(front) - 1):
                    distance = (front[i + 1].objectives[obj_idx] - 
                              front[i - 1].objectives[obj_idx]) / obj_range
                    front[i].crowding_distance += distance
    
    def _update_pareto_front(self) -> None:
        """更新帕累托前沿"""
        fronts = self._fast_non_dominated_sort()
        if fronts:
            self.pareto_front = fronts[0].copy()
    
    def evaluate_solution(self, solution: Dict[str, Any]) -> float:
        """
        评估解（多目标优化返回支配等级）
        
        Args:
            solution: 解
            
        Returns:
            float: 支配等级（越小越好）
        """
        # 对于多目标优化，返回支配等级作为适应度
        genes = solution.get("genes", [])
        objectives = []
        
        for obj_func in self.objective_functions:
            obj_value = obj_func({"genes": genes})
            objectives.append(obj_value)
        
        # 计算与当前帕累托前沿的支配关系
        domination_count = 0
        for pareto_individual in self.pareto_front:
            temp_individual = MultiObjectiveIndividual(genes=genes, objectives=objectives)
            if pareto_individual.dominates(temp_individual):
                domination_count += 1
        
        return float(domination_count)
    
    def generate_new_solutions(self) -> List[Dict[str, Any]]:
        """
        生成新一代解
        
        Returns:
            List[Dict[str, Any]]: 新一代解列表
        """
        # 创建子代种群
        offspring = []
        
        while len(offspring) < self.config.population_size:
            # 锦标赛选择父代
            parent1 = self._tournament_selection()
            parent2 = self._tournament_selection()
            
            # 交叉
            if random.random() < self.crossover_rate:
                child1, child2 = self._crossover(parent1, parent2)
            else:
                child1, child2 = parent1.copy(), parent2.copy()
            
            # 变异
            if random.random() < self.mutation_rate:
                child1 = self._mutate(child1)
            if random.random() < self.mutation_rate:
                child2 = self._mutate(child2)
            
            offspring.extend([child1, child2])
        
        # 限制子代数量
        offspring = offspring[:self.config.population_size]
        
        # 合并父代和子代
        combined_population = self.population + offspring
        
        # 评估新个体
        for individual in offspring:
            individual.objectives = []
            solution = {"genes": individual.genes}
            for obj_func in self.objective_functions:
                obj_value = obj_func(solution)
                individual.objectives.append(obj_value)
        
        # 环境选择
        self.population = self._environmental_selection(combined_population)
        
        # 更新帕累托前沿
        self._update_pareto_front()
        
        self.generation += 1
        
        # 返回帕累托前沿解
        return [{"genes": ind.genes, "objectives": ind.objectives} for ind in self.pareto_front]
    
    def _tournament_selection(self, tournament_size: int = 2) -> MultiObjectiveIndividual:
        """锦标赛选择"""
        tournament = random.sample(self.population, min(tournament_size, len(self.population)))
        
        # 选择支配等级最小的个体
        best = min(tournament, key=lambda x: x.rank)
        
        # 如果支配等级相同，选择拥挤距离最大的
        same_rank = [ind for ind in tournament if ind.rank == best.rank]
        if len(same_rank) > 1:
            best = max(same_rank, key=lambda x: x.crowding_distance)
        
        return best
    
    def _crossover(self, parent1: MultiObjectiveIndividual, 
                  parent2: MultiObjectiveIndividual) -> Tuple[MultiObjectiveIndividual, MultiObjectiveIndividual]:
        """模拟二进制交叉（SBX）"""
        eta_c = 20.0  # 交叉分布指数
        
        child1_genes = []
        child2_genes = []
        
        for i in range(len(parent1.genes)):
            if random.random() <= 0.5:
                u = random.random()
                
                if u <= 0.5:
                    beta = (2 * u) ** (1.0 / (eta_c + 1))
                else:
                    beta = (1.0 / (2 * (1 - u))) ** (1.0 / (eta_c + 1))
                
                child1_gene = 0.5 * ((1 + beta) * parent1.genes[i] + (1 - beta) * parent2.genes[i])
                child2_gene = 0.5 * ((1 - beta) * parent1.genes[i] + (1 + beta) * parent2.genes[i])
                
                # 边界处理
                lower, upper = self.gene_bounds[i]
                child1_gene = max(min(child1_gene, upper), lower)
                child2_gene = max(min(child2_gene, upper), lower)
                
                child1_genes.append(child1_gene)
                child2_genes.append(child2_gene)
            else:
                child1_genes.append(parent1.genes[i])
                child2_genes.append(parent2.genes[i])
        
        return (MultiObjectiveIndividual(genes=child1_genes),
                MultiObjectiveIndividual(genes=child2_genes))
    
    def _mutate(self, individual: MultiObjectiveIndividual) -> MultiObjectiveIndividual:
        """多项式变异"""
        eta_m = 20.0  # 变异分布指数
        mutated_individual = individual.copy()
        
        for i in range(len(mutated_individual.genes)):
            if random.random() <= (1.0 / len(mutated_individual.genes)):
                u = random.random()
                lower, upper = self.gene_bounds[i]
                
                delta_1 = (mutated_individual.genes[i] - lower) / (upper - lower)
                delta_2 = (upper - mutated_individual.genes[i]) / (upper - lower)
                
                if u <= 0.5:
                    xy = 1.0 - delta_1
                    val = 2.0 * u + (1.0 - 2.0 * u) * (xy ** (eta_m + 1))
                    delta_q = val ** (1.0 / (eta_m + 1)) - 1.0
                else:
                    xy = 1.0 - delta_2
                    val = 2.0 * (1.0 - u) + 2.0 * (u - 0.5) * (xy ** (eta_m + 1))
                    delta_q = 1.0 - val ** (1.0 / (eta_m + 1))
                
                mutated_individual.genes[i] += delta_q * (upper - lower)
                mutated_individual.genes[i] = max(min(mutated_individual.genes[i], upper), lower)
        
        return mutated_individual
    
    def _environmental_selection(self, combined_population: List[MultiObjectiveIndividual]) -> List[MultiObjectiveIndividual]:
        """环境选择"""
        # 更新合并种群的支配关系
        for individual in combined_population:
            individual.dominated_solutions = []
            individual.domination_count = 0
        
        # 执行非支配排序
        fronts = []
        remaining_population = combined_population.copy()
        
        while remaining_population:
            current_front = []
            
            for individual in remaining_population:
                individual.domination_count = 0
                individual.dominated_solutions = []
                
                for other in remaining_population:
                    if individual.dominates(other):
                        individual.dominated_solutions.append(other)
                    elif other.dominates(individual):
                        individual.domination_count += 1
                
                if individual.domination_count == 0:
                    current_front.append(individual)
            
            if not current_front:
                break
            
            fronts.append(current_front)
            remaining_population = [ind for ind in remaining_population if ind not in current_front]
        
        # 选择个体
        selected = []
        for front in fronts:
            if len(selected) + len(front) <= self.config.population_size:
                selected.extend(front)
            else:
                # 计算拥挤距离并选择
                self._calculate_front_crowding_distance(front)
                front.sort(key=lambda x: x.crowding_distance, reverse=True)
                remaining_slots = self.config.population_size - len(selected)
                selected.extend(front[:remaining_slots])
                break
        
        return selected
    
    def _calculate_front_crowding_distance(self, front: List[MultiObjectiveIndividual]) -> None:
        """计算单个前沿的拥挤距离"""
        if len(front) <= 2:
            for individual in front:
                individual.crowding_distance = float('inf')
            return
        
        # 初始化拥挤距离
        for individual in front:
            individual.crowding_distance = 0.0
        
        # 对每个目标函数计算拥挤距离
        for obj_idx in range(self.num_objectives):
            front.sort(key=lambda x: x.objectives[obj_idx])
            
            front[0].crowding_distance = float('inf')
            front[-1].crowding_distance = float('inf')
            
            obj_range = front[-1].objectives[obj_idx] - front[0].objectives[obj_idx]
            if obj_range == 0:
                continue
            
            for i in range(1, len(front) - 1):
                distance = (front[i + 1].objectives[obj_idx] - 
                          front[i - 1].objectives[obj_idx]) / obj_range
                front[i].crowding_distance += distance
    
    def select_solutions(self, solutions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """选择解（多目标优化返回帕累托前沿）"""
        return solutions  # 返回所有帕累托前沿解
    
    def _get_best_solution(self) -> Dict[str, Any]:
        """获取当前最佳解（帕累托前沿）"""
        if not self.pareto_front:
            return {"pareto_front": [], "hypervolume": 0.0}
        
        pareto_solutions = []
        for individual in self.pareto_front:
            pareto_solutions.append({
                "genes": individual.genes,
                "objectives": individual.objectives,
                "rank": individual.rank,
                "crowding_distance": individual.crowding_distance
            })
        
        return {
            "pareto_front": pareto_solutions,
            "pareto_front_size": len(self.pareto_front),
            "generation": self.generation,
            "hypervolume": self._calculate_hypervolume(),
            "spacing": self._calculate_spacing(),
            "spread": self._calculate_spread()
        }
    
    def _calculate_hypervolume(self) -> float:
        """计算超体积指标（简化实现）"""
        if not self.pareto_front or self.num_objectives > 3:
            return 0.0
        
        # 简化的超体积计算
        reference_point = [max(ind.objectives[i] for ind in self.pareto_front) + 1 
                          for i in range(self.num_objectives)]
        
        # 对于2D情况的简化计算
        if self.num_objectives == 2:
            sorted_front = sorted(self.pareto_front, key=lambda x: x.objectives[0])
            hypervolume = 0.0
            
            for i, individual in enumerate(sorted_front):
                if i == 0:
                    width = reference_point[0] - individual.objectives[0]
                else:
                    width = sorted_front[i-1].objectives[0] - individual.objectives[0]
                
                height = reference_point[1] - individual.objectives[1]
                hypervolume += width * height
            
            return hypervolume
        
        return 0.0
    
    def _calculate_spacing(self) -> float:
        """计算间距指标"""
        if len(self.pareto_front) < 2:
            return 0.0
        
        distances = []
        for i, ind1 in enumerate(self.pareto_front):
            min_distance = float('inf')
            for j, ind2 in enumerate(self.pareto_front):
                if i != j:
                    distance = sum((ind1.objectives[k] - ind2.objectives[k])**2 
                                 for k in range(self.num_objectives))**0.5
                    min_distance = min(min_distance, distance)
            distances.append(min_distance)
        
        mean_distance = sum(distances) / len(distances)
        variance = sum((d - mean_distance)**2 for d in distances) / len(distances)
        
        return variance**0.5
    
    def _calculate_spread(self) -> float:
        """计算分布性指标"""
        if len(self.pareto_front) < 2:
            return 0.0
        
        # 简化的分布性计算
        objectives_array = np.array([ind.objectives for ind in self.pareto_front])
        
        # 计算每个目标的范围
        ranges = []
        for obj_idx in range(self.num_objectives):
            obj_values = objectives_array[:, obj_idx]
            ranges.append(max(obj_values) - min(obj_values))
        
        return sum(ranges) / self.num_objectives
