"""
粒子群优化算法实现

遵循base-rules.md规范：
- 函数复杂度控制，每个函数圈复杂度不超过10
- 函数参数不超过5个
- 使用描述性的函数命名
"""

import random
import numpy as np
from typing import Dict, Any, List, Optional
from dataclasses import dataclass

from src.algorithms.base import BaseOptimizationAlgorithm, AlgorithmConfig, OptimizationResult


@dataclass
class Particle:
    """粒子数据类"""
    position: np.ndarray  # 当前位置
    velocity: np.ndarray  # 当前速度
    fitness: float = float('inf')  # 适应度值
    best_position: Optional[np.ndarray] = None  # 个体最佳位置
    best_fitness: float = float('inf')  # 个体最佳适应度
    
    def __post_init__(self) -> None:
        """初始化后处理"""
        if self.best_position is None:
            self.best_position = self.position.copy()
            self.best_fitness = self.fitness


class ParticleSwarmOptimization(BaseOptimizationAlgorithm):
    """
    粒子群优化算法实现
    
    实现标准的粒子群优化算法用于资源调度优化：
    - 支持多种拓扑结构
    - 自适应参数调整
    - 多样性保持机制
    """
    
    def __init__(self, config: AlgorithmConfig) -> None:
        """
        初始化粒子群优化算法
        
        Args:
            config: 算法配置参数
        """
        super().__init__(config)
        
        # PSO参数
        self.inertia_weight = config.algorithm_params.get("inertia_weight", 0.9)
        self.cognitive_coefficient = config.algorithm_params.get("cognitive_coefficient", 2.0)
        self.social_coefficient = config.algorithm_params.get("social_coefficient", 2.0)
        self.max_velocity = config.algorithm_params.get("max_velocity", None)
        self.topology = config.algorithm_params.get("topology", "global")
        self.adaptive_parameters = config.algorithm_params.get("adaptive_parameters", True)
        
        # 算法状态
        self.swarm: List[Particle] = []
        self.global_best_position: Optional[np.ndarray] = None
        self.global_best_fitness: float = float('inf')
        self.iteration = 0
        
        # 问题相关参数
        self.dimension = 0
        self.position_bounds = []
        self.velocity_bounds = []
        
        # 统计信息
        self.diversity_history: List[float] = []
        self.velocity_history: List[float] = []
    
    def initialize_population(self, problem_definition: Dict[str, Any]) -> None:
        """
        初始化粒子群
        
        Args:
            problem_definition: 问题定义
        """
        # 解析问题参数
        self.dimension = problem_definition.get("dimension", 10)
        self.position_bounds = problem_definition.get("position_bounds", [(0, 1)] * self.dimension)
        
        # 设置速度边界
        if "velocity_bounds" in problem_definition:
            self.velocity_bounds = problem_definition["velocity_bounds"]
        else:
            # 默认速度边界为位置范围的20%
            self.velocity_bounds = []
            for lower, upper in self.position_bounds:
                v_range = (upper - lower) * 0.2
                self.velocity_bounds.append((-v_range, v_range))
        
        # 初始化粒子群
        self.swarm = []
        for _ in range(self.config.population_size):
            particle = self._create_random_particle()
            self.swarm.append(particle)
        
        # 评估初始粒子群
        self._evaluate_swarm()
        
        # 初始化全局最佳
        self._update_global_best()
    
    def _create_random_particle(self) -> Particle:
        """
        创建随机粒子
        
        Returns:
            Particle: 随机生成的粒子
        """
        # 随机初始化位置
        position = np.zeros(self.dimension)
        for i in range(self.dimension):
            lower, upper = self.position_bounds[i]
            position[i] = random.uniform(lower, upper)
        
        # 随机初始化速度
        velocity = np.zeros(self.dimension)
        for i in range(self.dimension):
            v_lower, v_upper = self.velocity_bounds[i]
            velocity[i] = random.uniform(v_lower, v_upper)
        
        return Particle(position=position, velocity=velocity)
    
    def _evaluate_swarm(self) -> None:
        """评估粒子群中所有粒子的适应度"""
        for particle in self.swarm:
            solution = {"position": particle.position.tolist()}
            particle.fitness = self.evaluate_solution(solution)
            
            # 更新个体最佳
            if particle.fitness < particle.best_fitness:
                particle.best_position = particle.position.copy()
                particle.best_fitness = particle.fitness
    
    def _update_global_best(self) -> None:
        """更新全局最佳位置"""
        for particle in self.swarm:
            if particle.best_fitness < self.global_best_fitness:
                self.global_best_position = particle.best_position.copy()
                self.global_best_fitness = particle.best_fitness
    
    def evaluate_solution(self, solution: Dict[str, Any]) -> float:
        """
        评估解的适应度
        
        Args:
            solution: 解
            
        Returns:
            float: 适应度值
        """
        if self.objective_function is None:
            return float('inf')
        
        return self.objective_function(solution)
    
    def generate_new_solutions(self) -> List[Dict[str, Any]]:
        """
        执行PSO迭代
        
        Returns:
            List[Dict[str, Any]]: 当前最佳解列表
        """
        # 更新粒子速度和位置
        self._update_swarm()
        
        # 评估粒子群
        self._evaluate_swarm()
        
        # 更新全局最佳
        self._update_global_best()
        
        # 自适应参数调整
        if self.adaptive_parameters:
            self._adapt_parameters()
        
        # 记录统计信息
        self._record_statistics()
        
        self.iteration += 1
        
        # 返回当前最佳解
        return [{"position": self.global_best_position.tolist(), "fitness": self.global_best_fitness}]
    
    def _update_swarm(self) -> None:
        """更新粒子群的速度和位置"""
        for particle in self.swarm:
            self._update_particle_velocity(particle)
            self._update_particle_position(particle)
    
    def _update_particle_velocity(self, particle: Particle) -> None:
        """
        更新粒子速度
        
        Args:
            particle: 待更新的粒子
        """
        # 生成随机数
        r1 = np.random.random(self.dimension)
        r2 = np.random.random(self.dimension)
        
        # 计算认知分量
        cognitive_component = (
            self.cognitive_coefficient * r1 * 
            (particle.best_position - particle.position)
        )
        
        # 计算社会分量
        if self.topology == "global":
            social_component = (
                self.social_coefficient * r2 * 
                (self.global_best_position - particle.position)
            )
        else:
            # 局部拓扑结构（简化实现）
            local_best = self._get_local_best(particle)
            social_component = (
                self.social_coefficient * r2 * 
                (local_best - particle.position)
            )
        
        # 更新速度
        particle.velocity = (
            self.inertia_weight * particle.velocity + 
            cognitive_component + 
            social_component
        )
        
        # 限制速度范围
        self._clamp_velocity(particle)
    
    def _update_particle_position(self, particle: Particle) -> None:
        """
        更新粒子位置
        
        Args:
            particle: 待更新的粒子
        """
        # 更新位置
        particle.position += particle.velocity
        
        # 限制位置范围
        self._clamp_position(particle)
    
    def _clamp_velocity(self, particle: Particle) -> None:
        """限制粒子速度范围"""
        for i in range(self.dimension):
            v_lower, v_upper = self.velocity_bounds[i]
            particle.velocity[i] = np.clip(particle.velocity[i], v_lower, v_upper)
        
        # 如果设置了最大速度
        if self.max_velocity is not None:
            velocity_magnitude = np.linalg.norm(particle.velocity)
            if velocity_magnitude > self.max_velocity:
                particle.velocity = (particle.velocity / velocity_magnitude) * self.max_velocity
    
    def _clamp_position(self, particle: Particle) -> None:
        """限制粒子位置范围"""
        for i in range(self.dimension):
            lower, upper = self.position_bounds[i]
            particle.position[i] = np.clip(particle.position[i], lower, upper)
    
    def _get_local_best(self, particle: Particle) -> np.ndarray:
        """
        获取局部最佳位置（简化实现）
        
        Args:
            particle: 当前粒子
            
        Returns:
            np.ndarray: 局部最佳位置
        """
        # 简化实现：返回全局最佳
        # 完整实现需要根据拓扑结构确定邻居
        return self.global_best_position
    
    def _adapt_parameters(self) -> None:
        """自适应参数调整"""
        # 线性递减惯性权重
        max_iterations = self.config.max_iterations
        min_inertia = 0.4
        max_inertia = 0.9
        
        self.inertia_weight = (
            max_inertia - (max_inertia - min_inertia) * 
            (self.iteration / max_iterations)
        )
        
        # 根据收敛情况调整认知和社会系数
        if len(self.best_fitness_history) >= 10:
            recent_improvement = (
                self.best_fitness_history[-10] - self.best_fitness_history[-1]
            )
            
            if recent_improvement < self.config.convergence_tolerance:
                # 收敛缓慢，增加探索
                self.cognitive_coefficient = min(self.cognitive_coefficient * 1.05, 3.0)
                self.social_coefficient = max(self.social_coefficient * 0.95, 1.0)
            else:
                # 收敛较快，增加开发
                self.cognitive_coefficient = max(self.cognitive_coefficient * 0.95, 1.0)
                self.social_coefficient = min(self.social_coefficient * 1.05, 3.0)
    
    def _record_statistics(self) -> None:
        """记录统计信息"""
        # 计算种群多样性
        diversity = self._calculate_diversity()
        self.diversity_history.append(diversity)
        
        # 计算平均速度
        avg_velocity = np.mean([np.linalg.norm(p.velocity) for p in self.swarm])
        self.velocity_history.append(avg_velocity)
    
    def _calculate_diversity(self) -> float:
        """计算粒子群多样性"""
        if len(self.swarm) < 2:
            return 0.0
        
        positions = np.array([p.position for p in self.swarm])
        center = np.mean(positions, axis=0)
        
        # 计算所有粒子到中心的平均距离
        distances = [np.linalg.norm(pos - center) for pos in positions]
        return float(np.mean(distances))
    
    def select_solutions(self, solutions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """选择最优解"""
        if not solutions:
            return []
        
        return [min(solutions, key=lambda x: x["fitness"])]
    
    def _get_best_solution(self) -> Dict[str, Any]:
        """获取当前最佳解"""
        if self.global_best_position is None:
            return {"position": [], "fitness": float('inf')}
        
        return {
            "position": self.global_best_position.tolist(),
            "fitness": self.global_best_fitness,
            "iteration": self.iteration,
            "swarm_diversity": self._calculate_diversity(),
            "average_velocity": np.mean([np.linalg.norm(p.velocity) for p in self.swarm]),
            "inertia_weight": self.inertia_weight,
            "cognitive_coefficient": self.cognitive_coefficient,
            "social_coefficient": self.social_coefficient
        }
    
    def get_algorithm_statistics(self) -> Dict[str, Any]:
        """
        获取算法统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        return {
            "iteration": self.iteration,
            "swarm_size": len(self.swarm),
            "global_best_fitness": self.global_best_fitness,
            "swarm_diversity": self._calculate_diversity(),
            "average_velocity": np.mean([np.linalg.norm(p.velocity) for p in self.swarm]),
            "diversity_history": self.diversity_history[-50:],  # 最近50次的多样性历史
            "velocity_history": self.velocity_history[-50:],    # 最近50次的速度历史
            "inertia_weight": self.inertia_weight,
            "cognitive_coefficient": self.cognitive_coefficient,
            "social_coefficient": self.social_coefficient,
            "best_particles_count": sum(1 for p in self.swarm if p.fitness == self.global_best_fitness)
        }
