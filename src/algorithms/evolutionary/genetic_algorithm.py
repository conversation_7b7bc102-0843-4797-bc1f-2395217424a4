"""
遗传算法实现

遵循base-rules.md规范：
- 函数复杂度控制，每个函数圈复杂度不超过10
- 函数参数不超过5个
- 使用描述性的函数命名
"""

import random
import numpy as np
from typing import Dict, Any, List, Optional, Tuple, Callable
from dataclasses import dataclass

from src.algorithms.base import BaseOptimizationAlgorithm, AlgorithmConfig, OptimizationResult


@dataclass
class Individual:
    """个体数据类"""
    genes: List[Any]  # 基因序列
    fitness: float = float('inf')  # 适应度值
    age: int = 0  # 个体年龄
    
    def copy(self) -> 'Individual':
        """复制个体"""
        return Individual(
            genes=self.genes.copy(),
            fitness=self.fitness,
            age=self.age
        )


class GeneticAlgorithm(BaseOptimizationAlgorithm):
    """
    遗传算法实现
    
    实现经典的遗传算法用于资源调度优化：
    - 支持多种编码方式（二进制、实数、排列）
    - 自适应参数调整
    - 多种选择、交叉、变异策略
    """
    
    def __init__(self, config: AlgorithmConfig) -> None:
        """
        初始化遗传算法
        
        Args:
            config: 算法配置参数
        """
        super().__init__(config)
        self.population: List[Individual] = []
        self.elite_individuals: List[Individual] = []
        self.generation = 0
        
        # 遗传算法参数
        self.crossover_rate = config.algorithm_params.get("crossover_rate", 0.8)
        self.mutation_rate = config.algorithm_params.get("mutation_rate", 0.1)
        self.elite_size = config.algorithm_params.get("elite_size", 2)
        self.selection_method = config.algorithm_params.get("selection_method", "tournament")
        self.crossover_method = config.algorithm_params.get("crossover_method", "single_point")
        self.mutation_method = config.algorithm_params.get("mutation_method", "random")
        self.encoding_type = config.algorithm_params.get("encoding_type", "binary")
        
        # 问题相关参数
        self.gene_length = 0
        self.gene_bounds = []
        self.problem_type = "minimization"
    
    def initialize_population(self, problem_definition: Dict[str, Any]) -> None:
        """
        初始化种群
        
        Args:
            problem_definition: 问题定义
        """
        # 解析问题参数
        self.gene_length = problem_definition.get("gene_length", 10)
        self.gene_bounds = problem_definition.get("gene_bounds", [(0, 1)] * self.gene_length)
        self.problem_type = problem_definition.get("problem_type", "minimization")
        
        # 创建初始种群
        self.population = []
        for _ in range(self.config.population_size):
            individual = self._create_random_individual()
            self.population.append(individual)
    
    def _create_random_individual(self) -> Individual:
        """
        创建随机个体
        
        Returns:
            Individual: 随机生成的个体
        """
        if self.encoding_type == "binary":
            genes = [random.randint(0, 1) for _ in range(self.gene_length)]
        elif self.encoding_type == "real":
            genes = []
            for i in range(self.gene_length):
                lower, upper = self.gene_bounds[i]
                genes.append(random.uniform(lower, upper))
        elif self.encoding_type == "permutation":
            genes = list(range(self.gene_length))
            random.shuffle(genes)
        else:
            genes = [random.randint(0, 1) for _ in range(self.gene_length)]
        
        return Individual(genes=genes)
    
    def evaluate_solution(self, solution: Dict[str, Any]) -> float:
        """
        评估解的适应度
        
        Args:
            solution: 解
            
        Returns:
            float: 适应度值
        """
        if self.objective_function is None:
            return float('inf')
        
        return self.objective_function(solution)
    
    def generate_new_solutions(self) -> List[Dict[str, Any]]:
        """
        生成新的候选解（执行一代进化）
        
        Returns:
            List[Dict[str, Any]]: 新候选解列表
        """
        # 评估当前种群
        self._evaluate_population()
        
        # 保存精英个体
        self._preserve_elites()
        
        # 选择、交叉、变异生成新种群
        new_population = self._evolve_population()
        
        # 更新种群
        self.population = new_population
        self.generation += 1
        
        # 返回当前最佳解
        best_individual = min(self.population, key=lambda x: x.fitness)
        return [{"genes": best_individual.genes, "fitness": best_individual.fitness}]
    
    def _evaluate_population(self) -> None:
        """评估种群中所有个体的适应度"""
        for individual in self.population:
            if individual.fitness == float('inf'):
                solution = {"genes": individual.genes}
                individual.fitness = self.evaluate_solution(solution)
    
    def _preserve_elites(self) -> None:
        """保存精英个体"""
        # 按适应度排序
        sorted_population = sorted(self.population, key=lambda x: x.fitness)
        
        # 保存前elite_size个个体
        self.elite_individuals = []
        for i in range(min(self.elite_size, len(sorted_population))):
            self.elite_individuals.append(sorted_population[i].copy())
    
    def _evolve_population(self) -> List[Individual]:
        """进化种群生成新一代"""
        new_population = []
        
        # 添加精英个体
        new_population.extend([elite.copy() for elite in self.elite_individuals])
        
        # 生成剩余个体
        while len(new_population) < self.config.population_size:
            # 选择父代
            parent1 = self._select_parent()
            parent2 = self._select_parent()
            
            # 交叉
            if random.random() < self.crossover_rate:
                child1, child2 = self._crossover(parent1, parent2)
            else:
                child1, child2 = parent1.copy(), parent2.copy()
            
            # 变异
            if random.random() < self.mutation_rate:
                child1 = self._mutate(child1)
            if random.random() < self.mutation_rate:
                child2 = self._mutate(child2)
            
            new_population.extend([child1, child2])
        
        # 确保种群大小正确
        return new_population[:self.config.population_size]
    
    def _select_parent(self) -> Individual:
        """
        选择父代个体
        
        Returns:
            Individual: 选中的父代个体
        """
        if self.selection_method == "tournament":
            return self._tournament_selection()
        elif self.selection_method == "roulette":
            return self._roulette_selection()
        elif self.selection_method == "rank":
            return self._rank_selection()
        else:
            return random.choice(self.population)
    
    def _tournament_selection(self, tournament_size: int = 3) -> Individual:
        """锦标赛选择"""
        tournament = random.sample(self.population, min(tournament_size, len(self.population)))
        return min(tournament, key=lambda x: x.fitness)
    
    def _roulette_selection(self) -> Individual:
        """轮盘赌选择"""
        # 计算适应度权重（最小化问题需要转换）
        max_fitness = max(ind.fitness for ind in self.population)
        weights = [max_fitness - ind.fitness + 1 for ind in self.population]
        total_weight = sum(weights)
        
        if total_weight == 0:
            return random.choice(self.population)
        
        # 轮盘赌选择
        pick = random.uniform(0, total_weight)
        current = 0
        for i, weight in enumerate(weights):
            current += weight
            if current >= pick:
                return self.population[i]
        
        return self.population[-1]
    
    def _rank_selection(self) -> Individual:
        """排序选择"""
        sorted_population = sorted(self.population, key=lambda x: x.fitness)
        ranks = list(range(1, len(sorted_population) + 1))
        total_rank = sum(ranks)
        
        pick = random.uniform(0, total_rank)
        current = 0
        for i, rank in enumerate(ranks):
            current += rank
            if current >= pick:
                return sorted_population[i]
        
        return sorted_population[-1]
    
    def _crossover(self, parent1: Individual, parent2: Individual) -> Tuple[Individual, Individual]:
        """
        交叉操作
        
        Args:
            parent1: 父代1
            parent2: 父代2
            
        Returns:
            Tuple[Individual, Individual]: 子代个体对
        """
        if self.crossover_method == "single_point":
            return self._single_point_crossover(parent1, parent2)
        elif self.crossover_method == "two_point":
            return self._two_point_crossover(parent1, parent2)
        elif self.crossover_method == "uniform":
            return self._uniform_crossover(parent1, parent2)
        elif self.crossover_method == "arithmetic":
            return self._arithmetic_crossover(parent1, parent2)
        else:
            return parent1.copy(), parent2.copy()
    
    def _single_point_crossover(self, parent1: Individual, parent2: Individual) -> Tuple[Individual, Individual]:
        """单点交叉"""
        if len(parent1.genes) <= 1:
            return parent1.copy(), parent2.copy()
        
        crossover_point = random.randint(1, len(parent1.genes) - 1)
        
        child1_genes = parent1.genes[:crossover_point] + parent2.genes[crossover_point:]
        child2_genes = parent2.genes[:crossover_point] + parent1.genes[crossover_point:]
        
        return Individual(genes=child1_genes), Individual(genes=child2_genes)
    
    def _two_point_crossover(self, parent1: Individual, parent2: Individual) -> Tuple[Individual, Individual]:
        """两点交叉"""
        if len(parent1.genes) <= 2:
            return self._single_point_crossover(parent1, parent2)
        
        point1 = random.randint(1, len(parent1.genes) - 2)
        point2 = random.randint(point1 + 1, len(parent1.genes) - 1)
        
        child1_genes = (parent1.genes[:point1] + 
                       parent2.genes[point1:point2] + 
                       parent1.genes[point2:])
        child2_genes = (parent2.genes[:point1] + 
                       parent1.genes[point1:point2] + 
                       parent2.genes[point2:])
        
        return Individual(genes=child1_genes), Individual(genes=child2_genes)
    
    def _uniform_crossover(self, parent1: Individual, parent2: Individual) -> Tuple[Individual, Individual]:
        """均匀交叉"""
        child1_genes = []
        child2_genes = []
        
        for i in range(len(parent1.genes)):
            if random.random() < 0.5:
                child1_genes.append(parent1.genes[i])
                child2_genes.append(parent2.genes[i])
            else:
                child1_genes.append(parent2.genes[i])
                child2_genes.append(parent1.genes[i])
        
        return Individual(genes=child1_genes), Individual(genes=child2_genes)
    
    def _arithmetic_crossover(self, parent1: Individual, parent2: Individual) -> Tuple[Individual, Individual]:
        """算术交叉（适用于实数编码）"""
        if self.encoding_type != "real":
            return self._uniform_crossover(parent1, parent2)
        
        alpha = random.random()
        child1_genes = []
        child2_genes = []
        
        for i in range(len(parent1.genes)):
            gene1 = alpha * parent1.genes[i] + (1 - alpha) * parent2.genes[i]
            gene2 = (1 - alpha) * parent1.genes[i] + alpha * parent2.genes[i]
            child1_genes.append(gene1)
            child2_genes.append(gene2)
        
        return Individual(genes=child1_genes), Individual(genes=child2_genes)
    
    def _mutate(self, individual: Individual) -> Individual:
        """
        变异操作
        
        Args:
            individual: 待变异的个体
            
        Returns:
            Individual: 变异后的个体
        """
        mutated_individual = individual.copy()
        
        if self.mutation_method == "random":
            self._random_mutation(mutated_individual)
        elif self.mutation_method == "gaussian":
            self._gaussian_mutation(mutated_individual)
        elif self.mutation_method == "swap":
            self._swap_mutation(mutated_individual)
        
        return mutated_individual
    
    def _random_mutation(self, individual: Individual) -> None:
        """随机变异"""
        for i in range(len(individual.genes)):
            if random.random() < self.mutation_rate:
                if self.encoding_type == "binary":
                    individual.genes[i] = 1 - individual.genes[i]
                elif self.encoding_type == "real":
                    lower, upper = self.gene_bounds[i]
                    individual.genes[i] = random.uniform(lower, upper)
    
    def _gaussian_mutation(self, individual: Individual) -> None:
        """高斯变异（适用于实数编码）"""
        if self.encoding_type != "real":
            self._random_mutation(individual)
            return
        
        for i in range(len(individual.genes)):
            if random.random() < self.mutation_rate:
                lower, upper = self.gene_bounds[i]
                sigma = (upper - lower) * 0.1  # 标准差为范围的10%
                mutation = np.random.normal(0, sigma)
                individual.genes[i] = np.clip(individual.genes[i] + mutation, lower, upper)
    
    def _swap_mutation(self, individual: Individual) -> None:
        """交换变异（适用于排列编码）"""
        if self.encoding_type != "permutation":
            self._random_mutation(individual)
            return
        
        if len(individual.genes) >= 2:
            i, j = random.sample(range(len(individual.genes)), 2)
            individual.genes[i], individual.genes[j] = individual.genes[j], individual.genes[i]
    
    def select_solutions(self, solutions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """选择最优解"""
        if not solutions:
            return []
        
        # 返回当前最佳解
        return [min(solutions, key=lambda x: x["fitness"])]
    
    def _get_best_solution(self) -> Dict[str, Any]:
        """获取当前最佳解"""
        if not self.population:
            return {"genes": [], "fitness": float('inf')}
        
        best_individual = min(self.population, key=lambda x: x.fitness)
        
        return {
            "genes": best_individual.genes,
            "fitness": best_individual.fitness,
            "generation": self.generation,
            "population_diversity": self._calculate_diversity(),
            "convergence_rate": self._calculate_convergence_rate()
        }
    
    def _calculate_diversity(self) -> float:
        """计算种群多样性"""
        if len(self.population) < 2:
            return 0.0
        
        # 简化的多样性计算：基于适应度方差
        fitness_values = [ind.fitness for ind in self.population]
        mean_fitness = np.mean(fitness_values)
        variance = np.var(fitness_values)
        
        return float(variance / (mean_fitness + 1e-10))
    
    def _calculate_convergence_rate(self) -> float:
        """计算收敛速度"""
        if len(self.best_fitness_history) < 2:
            return 0.0

        recent_improvement = abs(self.best_fitness_history[-1] - self.best_fitness_history[-2])
        return float(recent_improvement)

    def get_algorithm_statistics(self) -> Dict[str, Any]:
        """
        获取算法统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        return {
            "generation": self.generation,
            "population_size": len(self.population),
            "elite_size": len(self.elite_individuals),
            "population_diversity": self._calculate_diversity(),
            "convergence_rate": self._calculate_convergence_rate(),
            "best_fitness": min(ind.fitness for ind in self.population) if self.population else float('inf'),
            "average_fitness": np.mean([ind.fitness for ind in self.population]) if self.population else float('inf'),
            "fitness_variance": np.var([ind.fitness for ind in self.population]) if self.population else 0.0
        }
