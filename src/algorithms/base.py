"""
算法基类和通用接口定义

遵循base-rules.md规范：
- 类文档字符串规则：说明类的用途、主要功能
- 使用描述性的类和方法命名
- 函数复杂度控制，每个函数圈复杂度不超过10
"""

from abc import ABC, abstractmethod
from enum import Enum
from typing import Dict, Any, Optional, List, Callable
from dataclasses import dataclass
from datetime import datetime


class AlgorithmStatus(str, Enum):
    """算法执行状态枚举"""
    INITIALIZED = "initialized"     # 已初始化
    RUNNING = "running"             # 运行中
    CONVERGED = "converged"         # 已收敛
    TERMINATED = "terminated"       # 已终止
    FAILED = "failed"              # 执行失败


@dataclass
class AlgorithmConfig:
    """
    算法配置数据类
    
    存储算法的配置参数和执行设置：
    - 算法基本参数
    - 终止条件
    - 性能配置
    """
    
    # 基本参数
    max_iterations: int = 1000
    population_size: int = 100
    convergence_tolerance: float = 1e-6
    
    # 终止条件
    max_time_seconds: Optional[int] = None
    target_fitness: Optional[float] = None
    stagnation_generations: int = 50
    
    # 性能配置
    parallel_execution: bool = False
    num_workers: Optional[int] = None
    memory_limit_mb: Optional[int] = None
    
    # 算法特定参数
    algorithm_params: Dict[str, Any] = None
    
    def __post_init__(self) -> None:
        """初始化后处理"""
        if self.algorithm_params is None:
            self.algorithm_params = {}


@dataclass
class OptimizationResult:
    """
    优化结果数据类
    
    存储算法执行的结果和性能指标：
    - 最优解和目标函数值
    - 算法执行统计信息
    - 收敛过程数据
    """
    
    # 解的信息
    best_solution: Dict[str, Any]
    best_fitness: float
    feasible: bool = True
    
    # 执行统计
    iterations_completed: int = 0
    execution_time_seconds: float = 0.0
    function_evaluations: int = 0
    
    # 收敛信息
    converged: bool = False
    convergence_history: List[float] = None
    final_status: AlgorithmStatus = AlgorithmStatus.INITIALIZED
    
    # 约束违反情况
    constraint_violations: Optional[Dict[str, float]] = None
    
    # 算法特定结果
    algorithm_data: Dict[str, Any] = None
    
    # 错误信息
    error_message: Optional[str] = None
    
    def __post_init__(self) -> None:
        """初始化后处理"""
        if self.convergence_history is None:
            self.convergence_history = []
        if self.algorithm_data is None:
            self.algorithm_data = {}


class BaseOptimizationAlgorithm(ABC):
    """
    优化算法基类
    
    定义所有优化算法的统一接口：
    - 算法初始化和配置
    - 优化执行和进度监控
    - 结果获取和状态查询
    """
    
    def __init__(self, config: AlgorithmConfig) -> None:
        """
        初始化算法
        
        Args:
            config: 算法配置参数
        """
        self.config = config
        self.status = AlgorithmStatus.INITIALIZED
        self.current_iteration = 0
        self.start_time: Optional[datetime] = None
        self.best_fitness_history: List[float] = []
        self.objective_function: Optional[Callable] = None
        self.constraints: List[Callable] = []
        
    @abstractmethod
    def initialize_population(self, problem_definition: Dict[str, Any]) -> None:
        """
        初始化种群或解空间
        
        Args:
            problem_definition: 问题定义，包含决策变量、约束等信息
        """
        pass
    
    @abstractmethod
    def evaluate_solution(self, solution: Dict[str, Any]) -> float:
        """
        评估解的适应度
        
        Args:
            solution: 待评估的解
            
        Returns:
            float: 适应度值
        """
        pass
    
    @abstractmethod
    def generate_new_solutions(self) -> List[Dict[str, Any]]:
        """
        生成新的候选解
        
        Returns:
            List[Dict[str, Any]]: 新候选解列表
        """
        pass
    
    @abstractmethod
    def select_solutions(self, solutions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        选择保留的解
        
        Args:
            solutions: 候选解列表
            
        Returns:
            List[Dict[str, Any]]: 选择的解列表
        """
        pass
    
    def set_objective_function(self, objective_func: Callable) -> None:
        """
        设置目标函数
        
        Args:
            objective_func: 目标函数
        """
        self.objective_function = objective_func
    
    def add_constraint(self, constraint_func: Callable) -> None:
        """
        添加约束条件
        
        Args:
            constraint_func: 约束函数
        """
        self.constraints.append(constraint_func)
    
    def check_termination_criteria(self) -> bool:
        """
        检查终止条件
        
        Returns:
            bool: 是否满足终止条件
        """
        # 检查最大迭代次数
        if self.current_iteration >= self.config.max_iterations:
            return True
        
        # 检查最大执行时间
        if self.config.max_time_seconds and self.start_time:
            elapsed = (datetime.now() - self.start_time).total_seconds()
            if elapsed >= self.config.max_time_seconds:
                return True
        
        # 检查收敛性
        if len(self.best_fitness_history) >= self.config.stagnation_generations:
            recent_history = self.best_fitness_history[-self.config.stagnation_generations:]
            if max(recent_history) - min(recent_history) < self.config.convergence_tolerance:
                return True
        
        # 检查目标适应度
        if (self.config.target_fitness is not None and 
            self.best_fitness_history and 
            self.best_fitness_history[-1] <= self.config.target_fitness):
            return True
        
        return False
    
    def get_progress(self) -> Dict[str, Any]:
        """
        获取算法执行进度
        
        Returns:
            Dict[str, Any]: 进度信息
        """
        progress_percentage = (self.current_iteration / self.config.max_iterations) * 100
        
        elapsed_time = 0.0
        if self.start_time:
            elapsed_time = (datetime.now() - self.start_time).total_seconds()
        
        return {
            "status": self.status.value,
            "current_iteration": self.current_iteration,
            "max_iterations": self.config.max_iterations,
            "progress_percentage": min(progress_percentage, 100.0),
            "elapsed_time_seconds": elapsed_time,
            "best_fitness": self.best_fitness_history[-1] if self.best_fitness_history else None,
            "convergence_trend": self.best_fitness_history[-10:] if len(self.best_fitness_history) >= 10 else self.best_fitness_history
        }
    
    def optimize(self, 
                problem_definition: Dict[str, Any],
                objective_function: Callable,
                constraints: Optional[List[Callable]] = None) -> OptimizationResult:
        """
        执行优化算法
        
        Args:
            problem_definition: 问题定义
            objective_function: 目标函数
            constraints: 约束条件列表
            
        Returns:
            OptimizationResult: 优化结果
        """
        try:
            # 设置目标函数和约束
            self.set_objective_function(objective_function)
            if constraints:
                for constraint in constraints:
                    self.add_constraint(constraint)
            
            # 初始化
            self.start_time = datetime.now()
            self.status = AlgorithmStatus.RUNNING
            self.initialize_population(problem_definition)
            
            # 主优化循环
            while not self.check_termination_criteria():
                self._execute_iteration()
                self.current_iteration += 1
            
            # 设置最终状态
            if self.check_convergence():
                self.status = AlgorithmStatus.CONVERGED
            else:
                self.status = AlgorithmStatus.TERMINATED
            
            return self._create_result()
            
        except Exception as e:
            self.status = AlgorithmStatus.FAILED
            return OptimizationResult(
                best_solution={},
                best_fitness=float('inf'),
                feasible=False,
                final_status=self.status,
                error_message=str(e)
            )
    
    def _execute_iteration(self) -> None:
        """执行单次迭代"""
        # 生成新解
        new_solutions = self.generate_new_solutions()
        
        # 评估解
        for solution in new_solutions:
            fitness = self.evaluate_solution(solution)
            solution['fitness'] = fitness
        
        # 选择解
        selected_solutions = self.select_solutions(new_solutions)
        
        # 更新最佳适应度历史
        if selected_solutions:
            best_fitness = min(sol['fitness'] for sol in selected_solutions)
            self.best_fitness_history.append(best_fitness)
    
    def check_convergence(self) -> bool:
        """检查是否收敛"""
        if len(self.best_fitness_history) < 2:
            return False
        
        recent_improvement = abs(self.best_fitness_history[-1] - self.best_fitness_history[-2])
        return recent_improvement < self.config.convergence_tolerance
    
    def _create_result(self) -> OptimizationResult:
        """创建优化结果"""
        execution_time = 0.0
        if self.start_time:
            execution_time = (datetime.now() - self.start_time).total_seconds()
        
        return OptimizationResult(
            best_solution=self._get_best_solution(),
            best_fitness=self.best_fitness_history[-1] if self.best_fitness_history else float('inf'),
            feasible=True,
            iterations_completed=self.current_iteration,
            execution_time_seconds=execution_time,
            converged=self.status == AlgorithmStatus.CONVERGED,
            convergence_history=self.best_fitness_history.copy(),
            final_status=self.status
        )
    
    @abstractmethod
    def _get_best_solution(self) -> Dict[str, Any]:
        """获取当前最佳解"""
        pass
