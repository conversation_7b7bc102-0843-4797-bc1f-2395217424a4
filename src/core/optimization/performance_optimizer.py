"""
性能优化器

遵循base-rules.md规范：
- 函数复杂度控制，每个函数圈复杂度不超过10
- 函数参数不超过5个
- 使用描述性的函数命名
"""

import time
import functools
import cProfile
import pstats
import io
from typing import Dict, Any, Callable, Optional, List
import numpy as np
from dataclasses import dataclass
import logging

from src.core.parallel.parallel_executor import ParallelExecutor


@dataclass
class OptimizationResult:
    """优化结果数据类"""
    original_time: float
    optimized_time: float
    speedup_ratio: float
    memory_saved: float
    optimization_method: str
    success: bool
    error_message: Optional[str] = None


class PerformanceOptimizer:
    """
    性能优化器类
    
    提供多种性能优化策略：
    - 函数执行时间优化
    - 内存使用优化
    - 并行计算优化
    - 缓存优化
    - 算法参数自动调优
    """
    
    def __init__(self) -> None:
        """初始化性能优化器"""
        self.logger = logging.getLogger(__name__)
        self.parallel_executor = ParallelExecutor()
        self.optimization_cache = {}
        self.profiling_enabled = False
    
    def enable_profiling(self) -> None:
        """启用性能分析"""
        self.profiling_enabled = True
    
    def disable_profiling(self) -> None:
        """禁用性能分析"""
        self.profiling_enabled = False
    
    def profile_function(self, func: Callable) -> Callable:
        """
        函数性能分析装饰器
        
        Args:
            func: 要分析的函数
            
        Returns:
            Callable: 装饰后的函数
        """
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            if not self.profiling_enabled:
                return func(*args, **kwargs)
            
            # 创建性能分析器
            profiler = cProfile.Profile()
            profiler.enable()
            
            try:
                result = func(*args, **kwargs)
            finally:
                profiler.disable()
            
            # 生成分析报告
            s = io.StringIO()
            ps = pstats.Stats(profiler, stream=s)
            ps.sort_stats('cumulative')
            ps.print_stats(20)  # 显示前20个最耗时的函数
            
            profile_output = s.getvalue()
            self.logger.info(f"函数 {func.__name__} 性能分析:\n{profile_output}")
            
            return result
        
        return wrapper
    
    def time_function(self, func: Callable) -> Callable:
        """
        函数执行时间测量装饰器
        
        Args:
            func: 要测量的函数
            
        Returns:
            Callable: 装饰后的函数
        """
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            result = func(*args, **kwargs)
            end_time = time.time()
            
            execution_time = end_time - start_time
            self.logger.info(f"函数 {func.__name__} 执行时间: {execution_time:.4f}秒")
            
            # 将执行时间添加到结果中（如果结果是字典）
            if isinstance(result, dict):
                result['_execution_time'] = execution_time
            
            return result
        
        return wrapper
    
    def memoize(self, maxsize: int = 128) -> Callable:
        """
        记忆化装饰器，缓存函数结果
        
        Args:
            maxsize: 缓存最大大小
            
        Returns:
            Callable: 装饰器函数
        """
        def decorator(func: Callable) -> Callable:
            cache = {}
            cache_order = []
            
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                # 创建缓存键
                key = str(args) + str(sorted(kwargs.items()))
                
                if key in cache:
                    # 缓存命中
                    self.logger.debug(f"缓存命中: {func.__name__}")
                    return cache[key]
                
                # 计算结果
                result = func(*args, **kwargs)
                
                # 添加到缓存
                if len(cache) >= maxsize:
                    # 移除最旧的缓存项
                    oldest_key = cache_order.pop(0)
                    del cache[oldest_key]
                
                cache[key] = result
                cache_order.append(key)
                
                self.logger.debug(f"缓存存储: {func.__name__}")
                return result
            
            # 添加缓存管理方法
            wrapper.cache_clear = lambda: cache.clear() or cache_order.clear()
            wrapper.cache_info = lambda: {
                'hits': len([k for k in cache_order if k in cache]),
                'misses': len(cache_order) - len([k for k in cache_order if k in cache]),
                'maxsize': maxsize,
                'currsize': len(cache)
            }
            
            return wrapper
        
        return decorator
    
    def optimize_numpy_operations(self, func: Callable) -> Callable:
        """
        NumPy操作优化装饰器
        
        Args:
            func: 要优化的函数
            
        Returns:
            Callable: 优化后的函数
        """
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 设置NumPy优化选项
            original_threads = np.get_num_threads()
            
            try:
                # 根据CPU核心数设置线程数
                import os
                optimal_threads = min(8, os.cpu_count() or 1)
                np.set_num_threads(optimal_threads)
                
                result = func(*args, **kwargs)
                
            finally:
                # 恢复原始设置
                np.set_num_threads(original_threads)
            
            return result
        
        return wrapper
    
    def parallelize_function(self, 
                           chunk_size: Optional[int] = None,
                           execution_type: str = "process") -> Callable:
        """
        函数并行化装饰器
        
        Args:
            chunk_size: 批处理大小
            execution_type: 执行类型
            
        Returns:
            Callable: 装饰器函数
        """
        def decorator(func: Callable) -> Callable:
            @functools.wraps(func)
            def wrapper(data_list: List[Any], *args, **kwargs):
                if not isinstance(data_list, list) or len(data_list) <= 1:
                    # 数据量太小，直接执行
                    return [func(item, *args, **kwargs) for item in data_list]
                
                # 创建并行执行器
                executor = ParallelExecutor(execution_type=execution_type)
                
                # 创建任务函数
                def task_func(item):
                    return func(item, *args, **kwargs)
                
                # 并行执行
                results = executor.execute_parallel(task_func, data_list, chunk_size)
                
                return results
            
            return wrapper
        
        return decorator
    
    def auto_optimize_algorithm_params(self, 
                                     algorithm_class,
                                     param_ranges: Dict[str, tuple],
                                     objective_function: Callable,
                                     optimization_iterations: int = 20) -> Dict[str, Any]:
        """
        自动优化算法参数
        
        Args:
            algorithm_class: 算法类
            param_ranges: 参数范围字典
            objective_function: 目标函数
            optimization_iterations: 优化迭代次数
            
        Returns:
            Dict[str, Any]: 最优参数配置
        """
        best_params = None
        best_performance = float('inf')
        optimization_history = []
        
        for iteration in range(optimization_iterations):
            # 随机生成参数组合
            params = {}
            for param_name, (min_val, max_val) in param_ranges.items():
                if isinstance(min_val, int) and isinstance(max_val, int):
                    params[param_name] = np.random.randint(min_val, max_val + 1)
                else:
                    params[param_name] = np.random.uniform(min_val, max_val)
            
            try:
                # 测试参数性能
                start_time = time.time()
                
                # 创建算法实例
                from src.algorithms.base import AlgorithmConfig
                config = AlgorithmConfig(
                    max_iterations=100,  # 用于测试的较小迭代次数
                    population_size=params.get('population_size', 50),
                    algorithm_params=params
                )
                
                algorithm = algorithm_class(config)
                
                # 执行优化
                result = algorithm.optimize(
                    problem_definition={"dimension": 10, "bounds": [(-5, 5)] * 10},
                    objective_function=objective_function,
                    constraints=[]
                )
                
                execution_time = time.time() - start_time
                
                # 计算性能指标（结合解质量和执行时间）
                performance_score = result.best_fitness + execution_time * 0.1
                
                optimization_history.append({
                    "iteration": iteration,
                    "params": params.copy(),
                    "performance": performance_score,
                    "execution_time": execution_time,
                    "best_fitness": result.best_fitness
                })
                
                if performance_score < best_performance:
                    best_performance = performance_score
                    best_params = params.copy()
                
                self.logger.info(f"参数优化迭代 {iteration}: 性能={performance_score:.4f}")
                
            except Exception as e:
                self.logger.error(f"参数优化迭代 {iteration} 失败: {e}")
                continue
        
        return {
            "best_params": best_params,
            "best_performance": best_performance,
            "optimization_history": optimization_history,
            "total_iterations": optimization_iterations
        }
    
    def optimize_memory_usage(self, func: Callable) -> Callable:
        """
        内存使用优化装饰器
        
        Args:
            func: 要优化的函数
            
        Returns:
            Callable: 优化后的函数
        """
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            import gc
            
            # 执行前清理内存
            gc.collect()
            
            try:
                result = func(*args, **kwargs)
            finally:
                # 执行后清理内存
                gc.collect()
            
            return result
        
        return wrapper
    
    def adaptive_batch_size(self, 
                          min_batch_size: int = 1,
                          max_batch_size: int = 1000) -> Callable:
        """
        自适应批处理大小装饰器
        
        Args:
            min_batch_size: 最小批处理大小
            max_batch_size: 最大批处理大小
            
        Returns:
            Callable: 装饰器函数
        """
        def decorator(func: Callable) -> Callable:
            optimal_batch_size = min_batch_size
            
            @functools.wraps(func)
            def wrapper(data_list: List[Any], *args, **kwargs):
                nonlocal optimal_batch_size
                
                if len(data_list) <= min_batch_size:
                    return func(data_list, *args, **kwargs)
                
                # 测试不同批处理大小的性能
                test_sizes = [optimal_batch_size]
                if optimal_batch_size < max_batch_size:
                    test_sizes.append(min(optimal_batch_size * 2, max_batch_size))
                if optimal_batch_size > min_batch_size:
                    test_sizes.append(max(optimal_batch_size // 2, min_batch_size))
                
                best_time = float('inf')
                best_size = optimal_batch_size
                
                for batch_size in test_sizes:
                    # 使用小样本测试
                    sample_size = min(batch_size * 3, len(data_list))
                    sample_data = data_list[:sample_size]
                    
                    start_time = time.time()
                    
                    # 分批处理样本
                    for i in range(0, len(sample_data), batch_size):
                        batch = sample_data[i:i + batch_size]
                        func(batch, *args, **kwargs)
                    
                    execution_time = time.time() - start_time
                    avg_time_per_item = execution_time / len(sample_data)
                    
                    if avg_time_per_item < best_time:
                        best_time = avg_time_per_item
                        best_size = batch_size
                
                optimal_batch_size = best_size
                
                # 使用最优批处理大小处理所有数据
                results = []
                for i in range(0, len(data_list), optimal_batch_size):
                    batch = data_list[i:i + optimal_batch_size]
                    batch_result = func(batch, *args, **kwargs)
                    if isinstance(batch_result, list):
                        results.extend(batch_result)
                    else:
                        results.append(batch_result)
                
                return results
            
            return wrapper
        
        return decorator
    
    def benchmark_function(self, 
                         func: Callable,
                         test_data: List[Any],
                         iterations: int = 10) -> Dict[str, Any]:
        """
        函数性能基准测试
        
        Args:
            func: 要测试的函数
            test_data: 测试数据
            iterations: 测试迭代次数
            
        Returns:
            Dict[str, Any]: 基准测试结果
        """
        execution_times = []
        memory_usages = []
        
        import psutil
        process = psutil.Process()
        
        for i in range(iterations):
            # 记录开始状态
            start_memory = process.memory_info().rss
            start_time = time.time()
            
            try:
                result = func(test_data)
                success = True
                error_message = None
            except Exception as e:
                success = False
                error_message = str(e)
                result = None
            
            # 记录结束状态
            end_time = time.time()
            end_memory = process.memory_info().rss
            
            execution_time = end_time - start_time
            memory_usage = end_memory - start_memory
            
            execution_times.append(execution_time)
            memory_usages.append(memory_usage)
            
            self.logger.debug(f"基准测试迭代 {i}: 时间={execution_time:.4f}s, 内存={memory_usage}bytes")
        
        # 计算统计信息
        avg_time = np.mean(execution_times)
        std_time = np.std(execution_times)
        min_time = np.min(execution_times)
        max_time = np.max(execution_times)
        
        avg_memory = np.mean(memory_usages)
        std_memory = np.std(memory_usages)
        
        return {
            "function_name": func.__name__,
            "iterations": iterations,
            "success_rate": sum(1 for t in execution_times if t > 0) / iterations,
            "execution_time": {
                "average": round(avg_time, 4),
                "std_dev": round(std_time, 4),
                "min": round(min_time, 4),
                "max": round(max_time, 4)
            },
            "memory_usage": {
                "average": round(avg_memory / 1024 / 1024, 2),  # MB
                "std_dev": round(std_memory / 1024 / 1024, 2),
                "total_peak": round(max(memory_usages) / 1024 / 1024, 2)
            },
            "throughput": round(len(test_data) / avg_time, 2) if avg_time > 0 else 0
        }


# 全局性能优化器实例
_global_optimizer = None

def get_global_optimizer() -> PerformanceOptimizer:
    """获取全局性能优化器实例"""
    global _global_optimizer
    if _global_optimizer is None:
        _global_optimizer = PerformanceOptimizer()
    return _global_optimizer

# 便捷装饰器
def profile(func: Callable) -> Callable:
    """性能分析装饰器"""
    optimizer = get_global_optimizer()
    return optimizer.profile_function(func)

def timeit(func: Callable) -> Callable:
    """执行时间测量装饰器"""
    optimizer = get_global_optimizer()
    return optimizer.time_function(func)

def memoize(maxsize: int = 128):
    """记忆化装饰器"""
    optimizer = get_global_optimizer()
    return optimizer.memoize(maxsize)

def optimize_numpy(func: Callable) -> Callable:
    """NumPy优化装饰器"""
    optimizer = get_global_optimizer()
    return optimizer.optimize_numpy_operations(func)

def parallelize(chunk_size: Optional[int] = None, execution_type: str = "process"):
    """并行化装饰器"""
    optimizer = get_global_optimizer()
    return optimizer.parallelize_function(chunk_size, execution_type)
