"""
性能监控器

遵循base-rules.md规范：
- 函数复杂度控制，每个函数圈复杂度不超过10
- 函数参数不超过5个
- 使用描述性的函数命名
"""

import time
import psutil
import threading
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, field
from collections import defaultdict, deque
import statistics


@dataclass
class ExecutionRecord:
    """执行记录数据类"""
    timestamp: float
    task_count: int
    execution_time: float
    worker_count: int
    execution_type: str
    cpu_usage: float = 0.0
    memory_usage: float = 0.0
    throughput: float = 0.0
    
    def __post_init__(self):
        """计算吞吐量"""
        if self.execution_time > 0:
            self.throughput = self.task_count / self.execution_time


@dataclass
class PerformanceStats:
    """性能统计数据类"""
    total_executions: int = 0
    total_tasks: int = 0
    total_time: float = 0.0
    average_throughput: float = 0.0
    peak_throughput: float = 0.0
    average_cpu_usage: float = 0.0
    peak_cpu_usage: float = 0.0
    average_memory_usage: float = 0.0
    peak_memory_usage: float = 0.0
    execution_history: List[ExecutionRecord] = field(default_factory=list)


class PerformanceMonitor:
    """
    性能监控器类
    
    监控并行执行的性能指标：
    - 执行时间和吞吐量
    - CPU和内存使用率
    - 工作进程效率
    - 历史性能趋势
    """
    
    def __init__(self, history_size: int = 1000) -> None:
        """
        初始化性能监控器
        
        Args:
            history_size: 历史记录保存数量
        """
        self.history_size = history_size
        self.execution_records = deque(maxlen=history_size)
        self.stats_by_type = defaultdict(lambda: PerformanceStats())
        self.lock = threading.Lock()
        
        # 系统资源监控
        self.process = psutil.Process()
        self.system_stats = {
            "cpu_percent": deque(maxlen=100),
            "memory_percent": deque(maxlen=100),
            "disk_io": deque(maxlen=100),
            "network_io": deque(maxlen=100)
        }
        
        # 启动后台监控线程
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_system_resources, daemon=True)
        self.monitor_thread.start()
    
    def record_execution(self, 
                        task_count: int,
                        execution_time: float,
                        worker_count: int,
                        execution_type: str) -> None:
        """
        记录执行性能
        
        Args:
            task_count: 任务数量
            execution_time: 执行时间
            worker_count: 工作进程数
            execution_type: 执行类型
        """
        with self.lock:
            # 获取当前系统资源使用情况
            cpu_usage = self.process.cpu_percent()
            memory_info = self.process.memory_info()
            memory_usage = memory_info.rss / 1024 / 1024  # MB
            
            # 创建执行记录
            record = ExecutionRecord(
                timestamp=time.time(),
                task_count=task_count,
                execution_time=execution_time,
                worker_count=worker_count,
                execution_type=execution_type,
                cpu_usage=cpu_usage,
                memory_usage=memory_usage
            )
            
            # 添加到历史记录
            self.execution_records.append(record)
            
            # 更新统计信息
            self._update_stats(record)
    
    def _update_stats(self, record: ExecutionRecord) -> None:
        """
        更新统计信息
        
        Args:
            record: 执行记录
        """
        stats = self.stats_by_type[record.execution_type]
        
        stats.total_executions += 1
        stats.total_tasks += record.task_count
        stats.total_time += record.execution_time
        
        # 更新吞吐量统计
        if stats.total_time > 0:
            stats.average_throughput = stats.total_tasks / stats.total_time
        
        if record.throughput > stats.peak_throughput:
            stats.peak_throughput = record.throughput
        
        # 更新CPU使用率统计
        if stats.total_executions == 1:
            stats.average_cpu_usage = record.cpu_usage
        else:
            stats.average_cpu_usage = (
                (stats.average_cpu_usage * (stats.total_executions - 1) + record.cpu_usage) 
                / stats.total_executions
            )
        
        if record.cpu_usage > stats.peak_cpu_usage:
            stats.peak_cpu_usage = record.cpu_usage
        
        # 更新内存使用率统计
        if stats.total_executions == 1:
            stats.average_memory_usage = record.memory_usage
        else:
            stats.average_memory_usage = (
                (stats.average_memory_usage * (stats.total_executions - 1) + record.memory_usage)
                / stats.total_executions
            )
        
        if record.memory_usage > stats.peak_memory_usage:
            stats.peak_memory_usage = record.memory_usage
        
        # 保持历史记录
        stats.execution_history.append(record)
        if len(stats.execution_history) > self.history_size:
            stats.execution_history.pop(0)
    
    def _monitor_system_resources(self) -> None:
        """后台监控系统资源"""
        while self.monitoring:
            try:
                # CPU使用率
                cpu_percent = psutil.cpu_percent(interval=1)
                self.system_stats["cpu_percent"].append(cpu_percent)
                
                # 内存使用率
                memory = psutil.virtual_memory()
                self.system_stats["memory_percent"].append(memory.percent)
                
                # 磁盘IO
                disk_io = psutil.disk_io_counters()
                if disk_io:
                    self.system_stats["disk_io"].append({
                        "read_bytes": disk_io.read_bytes,
                        "write_bytes": disk_io.write_bytes
                    })
                
                # 网络IO
                network_io = psutil.net_io_counters()
                if network_io:
                    self.system_stats["network_io"].append({
                        "bytes_sent": network_io.bytes_sent,
                        "bytes_recv": network_io.bytes_recv
                    })
                
            except Exception:
                # 忽略监控错误，继续运行
                pass
            
            time.sleep(1)
    
    def get_stats(self, execution_type: Optional[str] = None) -> Dict[str, Any]:
        """
        获取性能统计信息
        
        Args:
            execution_type: 执行类型，None表示所有类型
            
        Returns:
            Dict[str, Any]: 统计信息
        """
        with self.lock:
            if execution_type:
                stats = self.stats_by_type.get(execution_type, PerformanceStats())
                return self._format_stats(execution_type, stats)
            else:
                # 返回所有类型的统计
                all_stats = {}
                for exec_type, stats in self.stats_by_type.items():
                    all_stats[exec_type] = self._format_stats(exec_type, stats)
                
                # 添加系统资源统计
                all_stats["system_resources"] = self._get_system_resource_stats()
                
                return all_stats
    
    def _format_stats(self, execution_type: str, stats: PerformanceStats) -> Dict[str, Any]:
        """
        格式化统计信息
        
        Args:
            execution_type: 执行类型
            stats: 统计数据
            
        Returns:
            Dict[str, Any]: 格式化的统计信息
        """
        return {
            "execution_type": execution_type,
            "total_executions": stats.total_executions,
            "total_tasks": stats.total_tasks,
            "total_time": round(stats.total_time, 2),
            "average_throughput": round(stats.average_throughput, 2),
            "peak_throughput": round(stats.peak_throughput, 2),
            "average_cpu_usage": round(stats.average_cpu_usage, 2),
            "peak_cpu_usage": round(stats.peak_cpu_usage, 2),
            "average_memory_usage": round(stats.average_memory_usage, 2),
            "peak_memory_usage": round(stats.peak_memory_usage, 2),
            "efficiency_score": self._calculate_efficiency_score(stats)
        }
    
    def _calculate_efficiency_score(self, stats: PerformanceStats) -> float:
        """
        计算效率评分
        
        Args:
            stats: 统计数据
            
        Returns:
            float: 效率评分 (0-100)
        """
        if stats.total_executions == 0:
            return 0.0
        
        # 基于吞吐量、CPU使用率和内存使用率计算效率
        throughput_score = min(100, stats.average_throughput * 10)  # 假设10任务/秒为满分
        cpu_efficiency = max(0, 100 - stats.average_cpu_usage)  # CPU使用率越低效率越高
        memory_efficiency = max(0, 100 - min(100, stats.average_memory_usage / 10))  # 内存使用合理范围
        
        # 加权平均
        efficiency_score = (throughput_score * 0.5 + cpu_efficiency * 0.3 + memory_efficiency * 0.2)
        
        return round(efficiency_score, 2)
    
    def _get_system_resource_stats(self) -> Dict[str, Any]:
        """获取系统资源统计"""
        system_stats = {}
        
        # CPU统计
        if self.system_stats["cpu_percent"]:
            cpu_data = list(self.system_stats["cpu_percent"])
            system_stats["cpu"] = {
                "current": cpu_data[-1] if cpu_data else 0,
                "average": round(statistics.mean(cpu_data), 2),
                "peak": round(max(cpu_data), 2),
                "min": round(min(cpu_data), 2)
            }
        
        # 内存统计
        if self.system_stats["memory_percent"]:
            memory_data = list(self.system_stats["memory_percent"])
            system_stats["memory"] = {
                "current": memory_data[-1] if memory_data else 0,
                "average": round(statistics.mean(memory_data), 2),
                "peak": round(max(memory_data), 2),
                "min": round(min(memory_data), 2)
            }
        
        return system_stats
    
    def get_recent_performance(self, minutes: int = 10) -> List[Dict[str, Any]]:
        """
        获取最近的性能记录
        
        Args:
            minutes: 最近多少分钟
            
        Returns:
            List[Dict[str, Any]]: 性能记录列表
        """
        cutoff_time = time.time() - (minutes * 60)
        recent_records = []
        
        with self.lock:
            for record in self.execution_records:
                if record.timestamp >= cutoff_time:
                    recent_records.append({
                        "timestamp": record.timestamp,
                        "task_count": record.task_count,
                        "execution_time": round(record.execution_time, 2),
                        "worker_count": record.worker_count,
                        "execution_type": record.execution_type,
                        "cpu_usage": round(record.cpu_usage, 2),
                        "memory_usage": round(record.memory_usage, 2),
                        "throughput": round(record.throughput, 2)
                    })
        
        return recent_records
    
    def get_performance_trends(self, execution_type: Optional[str] = None) -> Dict[str, Any]:
        """
        获取性能趋势分析
        
        Args:
            execution_type: 执行类型
            
        Returns:
            Dict[str, Any]: 趋势分析结果
        """
        with self.lock:
            records = []
            
            if execution_type:
                records = [r for r in self.execution_records if r.execution_type == execution_type]
            else:
                records = list(self.execution_records)
            
            if len(records) < 2:
                return {"trend": "insufficient_data"}
            
            # 计算趋势
            recent_records = records[-10:]  # 最近10次执行
            older_records = records[-20:-10] if len(records) >= 20 else records[:-10]
            
            if not older_records:
                return {"trend": "insufficient_data"}
            
            recent_avg_throughput = statistics.mean([r.throughput for r in recent_records])
            older_avg_throughput = statistics.mean([r.throughput for r in older_records])
            
            throughput_change = ((recent_avg_throughput - older_avg_throughput) / older_avg_throughput) * 100
            
            # 判断趋势
            if throughput_change > 5:
                trend = "improving"
            elif throughput_change < -5:
                trend = "declining"
            else:
                trend = "stable"
            
            return {
                "trend": trend,
                "throughput_change_percent": round(throughput_change, 2),
                "recent_avg_throughput": round(recent_avg_throughput, 2),
                "older_avg_throughput": round(older_avg_throughput, 2),
                "sample_size": len(records)
            }
    
    def reset(self) -> None:
        """重置所有统计信息"""
        with self.lock:
            self.execution_records.clear()
            self.stats_by_type.clear()
            for key in self.system_stats:
                self.system_stats[key].clear()
    
    def stop_monitoring(self) -> None:
        """停止后台监控"""
        self.monitoring = False
        if self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=2)
    
    def export_stats(self, filepath: str) -> None:
        """
        导出统计信息到文件
        
        Args:
            filepath: 文件路径
        """
        import json
        
        stats_data = {
            "export_time": time.time(),
            "stats": self.get_stats(),
            "recent_performance": self.get_recent_performance(60),  # 最近1小时
            "trends": {}
        }
        
        # 添加各类型的趋势分析
        for exec_type in self.stats_by_type.keys():
            stats_data["trends"][exec_type] = self.get_performance_trends(exec_type)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(stats_data, f, indent=2, ensure_ascii=False)
    
    def __del__(self):
        """析构函数，停止监控线程"""
        self.stop_monitoring()
