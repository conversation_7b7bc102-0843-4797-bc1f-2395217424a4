"""
并行执行器

遵循base-rules.md规范：
- 函数复杂度控制，每个函数圈复杂度不超过10
- 函数参数不超过5个
- 使用描述性的函数命名
"""

import os
import time
import multiprocessing as mp
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor, as_completed
from typing import List, Dict, Any, Callable, Optional, Union
import logging

from src.core.parallel.performance_monitor import PerformanceMonitor


class ParallelExecutor:
    """
    并行执行器类
    
    提供多种并行执行策略：
    - 进程池并行
    - 线程池并行
    - 异步并行
    - 批处理并行
    """
    
    def __init__(self, 
                 max_workers: Optional[int] = None,
                 execution_type: str = "process") -> None:
        """
        初始化并行执行器
        
        Args:
            max_workers: 最大工作进程/线程数
            execution_type: 执行类型，process或thread
        """
        self.max_workers = max_workers or min(32, (os.cpu_count() or 1) + 4)
        self.execution_type = execution_type
        self.performance_monitor = PerformanceMonitor()
        self.logger = logging.getLogger(__name__)
        
        # 根据执行类型选择执行器
        if execution_type == "process":
            self.executor_class = ProcessPoolExecutor
        elif execution_type == "thread":
            self.executor_class = ThreadPoolExecutor
        else:
            raise ValueError(f"不支持的执行类型: {execution_type}")
    
    def execute_parallel(self, 
                        func: Callable,
                        tasks: List[Any],
                        chunk_size: Optional[int] = None) -> List[Any]:
        """
        并行执行任务列表
        
        Args:
            func: 要执行的函数
            tasks: 任务列表
            chunk_size: 批处理大小
            
        Returns:
            List[Any]: 执行结果列表
        """
        if not tasks:
            return []
        
        start_time = time.time()
        
        # 自动确定批处理大小
        if chunk_size is None:
            chunk_size = max(1, len(tasks) // (self.max_workers * 4))
        
        results = []
        
        try:
            with self.executor_class(max_workers=self.max_workers) as executor:
                # 提交任务
                future_to_task = {}
                
                if chunk_size > 1:
                    # 批处理模式
                    chunks = self._create_chunks(tasks, chunk_size)
                    for chunk in chunks:
                        future = executor.submit(self._execute_chunk, func, chunk)
                        future_to_task[future] = chunk
                else:
                    # 单任务模式
                    for task in tasks:
                        future = executor.submit(func, task)
                        future_to_task[future] = task
                
                # 收集结果
                for future in as_completed(future_to_task):
                    try:
                        result = future.result()
                        if chunk_size > 1:
                            results.extend(result)
                        else:
                            results.append(result)
                    except Exception as e:
                        self.logger.error(f"任务执行失败: {e}")
                        if chunk_size > 1:
                            results.extend([None] * len(future_to_task[future]))
                        else:
                            results.append(None)
        
        except Exception as e:
            self.logger.error(f"并行执行失败: {e}")
            raise
        
        execution_time = time.time() - start_time
        
        # 记录性能指标
        self.performance_monitor.record_execution(
            task_count=len(tasks),
            execution_time=execution_time,
            worker_count=self.max_workers,
            execution_type=self.execution_type
        )
        
        self.logger.info(f"并行执行完成: {len(tasks)}个任务, 耗时{execution_time:.2f}秒")
        
        return results
    
    def _create_chunks(self, tasks: List[Any], chunk_size: int) -> List[List[Any]]:
        """
        将任务列表分割成批次
        
        Args:
            tasks: 任务列表
            chunk_size: 批次大小
            
        Returns:
            List[List[Any]]: 批次列表
        """
        chunks = []
        for i in range(0, len(tasks), chunk_size):
            chunks.append(tasks[i:i + chunk_size])
        return chunks
    
    def _execute_chunk(self, func: Callable, chunk: List[Any]) -> List[Any]:
        """
        执行一个批次的任务
        
        Args:
            func: 执行函数
            chunk: 任务批次
            
        Returns:
            List[Any]: 批次执行结果
        """
        results = []
        for task in chunk:
            try:
                result = func(task)
                results.append(result)
            except Exception as e:
                self.logger.error(f"批次任务执行失败: {e}")
                results.append(None)
        return results
    
    def execute_map_reduce(self, 
                          map_func: Callable,
                          reduce_func: Callable,
                          tasks: List[Any],
                          chunk_size: Optional[int] = None) -> Any:
        """
        执行MapReduce模式的并行计算
        
        Args:
            map_func: Map函数
            reduce_func: Reduce函数
            tasks: 任务列表
            chunk_size: 批处理大小
            
        Returns:
            Any: 最终结果
        """
        # Map阶段
        map_results = self.execute_parallel(map_func, tasks, chunk_size)
        
        # Reduce阶段
        final_result = map_results[0] if map_results else None
        for result in map_results[1:]:
            if result is not None:
                final_result = reduce_func(final_result, result)
        
        return final_result
    
    def execute_pipeline(self, 
                        pipeline_stages: List[Callable],
                        initial_data: Any) -> Any:
        """
        执行流水线并行处理
        
        Args:
            pipeline_stages: 流水线阶段函数列表
            initial_data: 初始数据
            
        Returns:
            Any: 最终处理结果
        """
        current_data = initial_data
        
        for stage_func in pipeline_stages:
            if isinstance(current_data, list):
                # 如果当前数据是列表，并行处理每个元素
                current_data = self.execute_parallel(stage_func, current_data)
            else:
                # 如果是单个数据，直接处理
                current_data = stage_func(current_data)
        
        return current_data
    
    def execute_with_timeout(self, 
                           func: Callable,
                           tasks: List[Any],
                           timeout_seconds: float) -> List[Any]:
        """
        带超时的并行执行
        
        Args:
            func: 执行函数
            tasks: 任务列表
            timeout_seconds: 超时时间（秒）
            
        Returns:
            List[Any]: 执行结果列表
        """
        results = [None] * len(tasks)
        
        try:
            with self.executor_class(max_workers=self.max_workers) as executor:
                # 提交所有任务
                future_to_index = {}
                for i, task in enumerate(tasks):
                    future = executor.submit(func, task)
                    future_to_index[future] = i
                
                # 等待结果，带超时
                completed_count = 0
                for future in as_completed(future_to_index, timeout=timeout_seconds):
                    try:
                        result = future.result()
                        index = future_to_index[future]
                        results[index] = result
                        completed_count += 1
                    except Exception as e:
                        self.logger.error(f"任务执行失败: {e}")
                        index = future_to_index[future]
                        results[index] = None
                
                self.logger.info(f"超时执行完成: {completed_count}/{len(tasks)}个任务完成")
        
        except Exception as e:
            self.logger.error(f"超时并行执行失败: {e}")
        
        return results
    
    def get_optimal_worker_count(self, 
                               sample_func: Callable,
                               sample_tasks: List[Any]) -> int:
        """
        获取最优工作进程数
        
        Args:
            sample_func: 样本函数
            sample_tasks: 样本任务
            
        Returns:
            int: 最优工作进程数
        """
        if not sample_tasks:
            return self.max_workers
        
        # 测试不同的工作进程数
        worker_counts = [1, 2, 4, 8, 16, min(32, mp.cpu_count())]
        worker_counts = [w for w in worker_counts if w <= self.max_workers]
        
        best_worker_count = 1
        best_throughput = 0
        
        for worker_count in worker_counts:
            # 创建临时执行器
            temp_executor = ParallelExecutor(
                max_workers=worker_count,
                execution_type=self.execution_type
            )
            
            start_time = time.time()
            temp_executor.execute_parallel(sample_func, sample_tasks[:min(10, len(sample_tasks))])
            execution_time = time.time() - start_time
            
            # 计算吞吐量
            throughput = len(sample_tasks) / execution_time if execution_time > 0 else 0
            
            if throughput > best_throughput:
                best_throughput = throughput
                best_worker_count = worker_count
            
            self.logger.info(f"工作进程数{worker_count}: 吞吐量{throughput:.2f}任务/秒")
        
        self.logger.info(f"最优工作进程数: {best_worker_count}")
        return best_worker_count
    
    def execute_adaptive(self, 
                        func: Callable,
                        tasks: List[Any],
                        sample_size: int = 10) -> List[Any]:
        """
        自适应并行执行
        
        Args:
            func: 执行函数
            tasks: 任务列表
            sample_size: 样本大小
            
        Returns:
            List[Any]: 执行结果列表
        """
        if len(tasks) <= sample_size:
            return self.execute_parallel(func, tasks)
        
        # 使用样本确定最优配置
        sample_tasks = tasks[:sample_size]
        optimal_workers = self.get_optimal_worker_count(func, sample_tasks)
        
        # 使用最优配置执行所有任务
        original_workers = self.max_workers
        self.max_workers = optimal_workers
        
        try:
            results = self.execute_parallel(func, tasks)
        finally:
            self.max_workers = original_workers
        
        return results
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """
        获取性能统计信息
        
        Returns:
            Dict[str, Any]: 性能统计
        """
        return self.performance_monitor.get_stats()
    
    def reset_performance_stats(self) -> None:
        """重置性能统计"""
        self.performance_monitor.reset()


# 全局并行执行器实例
_global_executor = None

def get_global_executor() -> ParallelExecutor:
    """获取全局并行执行器实例"""
    global _global_executor
    if _global_executor is None:
        _global_executor = ParallelExecutor()
    return _global_executor

def set_global_executor(executor: ParallelExecutor) -> None:
    """设置全局并行执行器实例"""
    global _global_executor
    _global_executor = executor

# 便捷函数
def parallel_map(func: Callable, tasks: List[Any], **kwargs) -> List[Any]:
    """并行映射函数"""
    executor = get_global_executor()
    return executor.execute_parallel(func, tasks, **kwargs)

def parallel_map_reduce(map_func: Callable, reduce_func: Callable, tasks: List[Any], **kwargs) -> Any:
    """并行MapReduce函数"""
    executor = get_global_executor()
    return executor.execute_map_reduce(map_func, reduce_func, tasks, **kwargs)
