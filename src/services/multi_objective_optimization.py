"""
多目标优化服务

遵循base-rules.md规范：
- 函数复杂度控制，每个函数圈复杂度不超过10
- 函数参数不超过5个
- 使用描述性的函数命名
- 模块职责单一性：只包含多目标优化相关的业务逻辑
"""

from typing import Optional, List, Dict, Any
from datetime import datetime
import numpy as np
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_

from src.database.models.multi_objective import (
    MultiObjectiveTask,
    ParetoSolution,
    ObjectiveFunction,
    MultiObjectiveStatusEnum,
    MultiObjectiveAlgorithmEnum,
    ObjectiveFunctionTypeEnum
)
from src.algorithms.multi_objective.nsga2 import NSGA2Algorithm
from src.algorithms.base import AlgorithmConfig


class MultiObjectiveOptimizationService:
    """
    多目标优化服务类
    
    提供多目标优化的业务逻辑处理：
    - 创建和管理多目标优化任务
    - 执行多目标优化算法
    - 帕累托前沿分析和可视化
    - 解的质量评估和比较
    """
    
    def __init__(self) -> None:
        """初始化多目标优化服务"""
        self.algorithm_registry = {
            MultiObjectiveAlgorithmEnum.NSGA2: NSGA2Algorithm
        }
    
    async def create_multi_objective_task(self, 
                                        session: AsyncSession,
                                        task_data: Dict[str, Any]) -> str:
        """
        创建多目标优化任务
        
        Args:
            session: 数据库会话
            task_data: 任务数据
            
        Returns:
            str: 任务ID
        """
        # 验证输入数据
        self._validate_multi_objective_data(task_data)
        
        # 创建多目标优化任务
        mo_task = MultiObjectiveTask(
            task_name=task_data["task_name"],
            task_description=task_data.get("task_description"),
            objective_functions=task_data["objective_functions"],
            objective_weights=task_data.get("objective_weights"),
            decision_variables=task_data["decision_variables"],
            variable_bounds=task_data["variable_bounds"],
            constraints=task_data.get("constraints", []),
            algorithm_type=MultiObjectiveAlgorithmEnum(task_data["algorithm_type"]),
            algorithm_parameters=task_data.get("algorithm_parameters", {}),
            population_size=task_data.get("population_size", 100),
            max_generations=task_data.get("max_generations", 1000),
            termination_criteria=task_data.get("termination_criteria"),
            created_by=task_data.get("created_by"),
            optimization_task_id=task_data.get("optimization_task_id")
        )
        
        session.add(mo_task)
        await session.commit()
        await session.refresh(mo_task)
        
        return mo_task.id
    
    def _validate_multi_objective_data(self, task_data: Dict[str, Any]) -> None:
        """
        验证多目标优化任务数据
        
        Args:
            task_data: 任务数据
            
        Raises:
            ValueError: 数据验证失败
        """
        required_fields = ["task_name", "objective_functions", "decision_variables", 
                          "variable_bounds", "algorithm_type"]
        
        for field in required_fields:
            if field not in task_data:
                raise ValueError(f"缺少必需字段: {field}")
        
        # 验证目标函数数量
        if len(task_data["objective_functions"]) < 2:
            raise ValueError("多目标优化至少需要2个目标函数")
        
        # 验证决策变量和边界匹配
        if len(task_data["decision_variables"]) != len(task_data["variable_bounds"]):
            raise ValueError("决策变量数量与变量边界数量不匹配")
        
        # 验证算法类型
        if task_data["algorithm_type"] not in [e.value for e in MultiObjectiveAlgorithmEnum]:
            raise ValueError(f"不支持的算法类型: {task_data['algorithm_type']}")
    
    async def execute_multi_objective_optimization(self, 
                                                 session: AsyncSession,
                                                 task_id: str) -> Dict[str, Any]:
        """
        执行多目标优化
        
        Args:
            session: 数据库会话
            task_id: 任务ID
            
        Returns:
            Dict[str, Any]: 优化结果
        """
        # 获取任务信息
        task = await self._get_task_by_id(session, task_id)
        if not task:
            raise ValueError(f"任务不存在: {task_id}")
        
        if task.status != MultiObjectiveStatusEnum.PENDING:
            raise ValueError(f"任务状态不允许执行: {task.status}")
        
        try:
            # 更新任务状态
            task.status = MultiObjectiveStatusEnum.OPTIMIZING
            task.started_at = datetime.utcnow()
            await session.commit()
            
            # 准备算法输入
            problem_definition = self._prepare_multi_objective_problem(task)
            
            # 创建并配置算法
            algorithm_class = self.algorithm_registry[task.algorithm_type]
            config = self._create_multi_objective_config(task)
            algorithm = algorithm_class(config)
            
            # 执行优化
            result = algorithm.optimize(
                problem_definition=problem_definition,
                objective_function=None,  # 多目标优化不使用单一目标函数
                constraints=[]
            )
            
            # 解析帕累托前沿
            pareto_solutions = self._extract_pareto_solutions(result.best_solution, task)
            
            # 计算质量指标
            quality_indicators = self._calculate_quality_indicators(pareto_solutions)
            
            # 保存结果
            await self._save_pareto_solutions(session, task, pareto_solutions)
            
            # 更新任务状态
            task.status = MultiObjectiveStatusEnum.COMPLETED
            task.completed_at = datetime.utcnow()
            task.optimization_duration = int((task.completed_at - task.started_at).total_seconds())
            task.pareto_solutions = [sol.to_dict() for sol in pareto_solutions]
            task.quality_indicators = quality_indicators
            task.hypervolume = quality_indicators.get("hypervolume", 0.0)
            task.spacing = quality_indicators.get("spacing", 0.0)
            task.spread = quality_indicators.get("spread", 0.0)
            
            await session.commit()
            
            return {
                "task_id": task_id,
                "status": "completed",
                "pareto_front_size": len(pareto_solutions),
                "quality_indicators": quality_indicators,
                "execution_time": task.optimization_duration
            }
            
        except Exception as e:
            # 更新任务状态为失败
            task.status = MultiObjectiveStatusEnum.FAILED
            task.error_message = str(e)
            task.completed_at = datetime.utcnow()
            await session.commit()
            
            raise e
    
    async def _get_task_by_id(self, session: AsyncSession, task_id: str) -> Optional[MultiObjectiveTask]:
        """获取任务信息"""
        result = await session.execute(
            select(MultiObjectiveTask).where(MultiObjectiveTask.id == task_id)
        )
        return result.scalar_one_or_none()
    
    def _prepare_multi_objective_problem(self, task: MultiObjectiveTask) -> Dict[str, Any]:
        """
        准备多目标优化问题定义
        
        Args:
            task: 多目标优化任务
            
        Returns:
            Dict[str, Any]: 问题定义
        """
        # 解析决策变量
        gene_length = len(task.decision_variables)
        gene_bounds = []
        
        for var_name in task.decision_variables:
            if var_name in task.variable_bounds:
                bounds = task.variable_bounds[var_name]
                gene_bounds.append((bounds["lower"], bounds["upper"]))
            else:
                gene_bounds.append((0.0, 1.0))  # 默认边界
        
        # 创建目标函数
        objective_functions = []
        for obj_def in task.objective_functions:
            obj_func = self._create_objective_function(obj_def)
            objective_functions.append(obj_func)
        
        return {
            "gene_length": gene_length,
            "gene_bounds": gene_bounds,
            "num_objectives": len(task.objective_functions),
            "objective_functions": objective_functions,
            "constraints": task.constraints or []
        }
    
    def _create_objective_function(self, obj_definition: Dict[str, Any]):
        """
        创建目标函数
        
        Args:
            obj_definition: 目标函数定义
            
        Returns:
            Callable: 目标函数
        """
        obj_type = obj_definition.get("type", "minimize")
        expression = obj_definition.get("expression", "sum(x**2 for x in genes)")
        
        def objective_function(solution: Dict[str, Any]) -> float:
            """目标函数实现"""
            genes = solution.get("genes", [])
            
            # 简化的目标函数实现
            try:
                # 创建局部变量环境
                local_vars = {"genes": genes, "x": genes, "sum": sum, "len": len}
                
                # 评估表达式
                result = eval(expression, {"__builtins__": {}}, local_vars)
                
                # 如果是最大化问题，取负值
                if obj_type == "maximize":
                    result = -result
                
                return float(result)
                
            except Exception:
                # 如果表达式评估失败，返回默认值
                if "sphere" in expression.lower():
                    return sum(x**2 for x in genes)
                elif "rosenbrock" in expression.lower():
                    return sum(100*(genes[i+1] - genes[i]**2)**2 + (1 - genes[i])**2 
                              for i in range(len(genes)-1))
                else:
                    return sum(abs(x) for x in genes)
        
        return objective_function
    
    def _create_multi_objective_config(self, task: MultiObjectiveTask) -> AlgorithmConfig:
        """创建多目标算法配置"""
        algorithm_params = task.algorithm_parameters.copy()
        algorithm_params.update({
            "num_objectives": len(task.objective_functions),
            "crossover_rate": algorithm_params.get("crossover_rate", 0.9),
            "mutation_rate": algorithm_params.get("mutation_rate", 0.1)
        })
        
        return AlgorithmConfig(
            max_iterations=task.max_generations,
            population_size=task.population_size,
            convergence_tolerance=algorithm_params.get("convergence_tolerance", 1e-6),
            max_time_seconds=algorithm_params.get("max_time_seconds", 3600),
            algorithm_params=algorithm_params
        )
    
    def _extract_pareto_solutions(self, 
                                best_solution: Dict[str, Any],
                                task: MultiObjectiveTask) -> List['ParetoSolutionData']:
        """
        提取帕累托解
        
        Args:
            best_solution: 算法返回的最佳解
            task: 多目标优化任务
            
        Returns:
            List[ParetoSolutionData]: 帕累托解列表
        """
        pareto_solutions = []
        pareto_front = best_solution.get("pareto_front", [])
        
        for i, solution in enumerate(pareto_front):
            pareto_sol = ParetoSolutionData(
                solution_id=f"sol_{i}",
                decision_variables=solution.get("genes", []),
                objective_values=solution.get("objectives", []),
                dominance_rank=solution.get("rank", 0),
                crowding_distance=solution.get("crowding_distance", 0.0),
                generation=best_solution.get("generation", 0)
            )
            pareto_solutions.append(pareto_sol)
        
        return pareto_solutions
    
    def _calculate_quality_indicators(self, pareto_solutions: List['ParetoSolutionData']) -> Dict[str, Any]:
        """计算质量指标"""
        if not pareto_solutions:
            return {"hypervolume": 0.0, "spacing": 0.0, "spread": 0.0}
        
        # 提取目标值矩阵
        objectives_matrix = np.array([sol.objective_values for sol in pareto_solutions])
        
        # 计算超体积（简化实现）
        hypervolume = self._calculate_hypervolume_simple(objectives_matrix)
        
        # 计算间距指标
        spacing = self._calculate_spacing_metric(objectives_matrix)
        
        # 计算分布性指标
        spread = self._calculate_spread_metric(objectives_matrix)
        
        return {
            "hypervolume": hypervolume,
            "spacing": spacing,
            "spread": spread,
            "pareto_front_size": len(pareto_solutions),
            "objective_ranges": self._calculate_objective_ranges(objectives_matrix)
        }
    
    def _calculate_hypervolume_simple(self, objectives_matrix: np.ndarray) -> float:
        """简化的超体积计算"""
        if objectives_matrix.shape[1] != 2:
            return 0.0  # 只支持2D情况
        
        # 排序
        sorted_indices = np.argsort(objectives_matrix[:, 0])
        sorted_objectives = objectives_matrix[sorted_indices]
        
        # 计算参考点
        ref_point = np.max(objectives_matrix, axis=0) + 1
        
        hypervolume = 0.0
        for i in range(len(sorted_objectives)):
            if i == 0:
                width = ref_point[0] - sorted_objectives[i, 0]
            else:
                width = sorted_objectives[i-1, 0] - sorted_objectives[i, 0]
            
            height = ref_point[1] - sorted_objectives[i, 1]
            hypervolume += width * height
        
        return float(hypervolume)
    
    def _calculate_spacing_metric(self, objectives_matrix: np.ndarray) -> float:
        """计算间距指标"""
        if len(objectives_matrix) < 2:
            return 0.0
        
        distances = []
        for i in range(len(objectives_matrix)):
            min_distance = float('inf')
            for j in range(len(objectives_matrix)):
                if i != j:
                    distance = np.linalg.norm(objectives_matrix[i] - objectives_matrix[j])
                    min_distance = min(min_distance, distance)
            distances.append(min_distance)
        
        mean_distance = np.mean(distances)
        variance = np.var(distances)
        
        return float(np.sqrt(variance))
    
    def _calculate_spread_metric(self, objectives_matrix: np.ndarray) -> float:
        """计算分布性指标"""
        if len(objectives_matrix) < 2:
            return 0.0
        
        # 计算每个目标的范围
        ranges = np.max(objectives_matrix, axis=0) - np.min(objectives_matrix, axis=0)
        
        return float(np.mean(ranges))
    
    def _calculate_objective_ranges(self, objectives_matrix: np.ndarray) -> Dict[str, Dict[str, float]]:
        """计算目标函数值范围"""
        ranges = {}
        
        for i in range(objectives_matrix.shape[1]):
            obj_values = objectives_matrix[:, i]
            ranges[f"objective_{i}"] = {
                "min": float(np.min(obj_values)),
                "max": float(np.max(obj_values)),
                "mean": float(np.mean(obj_values)),
                "std": float(np.std(obj_values))
            }
        
        return ranges
    
    async def _save_pareto_solutions(self, 
                                   session: AsyncSession,
                                   task: MultiObjectiveTask,
                                   pareto_solutions: List['ParetoSolutionData']) -> None:
        """保存帕累托解"""
        for sol_data in pareto_solutions:
            pareto_solution = ParetoSolution(
                task_id=task.id,
                solution_id=sol_data.solution_id,
                decision_variables={"variables": sol_data.decision_variables},
                objective_values=sol_data.objective_values,
                dominance_rank=sol_data.dominance_rank,
                crowding_distance=sol_data.crowding_distance,
                feasibility=True,  # 假设所有解都可行
                generation=sol_data.generation
            )
            
            session.add(pareto_solution)
        
        await session.commit()


# 辅助数据类
class ParetoSolutionData:
    """帕累托解数据类"""
    
    def __init__(self, solution_id: str, decision_variables: List[float], 
                 objective_values: List[float], dominance_rank: int = 0,
                 crowding_distance: float = 0.0, generation: int = 0):
        self.solution_id = solution_id
        self.decision_variables = decision_variables
        self.objective_values = objective_values
        self.dominance_rank = dominance_rank
        self.crowding_distance = crowding_distance
        self.generation = generation
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "solution_id": self.solution_id,
            "decision_variables": self.decision_variables,
            "objective_values": self.objective_values,
            "dominance_rank": self.dominance_rank,
            "crowding_distance": self.crowding_distance,
            "generation": self.generation
        }
