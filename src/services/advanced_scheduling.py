"""
高级资源调度服务

遵循base-rules.md规范：
- 函数复杂度控制，每个函数圈复杂度不超过10
- 函数参数不超过5个
- 使用描述性的函数命名
- 模块职责单一性：只包含高级调度相关的业务逻辑
"""

from typing import Optional, List, Dict, Any
from datetime import datetime
import numpy as np
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_

from src.database.models.optimization import (
    OptimizationTask,
    OptimizationResult,
    AlgorithmConfiguration,
    OptimizationTaskTypeEnum,
    OptimizationStatusEnum,
    AlgorithmTypeEnum
)
from src.algorithms.evolutionary.genetic_algorithm import GeneticAlgorithm
from src.algorithms.evolutionary.particle_swarm import ParticleSwarmOptimization
from src.algorithms.local_search.simulated_annealing import SimulatedAnnealing
from src.algorithms.base import AlgorithmConfig


class AdvancedSchedulingService:
    """
    高级资源调度服务类
    
    提供高级调度算法的业务逻辑处理：
    - 创建和管理调度优化任务
    - 执行多种高级调度算法
    - 资源分配和约束处理
    - 调度方案评估和优化
    """
    
    def __init__(self) -> None:
        """初始化高级调度服务"""
        self.algorithm_registry = {
            AlgorithmTypeEnum.GENETIC_ALGORITHM: GeneticAlgorithm,
            AlgorithmTypeEnum.PARTICLE_SWARM: ParticleSwarmOptimization,
            AlgorithmTypeEnum.SIMULATED_ANNEALING: SimulatedAnnealing
        }
    
    async def create_scheduling_task(self, 
                                   session: AsyncSession,
                                   task_data: Dict[str, Any]) -> str:
        """
        创建资源调度优化任务
        
        Args:
            session: 数据库会话
            task_data: 任务数据
            
        Returns:
            str: 任务ID
        """
        # 验证输入数据
        self._validate_scheduling_data(task_data)
        
        # 创建优化任务
        optimization_task = OptimizationTask(
            task_name=task_data["task_name"],
            task_description=task_data.get("task_description"),
            task_type=OptimizationTaskTypeEnum.RESOURCE_SCHEDULING,
            algorithm_type=AlgorithmTypeEnum(task_data["algorithm_type"]),
            algorithm_config=task_data.get("algorithm_config", {}),
            problem_definition=task_data["problem_definition"],
            created_by=task_data.get("created_by"),
            scenario_id=task_data.get("scenario_id")
        )
        
        session.add(optimization_task)
        await session.commit()
        await session.refresh(optimization_task)
        
        return optimization_task.id
    
    def _validate_scheduling_data(self, task_data: Dict[str, Any]) -> None:
        """
        验证调度任务数据
        
        Args:
            task_data: 任务数据
            
        Raises:
            ValueError: 数据验证失败
        """
        required_fields = ["task_name", "algorithm_type", "problem_definition"]
        
        for field in required_fields:
            if field not in task_data:
                raise ValueError(f"缺少必需字段: {field}")
        
        # 验证算法类型
        if task_data["algorithm_type"] not in [e.value for e in AlgorithmTypeEnum]:
            raise ValueError(f"不支持的算法类型: {task_data['algorithm_type']}")
        
        # 验证问题定义
        problem_def = task_data["problem_definition"]
        if not isinstance(problem_def, dict):
            raise ValueError("问题定义必须是字典格式")
        
        required_problem_fields = ["resources", "tasks", "constraints"]
        for field in required_problem_fields:
            if field not in problem_def:
                raise ValueError(f"问题定义中缺少必需字段: {field}")
    
    async def execute_scheduling_optimization(self, 
                                            session: AsyncSession,
                                            task_id: str) -> Dict[str, Any]:
        """
        执行资源调度优化
        
        Args:
            session: 数据库会话
            task_id: 任务ID
            
        Returns:
            Dict[str, Any]: 优化结果
        """
        # 获取任务信息
        task = await self._get_task_by_id(session, task_id)
        if not task:
            raise ValueError(f"任务不存在: {task_id}")
        
        if task.status != OptimizationStatusEnum.PENDING:
            raise ValueError(f"任务状态不允许执行: {task.status}")
        
        try:
            # 更新任务状态
            task.status = OptimizationStatusEnum.RUNNING
            task.started_at = datetime.utcnow()
            await session.commit()
            
            # 准备算法输入
            problem_definition = self._prepare_scheduling_problem(task.problem_definition)
            
            # 创建并配置算法
            algorithm_class = self.algorithm_registry[task.algorithm_type]
            config = self._create_algorithm_config(task.algorithm_config)
            algorithm = algorithm_class(config)
            
            # 定义目标函数
            objective_function = self._create_scheduling_objective_function(task.problem_definition)
            
            # 定义约束条件
            constraints = self._create_scheduling_constraints(task.problem_definition)
            
            # 执行优化
            result = algorithm.optimize(
                problem_definition=problem_definition,
                objective_function=objective_function,
                constraints=constraints
            )
            
            # 解析和验证结果
            scheduling_solution = self._parse_scheduling_solution(result.best_solution, task.problem_definition)
            
            # 保存结果
            await self._save_scheduling_result(session, task, result, scheduling_solution)
            
            # 更新任务状态
            task.status = OptimizationStatusEnum.COMPLETED
            task.completed_at = datetime.utcnow()
            task.execution_duration = int((task.completed_at - task.started_at).total_seconds())
            task.result_data = scheduling_solution
            task.performance_metrics = {
                "execution_time": result.execution_time_seconds,
                "iterations": result.iterations_completed,
                "converged": result.converged,
                "final_fitness": result.best_fitness
            }
            
            await session.commit()
            
            return {
                "task_id": task_id,
                "status": "completed",
                "scheduling_solution": scheduling_solution,
                "optimization_metrics": task.performance_metrics,
                "execution_time": task.execution_duration
            }
            
        except Exception as e:
            # 更新任务状态为失败
            task.status = OptimizationStatusEnum.FAILED
            task.error_message = str(e)
            task.completed_at = datetime.utcnow()
            await session.commit()
            
            raise e
    
    async def _get_task_by_id(self, session: AsyncSession, task_id: str) -> Optional[OptimizationTask]:
        """获取任务信息"""
        result = await session.execute(
            select(OptimizationTask).where(OptimizationTask.id == task_id)
        )
        return result.scalar_one_or_none()
    
    def _prepare_scheduling_problem(self, problem_definition: Dict[str, Any]) -> Dict[str, Any]:
        """
        准备调度问题定义
        
        Args:
            problem_definition: 原始问题定义
            
        Returns:
            Dict[str, Any]: 算法可用的问题定义
        """
        resources = problem_definition["resources"]
        tasks = problem_definition["tasks"]
        
        # 计算问题维度
        num_tasks = len(tasks)
        num_resources = len(resources)
        
        # 为遗传算法准备编码
        if "encoding" not in problem_definition:
            # 默认使用整数编码：每个任务分配到哪个资源
            problem_definition["encoding"] = "integer"
            problem_definition["gene_length"] = num_tasks
            problem_definition["gene_bounds"] = [(0, num_resources - 1)] * num_tasks
        
        # 为粒子群算法准备维度
        problem_definition["dimension"] = num_tasks
        problem_definition["position_bounds"] = [(0, num_resources - 1)] * num_tasks
        
        return problem_definition
    
    def _create_algorithm_config(self, algorithm_config: Dict[str, Any]) -> AlgorithmConfig:
        """创建算法配置"""
        return AlgorithmConfig(
            max_iterations=algorithm_config.get("max_iterations", 1000),
            population_size=algorithm_config.get("population_size", 50),
            convergence_tolerance=algorithm_config.get("convergence_tolerance", 1e-6),
            max_time_seconds=algorithm_config.get("max_time_seconds", 300),
            parallel_execution=algorithm_config.get("parallel_execution", False),
            algorithm_params=algorithm_config
        )
    
    def _create_scheduling_objective_function(self, problem_definition: Dict[str, Any]):
        """
        创建调度目标函数
        
        Args:
            problem_definition: 问题定义
            
        Returns:
            Callable: 目标函数
        """
        resources = problem_definition["resources"]
        tasks = problem_definition["tasks"]
        
        def objective_function(solution: Dict[str, Any]) -> float:
            """调度目标函数：最小化总完成时间和资源成本"""
            if "genes" in solution:
                # 遗传算法解格式
                assignment = solution["genes"]
            elif "position" in solution:
                # 粒子群算法解格式
                assignment = [int(round(x)) for x in solution["position"]]
            elif "variables" in solution:
                # 模拟退火算法解格式
                assignment = [int(round(x)) for x in solution["variables"]]
            else:
                return float('inf')
            
            # 确保分配有效
            if len(assignment) != len(tasks):
                return float('inf')
            
            # 计算总成本
            total_cost = 0.0
            resource_loads = [0.0] * len(resources)
            
            for task_idx, resource_idx in enumerate(assignment):
                if resource_idx < 0 or resource_idx >= len(resources):
                    return float('inf')  # 无效分配
                
                task = tasks[task_idx]
                resource = resources[resource_idx]
                
                # 计算任务在该资源上的执行时间
                execution_time = task.get("duration", 1.0) / resource.get("efficiency", 1.0)
                
                # 计算成本
                task_cost = execution_time * resource.get("cost_per_hour", 1.0)
                total_cost += task_cost
                
                # 更新资源负载
                resource_loads[resource_idx] += execution_time
            
            # 添加负载均衡惩罚
            max_load = max(resource_loads)
            min_load = min(resource_loads)
            load_imbalance_penalty = (max_load - min_load) * 10.0
            
            return total_cost + load_imbalance_penalty
        
        return objective_function
    
    def _create_scheduling_constraints(self, problem_definition: Dict[str, Any]) -> List:
        """创建调度约束条件"""
        constraints = []
        constraint_definitions = problem_definition.get("constraints", [])
        
        for constraint_def in constraint_definitions:
            constraint_type = constraint_def.get("type")
            
            if constraint_type == "resource_capacity":
                constraints.append(self._create_capacity_constraint(constraint_def))
            elif constraint_type == "task_dependency":
                constraints.append(self._create_dependency_constraint(constraint_def))
            elif constraint_type == "time_window":
                constraints.append(self._create_time_window_constraint(constraint_def))
        
        return constraints
    
    def _create_capacity_constraint(self, constraint_def: Dict[str, Any]):
        """创建资源容量约束"""
        def capacity_constraint(solution) -> bool:
            # 简化实现：总是返回True
            # 实际应该检查资源容量限制
            return True
        
        return capacity_constraint
    
    def _create_dependency_constraint(self, constraint_def: Dict[str, Any]):
        """创建任务依赖约束"""
        def dependency_constraint(solution) -> bool:
            # 简化实现：总是返回True
            # 实际应该检查任务依赖关系
            return True
        
        return dependency_constraint
    
    def _create_time_window_constraint(self, constraint_def: Dict[str, Any]):
        """创建时间窗口约束"""
        def time_window_constraint(solution) -> bool:
            # 简化实现：总是返回True
            # 实际应该检查时间窗口限制
            return True
        
        return time_window_constraint
    
    def _parse_scheduling_solution(self, 
                                 best_solution: Dict[str, Any],
                                 problem_definition: Dict[str, Any]) -> Dict[str, Any]:
        """
        解析调度解决方案
        
        Args:
            best_solution: 算法返回的最佳解
            problem_definition: 问题定义
            
        Returns:
            Dict[str, Any]: 解析后的调度方案
        """
        resources = problem_definition["resources"]
        tasks = problem_definition["tasks"]
        
        # 提取任务分配
        if "genes" in best_solution:
            assignment = best_solution["genes"]
        elif "position" in best_solution:
            assignment = [int(round(x)) for x in best_solution["position"]]
        elif "variables" in best_solution:
            assignment = [int(round(x)) for x in best_solution["variables"]]
        else:
            assignment = []
        
        # 构建调度方案
        scheduling_plan = []
        resource_schedules = {i: [] for i in range(len(resources))}
        
        for task_idx, resource_idx in enumerate(assignment):
            if 0 <= resource_idx < len(resources):
                task_assignment = {
                    "task_id": tasks[task_idx].get("id", f"task_{task_idx}"),
                    "task_name": tasks[task_idx].get("name", f"Task {task_idx}"),
                    "resource_id": resources[resource_idx].get("id", f"resource_{resource_idx}"),
                    "resource_name": resources[resource_idx].get("name", f"Resource {resource_idx}"),
                    "estimated_duration": tasks[task_idx].get("duration", 1.0),
                    "estimated_cost": self._calculate_task_cost(tasks[task_idx], resources[resource_idx])
                }
                
                scheduling_plan.append(task_assignment)
                resource_schedules[resource_idx].append(task_assignment)
        
        return {
            "scheduling_plan": scheduling_plan,
            "resource_schedules": resource_schedules,
            "total_tasks": len(tasks),
            "total_resources": len(resources),
            "assignment_vector": assignment,
            "solution_quality": best_solution.get("fitness", float('inf'))
        }
    
    def _calculate_task_cost(self, task: Dict[str, Any], resource: Dict[str, Any]) -> float:
        """计算任务成本"""
        duration = task.get("duration", 1.0)
        efficiency = resource.get("efficiency", 1.0)
        cost_per_hour = resource.get("cost_per_hour", 1.0)
        
        execution_time = duration / efficiency
        return execution_time * cost_per_hour
    
    async def _save_scheduling_result(self, 
                                    session: AsyncSession,
                                    task: OptimizationTask,
                                    result,
                                    scheduling_solution: Dict[str, Any]) -> None:
        """保存调度结果"""
        optimization_result = OptimizationResult(
            task_id=task.id,
            solution_data=scheduling_solution,
            objective_values={"total_cost": result.best_fitness},
            constraint_violations=result.constraint_violations or {},
            convergence_data={"history": result.convergence_history},
            iteration_count=result.iterations_completed,
            solution_quality={"fitness": result.best_fitness, "feasible": result.feasible},
            computation_time=result.execution_time_seconds,
            memory_usage=0.0,  # 暂未实现内存监控
            cpu_usage=0.0      # 暂未实现CPU监控
        )
        
        session.add(optimization_result)
        await session.commit()
