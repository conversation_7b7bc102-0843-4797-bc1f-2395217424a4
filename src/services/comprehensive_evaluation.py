"""
综合评估服务

遵循base-rules.md规范：
- 函数复杂度控制，每个函数圈复杂度不超过10
- 函数参数不超过5个
- 使用描述性的函数命名
- 模块职责单一性：只包含综合评估相关的业务逻辑
"""

from typing import Optional, List, Dict, Any
from datetime import datetime
import numpy as np
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_

from src.database.models.evaluation import (
    EvaluationTask,
    WeightConfiguration,
    EvaluationResult,
    EvaluationStatusEnum,
    EvaluationMethodEnum,
    ComparisonTypeEnum
)
from src.database.models.efficiency import IndicatorWeightConfig
from src.services.efficiency_evaluator import EfficiencyEvaluator


class ComprehensiveEvaluationService:
    """
    综合评估服务类
    
    提供多场景综合评估的业务逻辑处理：
    - 创建和管理评估任务
    - 执行多种评估方法
    - 方案比较和排序分析
    - 敏感性和不确定性分析
    """
    
    def __init__(self) -> None:
        """初始化综合评估服务"""
        self.evaluation_methods = {
            EvaluationMethodEnum.WEIGHTED_AVERAGE: self._weighted_average_evaluation,
            EvaluationMethodEnum.FUZZY_EVALUATION: self._fuzzy_evaluation,
            EvaluationMethodEnum.AHP: self._ahp_evaluation,
            EvaluationMethodEnum.TOPSIS: self._topsis_evaluation,
            EvaluationMethodEnum.GREY_RELATIONAL: self._grey_relational_evaluation
        }
        self.efficiency_evaluator = EfficiencyEvaluator()
    
    async def create_evaluation_task(self, 
                                   session: AsyncSession,
                                   task_data: Dict[str, Any]) -> str:
        """
        创建综合评估任务
        
        Args:
            session: 数据库会话
            task_data: 任务数据
            
        Returns:
            str: 任务ID
        """
        # 验证输入数据
        self._validate_evaluation_data(task_data)
        
        # 创建评估任务
        evaluation_task = EvaluationTask(
            task_name=task_data["task_name"],
            task_description=task_data.get("task_description"),
            scheme_ids=task_data["scheme_ids"],
            scheme_names=task_data["scheme_names"],
            evaluation_scenarios=task_data["evaluation_scenarios"],
            evaluation_indicators=task_data["evaluation_indicators"],
            indicator_weights=task_data["indicator_weights"],
            weight_config_id=task_data.get("weight_config_id"),
            evaluation_method=EvaluationMethodEnum(task_data["evaluation_method"]),
            method_parameters=task_data.get("method_parameters", {}),
            sensitivity_analysis=task_data.get("sensitivity_analysis", False),
            uncertainty_analysis=task_data.get("uncertainty_analysis", False),
            comparison_type=ComparisonTypeEnum(task_data.get("comparison_type", "ranking")),
            created_by=task_data.get("created_by")
        )
        
        session.add(evaluation_task)
        await session.commit()
        await session.refresh(evaluation_task)
        
        return evaluation_task.id
    
    def _validate_evaluation_data(self, task_data: Dict[str, Any]) -> None:
        """
        验证评估任务数据
        
        Args:
            task_data: 任务数据
            
        Raises:
            ValueError: 数据验证失败
        """
        required_fields = ["task_name", "scheme_ids", "scheme_names", 
                          "evaluation_scenarios", "evaluation_indicators", 
                          "indicator_weights", "evaluation_method"]
        
        for field in required_fields:
            if field not in task_data:
                raise ValueError(f"缺少必需字段: {field}")
        
        # 验证方案数量匹配
        if len(task_data["scheme_ids"]) != len(task_data["scheme_names"]):
            raise ValueError("方案ID和名称数量不匹配")
        
        # 验证至少有两个方案
        if len(task_data["scheme_ids"]) < 2:
            raise ValueError("至少需要两个方案进行比较")
        
        # 验证评估方法
        if task_data["evaluation_method"] not in [e.value for e in EvaluationMethodEnum]:
            raise ValueError(f"不支持的评估方法: {task_data['evaluation_method']}")
    
    async def execute_evaluation(self, 
                               session: AsyncSession,
                               task_id: str) -> Dict[str, Any]:
        """
        执行综合评估
        
        Args:
            session: 数据库会话
            task_id: 任务ID
            
        Returns:
            Dict[str, Any]: 评估结果
        """
        # 获取任务信息
        task = await self._get_task_by_id(session, task_id)
        if not task:
            raise ValueError(f"任务不存在: {task_id}")
        
        if task.status != EvaluationStatusEnum.PENDING:
            raise ValueError(f"任务状态不允许执行: {task.status}")
        
        try:
            # 更新任务状态
            task.status = EvaluationStatusEnum.EVALUATING
            task.started_at = datetime.utcnow()
            await session.commit()
            
            # 收集方案数据
            schemes_data = await self._collect_schemes_data(session, task)
            
            # 执行评估
            evaluation_method = self.evaluation_methods[task.evaluation_method]
            evaluation_results = evaluation_method(schemes_data, task)
            
            # 执行敏感性分析
            if task.sensitivity_analysis:
                sensitivity_results = self._perform_sensitivity_analysis(schemes_data, task)
                evaluation_results["sensitivity_analysis"] = sensitivity_results
            
            # 执行不确定性分析
            if task.uncertainty_analysis:
                uncertainty_results = self._perform_uncertainty_analysis(schemes_data, task)
                evaluation_results["uncertainty_analysis"] = uncertainty_results
            
            # 生成改进建议
            improvement_suggestions = self._generate_improvement_suggestions(evaluation_results)
            
            # 保存结果
            await self._save_evaluation_results(session, task, evaluation_results)
            
            # 更新任务状态
            task.status = EvaluationStatusEnum.COMPLETED
            task.completed_at = datetime.utcnow()
            task.evaluation_duration = int((task.completed_at - task.started_at).total_seconds())
            task.scheme_scores = evaluation_results["scheme_scores"]
            task.scheme_ranking = evaluation_results["scheme_ranking"]
            task.detailed_scores = evaluation_results["detailed_scores"]
            task.comparison_analysis = evaluation_results.get("comparison_analysis", {})
            task.sensitivity_results = evaluation_results.get("sensitivity_analysis", {})
            task.improvement_suggestions = improvement_suggestions
            
            await session.commit()
            
            return {
                "task_id": task_id,
                "status": "completed",
                "scheme_ranking": evaluation_results["scheme_ranking"],
                "evaluation_summary": evaluation_results.get("summary", {}),
                "execution_time": task.evaluation_duration
            }
            
        except Exception as e:
            # 更新任务状态为失败
            task.status = EvaluationStatusEnum.FAILED
            task.error_message = str(e)
            task.completed_at = datetime.utcnow()
            await session.commit()
            
            raise e
    
    async def _get_task_by_id(self, session: AsyncSession, task_id: str) -> Optional[EvaluationTask]:
        """获取任务信息"""
        result = await session.execute(
            select(EvaluationTask).where(EvaluationTask.id == task_id)
        )
        return result.scalar_one_or_none()
    
    async def _collect_schemes_data(self, 
                                  session: AsyncSession,
                                  task: EvaluationTask) -> Dict[str, Any]:
        """
        收集方案数据
        
        Args:
            session: 数据库会话
            task: 评估任务
            
        Returns:
            Dict[str, Any]: 方案数据
        """
        schemes_data = {}
        
        for i, scheme_id in enumerate(task.scheme_ids):
            scheme_name = task.scheme_names[i]
            
            # 为每个场景收集方案的指标数据
            scheme_scenario_data = {}
            for scenario in task.evaluation_scenarios:
                scenario_data = await self._get_scheme_scenario_data(
                    session, scheme_id, scenario, task.evaluation_indicators
                )
                scheme_scenario_data[scenario["scenario_id"]] = scenario_data
            
            schemes_data[scheme_id] = {
                "scheme_name": scheme_name,
                "scenario_data": scheme_scenario_data
            }
        
        return schemes_data
    
    async def _get_scheme_scenario_data(self, 
                                      session: AsyncSession,
                                      scheme_id: str,
                                      scenario: Dict[str, Any],
                                      indicators: List[str]) -> Dict[str, float]:
        """获取方案在特定场景下的指标数据"""
        # 这里需要根据实际的数据存储结构来实现
        # 暂时返回模拟数据
        indicator_values = {}
        
        for indicator_id in indicators:
            # 模拟指标值，实际应该从数据库查询
            base_value = hash(f"{scheme_id}_{scenario['scenario_id']}_{indicator_id}") % 100
            indicator_values[indicator_id] = float(base_value)
        
        return indicator_values
    
    def _weighted_average_evaluation(self, 
                                   schemes_data: Dict[str, Any],
                                   task: EvaluationTask) -> Dict[str, Any]:
        """
        加权平均评估方法
        
        Args:
            schemes_data: 方案数据
            task: 评估任务
            
        Returns:
            Dict[str, Any]: 评估结果
        """
        scheme_scores = {}
        detailed_scores = {}
        
        for scheme_id, scheme_info in schemes_data.items():
            total_score = 0.0
            scenario_scores = {}
            
            # 计算每个场景的得分
            for scenario in task.evaluation_scenarios:
                scenario_id = scenario["scenario_id"]
                scenario_weight = scenario.get("weight", 1.0)
                scenario_data = scheme_info["scenario_data"][scenario_id]
                
                # 计算场景内的加权得分
                scenario_score = 0.0
                for indicator_id, indicator_value in scenario_data.items():
                    indicator_weight = task.indicator_weights.get(indicator_id, 1.0)
                    scenario_score += indicator_value * indicator_weight
                
                scenario_scores[scenario_id] = scenario_score
                total_score += scenario_score * scenario_weight
            
            scheme_scores[scheme_id] = total_score
            detailed_scores[scheme_id] = {
                "total_score": total_score,
                "scenario_scores": scenario_scores
            }
        
        # 排序方案
        scheme_ranking = self._rank_schemes(scheme_scores, schemes_data)
        
        return {
            "scheme_scores": scheme_scores,
            "detailed_scores": detailed_scores,
            "scheme_ranking": scheme_ranking,
            "method": "weighted_average"
        }
    
    def _fuzzy_evaluation(self, 
                         schemes_data: Dict[str, Any],
                         task: EvaluationTask) -> Dict[str, Any]:
        """模糊评价方法（简化实现）"""
        # 简化的模糊评价实现
        return self._weighted_average_evaluation(schemes_data, task)
    
    def _ahp_evaluation(self, 
                       schemes_data: Dict[str, Any],
                       task: EvaluationTask) -> Dict[str, Any]:
        """层次分析法（简化实现）"""
        # 简化的AHP实现
        return self._weighted_average_evaluation(schemes_data, task)
    
    def _topsis_evaluation(self, 
                          schemes_data: Dict[str, Any],
                          task: EvaluationTask) -> Dict[str, Any]:
        """TOPSIS方法（简化实现）"""
        # 简化的TOPSIS实现
        return self._weighted_average_evaluation(schemes_data, task)
    
    def _grey_relational_evaluation(self, 
                                   schemes_data: Dict[str, Any],
                                   task: EvaluationTask) -> Dict[str, Any]:
        """灰色关联分析（简化实现）"""
        # 简化的灰色关联分析实现
        return self._weighted_average_evaluation(schemes_data, task)
    
    def _rank_schemes(self, 
                     scheme_scores: Dict[str, float],
                     schemes_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        对方案进行排序
        
        Args:
            scheme_scores: 方案得分
            schemes_data: 方案数据
            
        Returns:
            List[Dict[str, Any]]: 排序结果
        """
        sorted_schemes = sorted(scheme_scores.items(), key=lambda x: x[1], reverse=True)
        
        ranking = []
        for rank, (scheme_id, score) in enumerate(sorted_schemes, 1):
            scheme_name = schemes_data[scheme_id]["scheme_name"]
            ranking.append({
                "rank": rank,
                "scheme_id": scheme_id,
                "scheme_name": scheme_name,
                "score": score,
                "normalized_score": score / max(scheme_scores.values()) if scheme_scores.values() else 0.0
            })
        
        return ranking
    
    def _perform_sensitivity_analysis(self, 
                                    schemes_data: Dict[str, Any],
                                    task: EvaluationTask) -> Dict[str, Any]:
        """执行敏感性分析"""
        # 简化的敏感性分析实现
        return {
            "weight_sensitivity": {},
            "parameter_sensitivity": {},
            "robustness_score": 0.8
        }
    
    def _perform_uncertainty_analysis(self, 
                                    schemes_data: Dict[str, Any],
                                    task: EvaluationTask) -> Dict[str, Any]:
        """执行不确定性分析"""
        # 简化的不确定性分析实现
        return {
            "uncertainty_bounds": {},
            "confidence_intervals": {},
            "risk_assessment": {}
        }
    
    def _generate_improvement_suggestions(self, 
                                        evaluation_results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """生成改进建议"""
        suggestions = []
        
        # 基于评估结果生成改进建议
        ranking = evaluation_results.get("scheme_ranking", [])
        if ranking:
            worst_scheme = ranking[-1]
            suggestions.append({
                "type": "performance_improvement",
                "target_scheme": worst_scheme["scheme_id"],
                "suggestion": f"方案{worst_scheme['scheme_name']}得分较低，建议重点优化",
                "priority": "high"
            })
        
        return suggestions
    
    async def _save_evaluation_results(self, 
                                     session: AsyncSession,
                                     task: EvaluationTask,
                                     evaluation_results: Dict[str, Any]) -> None:
        """保存评估结果"""
        scheme_ranking = evaluation_results.get("scheme_ranking", [])
        
        for rank_info in scheme_ranking:
            result = EvaluationResult(
                task_id=task.id,
                scheme_id=rank_info["scheme_id"],
                scheme_name=rank_info["scheme_name"],
                total_score=rank_info["score"],
                normalized_score=rank_info["normalized_score"],
                ranking_position=rank_info["rank"],
                category_scores={},
                indicator_scores={},
                indicator_contributions={}
            )
            
            session.add(result)
        
        await session.commit()
