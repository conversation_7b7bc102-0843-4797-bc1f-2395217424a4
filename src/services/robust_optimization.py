"""
鲁棒优化服务

遵循base-rules.md规范：
- 函数复杂度控制，每个函数圈复杂度不超过10
- 函数参数不超过5个
- 使用描述性的函数命名
- 模块职责单一性：只包含鲁棒优化相关的业务逻辑
"""

from typing import Optional, List, Dict, Any
from datetime import datetime
import numpy as np
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_

from src.database.models.optimization import (
    OptimizationTask,
    OptimizationResult,
    OptimizationTaskTypeEnum,
    OptimizationStatusEnum,
    AlgorithmTypeEnum
)
from src.algorithms.robust.worst_case import WorstCaseOptimization
from src.algorithms.robust.stochastic_programming import StochasticProgramming
from src.algorithms.base import AlgorithmConfig


class RobustOptimizationService:
    """
    鲁棒优化服务类
    
    提供鲁棒优化的业务逻辑处理：
    - 创建和管理鲁棒优化任务
    - 执行不确定性建模和分析
    - 鲁棒解评估和敏感性分析
    - 风险度量和决策支持
    """
    
    def __init__(self) -> None:
        """初始化鲁棒优化服务"""
        self.algorithm_registry = {
            "worst_case": WorstCaseOptimization,
            "stochastic_programming": StochasticProgramming
        }
    
    async def create_robust_optimization_task(self, 
                                            session: AsyncSession,
                                            task_data: Dict[str, Any]) -> str:
        """
        创建鲁棒优化任务
        
        Args:
            session: 数据库会话
            task_data: 任务数据
            
        Returns:
            str: 任务ID
        """
        # 验证输入数据
        self._validate_robust_optimization_data(task_data)
        
        # 创建优化任务
        optimization_task = OptimizationTask(
            task_name=task_data["task_name"],
            task_description=task_data.get("task_description"),
            task_type=OptimizationTaskTypeEnum.ROBUST_OPTIMIZATION,
            algorithm_type=AlgorithmTypeEnum.GENETIC_ALGORITHM,  # 默认算法
            algorithm_config=task_data.get("algorithm_config", {}),
            problem_definition=task_data["problem_definition"],
            created_by=task_data.get("created_by"),
            scenario_id=task_data.get("scenario_id")
        )
        
        session.add(optimization_task)
        await session.commit()
        await session.refresh(optimization_task)
        
        return optimization_task.id
    
    def _validate_robust_optimization_data(self, task_data: Dict[str, Any]) -> None:
        """
        验证鲁棒优化任务数据
        
        Args:
            task_data: 任务数据
            
        Raises:
            ValueError: 数据验证失败
        """
        required_fields = ["task_name", "problem_definition"]
        
        for field in required_fields:
            if field not in task_data:
                raise ValueError(f"缺少必需字段: {field}")
        
        # 验证问题定义
        problem_def = task_data["problem_definition"]
        if not isinstance(problem_def, dict):
            raise ValueError("问题定义必须是字典格式")
        
        # 验证不确定性定义
        if "uncertainty_set" not in problem_def and "random_parameters_distribution" not in problem_def:
            raise ValueError("必须定义不确定性集合或随机参数分布")
    
    async def execute_robust_optimization(self, 
                                        session: AsyncSession,
                                        task_id: str,
                                        optimization_method: str = "worst_case") -> Dict[str, Any]:
        """
        执行鲁棒优化
        
        Args:
            session: 数据库会话
            task_id: 任务ID
            optimization_method: 优化方法
            
        Returns:
            Dict[str, Any]: 优化结果
        """
        # 获取任务信息
        task = await self._get_task_by_id(session, task_id)
        if not task:
            raise ValueError(f"任务不存在: {task_id}")
        
        if task.status != OptimizationStatusEnum.PENDING:
            raise ValueError(f"任务状态不允许执行: {task.status}")
        
        try:
            # 更新任务状态
            task.status = OptimizationStatusEnum.RUNNING
            task.started_at = datetime.utcnow()
            await session.commit()
            
            # 选择算法
            if optimization_method not in self.algorithm_registry:
                raise ValueError(f"不支持的优化方法: {optimization_method}")
            
            algorithm_class = self.algorithm_registry[optimization_method]
            
            # 准备算法配置
            config = self._create_robust_algorithm_config(task.algorithm_config, optimization_method)
            algorithm = algorithm_class(config)
            
            # 准备问题定义
            problem_definition = self._prepare_robust_problem_definition(
                task.problem_definition, optimization_method
            )
            
            # 定义目标函数
            objective_function = self._create_robust_objective_function(
                task.problem_definition, optimization_method
            )
            
            # 执行优化
            result = algorithm.optimize(
                problem_definition=problem_definition,
                objective_function=objective_function,
                constraints=[]
            )
            
            # 分析鲁棒性
            robustness_analysis = self._analyze_solution_robustness(
                algorithm, result.best_solution, task.problem_definition
            )
            
            # 保存结果
            await self._save_robust_optimization_result(
                session, task, result, robustness_analysis, optimization_method
            )
            
            # 更新任务状态
            task.status = OptimizationStatusEnum.COMPLETED
            task.completed_at = datetime.utcnow()
            task.execution_duration = int((task.completed_at - task.started_at).total_seconds())
            task.result_data = {
                "robust_solution": result.best_solution,
                "robustness_analysis": robustness_analysis,
                "optimization_method": optimization_method
            }
            task.performance_metrics = {
                "execution_time": result.execution_time_seconds,
                "iterations": result.iterations_completed,
                "converged": result.converged
            }
            
            await session.commit()
            
            return {
                "task_id": task_id,
                "status": "completed",
                "robust_solution": result.best_solution,
                "robustness_analysis": robustness_analysis,
                "optimization_method": optimization_method,
                "execution_time": task.execution_duration
            }
            
        except Exception as e:
            # 更新任务状态为失败
            task.status = OptimizationStatusEnum.FAILED
            task.error_message = str(e)
            task.completed_at = datetime.utcnow()
            await session.commit()
            
            raise e
    
    async def _get_task_by_id(self, session: AsyncSession, task_id: str) -> Optional[OptimizationTask]:
        """获取任务信息"""
        result = await session.execute(
            select(OptimizationTask).where(OptimizationTask.id == task_id)
        )
        return result.scalar_one_or_none()
    
    def _create_robust_algorithm_config(self, 
                                      algorithm_config: Dict[str, Any],
                                      optimization_method: str) -> AlgorithmConfig:
        """创建鲁棒算法配置"""
        # 根据优化方法设置默认参数
        if optimization_method == "worst_case":
            default_params = {
                "robustness_level": 0.1,
                "max_inner_iterations": 100,
                "uncertainty_budget": 1.0
            }
        elif optimization_method == "stochastic_programming":
            default_params = {
                "num_scenarios": 100,
                "confidence_level": 0.95,
                "risk_measure": "expected_value"
            }
        else:
            default_params = {}
        
        # 合并用户配置
        merged_params = {**default_params, **algorithm_config}
        
        return AlgorithmConfig(
            max_iterations=merged_params.get("max_iterations", 500),
            population_size=merged_params.get("population_size", 1),  # 鲁棒优化通常单解
            convergence_tolerance=merged_params.get("convergence_tolerance", 1e-6),
            max_time_seconds=merged_params.get("max_time_seconds", 600),
            algorithm_params=merged_params
        )
    
    def _prepare_robust_problem_definition(self, 
                                         problem_definition: Dict[str, Any],
                                         optimization_method: str) -> Dict[str, Any]:
        """准备鲁棒问题定义"""
        prepared_definition = problem_definition.copy()
        
        # 确保有决策变量定义
        if "decision_dimension" not in prepared_definition:
            prepared_definition["decision_dimension"] = 5
        
        if "decision_bounds" not in prepared_definition:
            dim = prepared_definition["decision_dimension"]
            prepared_definition["decision_bounds"] = [(0, 1)] * dim
        
        # 根据优化方法调整问题定义
        if optimization_method == "worst_case":
            self._prepare_worst_case_definition(prepared_definition)
        elif optimization_method == "stochastic_programming":
            self._prepare_stochastic_definition(prepared_definition)
        
        return prepared_definition
    
    def _prepare_worst_case_definition(self, problem_definition: Dict[str, Any]) -> None:
        """准备最坏情况优化问题定义"""
        if "uncertainty_set" not in problem_definition:
            # 创建默认不确定性集合
            problem_definition["uncertainty_set"] = {
                "parameter_names": ["demand", "cost", "capacity"],
                "nominal_values": [100.0, 1.0, 50.0],
                "uncertainty_bounds": [(-10.0, 10.0), (-0.2, 0.2), (-5.0, 5.0)],
                "uncertainty_type": "box"
            }
    
    def _prepare_stochastic_definition(self, problem_definition: Dict[str, Any]) -> None:
        """准备随机规划问题定义"""
        if "random_parameters_distribution" not in problem_definition:
            # 创建默认随机参数分布
            problem_definition["random_parameters_distribution"] = {
                "demand": {"type": "normal", "params": {"mean": 100, "std": 20}},
                "cost": {"type": "uniform", "params": {"low": 0.8, "high": 1.2}}
            }
        
        if "first_stage_dimension" not in problem_definition:
            problem_definition["first_stage_dimension"] = problem_definition.get("decision_dimension", 3)
        
        if "second_stage_dimension" not in problem_definition:
            problem_definition["second_stage_dimension"] = 2
    
    def _create_robust_objective_function(self, 
                                        problem_definition: Dict[str, Any],
                                        optimization_method: str):
        """创建鲁棒目标函数"""
        def robust_objective_function(solution: Dict[str, Any]) -> float:
            """鲁棒目标函数"""
            
            if optimization_method == "worst_case":
                return self._worst_case_objective(solution, problem_definition)
            elif optimization_method == "stochastic_programming":
                return self._stochastic_objective(solution, problem_definition)
            else:
                return self._default_objective(solution, problem_definition)
        
        return robust_objective_function
    
    def _worst_case_objective(self, solution: Dict[str, Any], problem_definition: Dict[str, Any]) -> float:
        """最坏情况目标函数"""
        if "decision_variables" not in solution:
            return float('inf')
        
        decision_vars = np.array(solution["decision_variables"])
        
        # 简化的最坏情况目标：考虑决策变量的平方和
        base_cost = np.sum(decision_vars ** 2)
        
        # 添加不确定性惩罚
        uncertainty_penalty = 0.0
        if "uncertain_parameters" in solution:
            uncertain_params = np.array(solution["uncertain_parameters"])
            uncertainty_penalty = np.sum(np.abs(uncertain_params)) * 0.1
        
        return base_cost + uncertainty_penalty
    
    def _stochastic_objective(self, solution: Dict[str, Any], problem_definition: Dict[str, Any]) -> float:
        """随机规划目标函数"""
        if "first_stage_variables" not in solution:
            return float('inf')
        
        first_stage = np.array(solution["first_stage_variables"])
        
        # 第一阶段成本
        first_stage_cost = np.sum(first_stage ** 2)
        
        # 第二阶段期望成本（简化）
        second_stage_cost = 0.0
        if "second_stage_variables" in solution:
            second_stage = np.array(solution["second_stage_variables"])
            second_stage_cost = np.sum(second_stage) * 0.5
        
        # 场景相关成本
        scenario_cost = 0.0
        if "scenario_parameters" in solution:
            scenario_params = np.array(solution["scenario_parameters"])
            scenario_cost = np.sum(scenario_params * first_stage) * 0.1
        
        return first_stage_cost + second_stage_cost + scenario_cost
    
    def _default_objective(self, solution: Dict[str, Any], problem_definition: Dict[str, Any]) -> float:
        """默认目标函数"""
        # 提取决策变量
        if "decision_variables" in solution:
            variables = np.array(solution["decision_variables"])
        elif "first_stage_variables" in solution:
            variables = np.array(solution["first_stage_variables"])
        else:
            return float('inf')
        
        # 简单的二次目标函数
        return np.sum(variables ** 2)
    
    def _analyze_solution_robustness(self, 
                                   algorithm,
                                   best_solution: Dict[str, Any],
                                   problem_definition: Dict[str, Any]) -> Dict[str, Any]:
        """分析解的鲁棒性"""
        robustness_analysis = {
            "robustness_measure": 0.0,
            "sensitivity_analysis": {},
            "uncertainty_impact": {},
            "confidence_intervals": {}
        }
        
        # 如果算法支持鲁棒性分析，调用相应方法
        if hasattr(algorithm, 'get_robustness_analysis'):
            algorithm_analysis = algorithm.get_robustness_analysis()
            robustness_analysis.update(algorithm_analysis)
        
        # 如果算法支持场景分析，调用相应方法
        if hasattr(algorithm, 'get_scenario_analysis'):
            scenario_analysis = algorithm.get_scenario_analysis()
            robustness_analysis["scenario_analysis"] = scenario_analysis
        
        return robustness_analysis
    
    async def _save_robust_optimization_result(self, 
                                             session: AsyncSession,
                                             task: OptimizationTask,
                                             result,
                                             robustness_analysis: Dict[str, Any],
                                             optimization_method: str) -> None:
        """保存鲁棒优化结果"""
        optimization_result = OptimizationResult(
            task_id=task.id,
            solution_data={
                "robust_solution": result.best_solution,
                "optimization_method": optimization_method,
                "robustness_analysis": robustness_analysis
            },
            objective_values={"robust_objective": result.best_fitness},
            constraint_violations=result.constraint_violations or {},
            convergence_data={"history": result.convergence_history},
            iteration_count=result.iterations_completed,
            solution_quality={
                "fitness": result.best_fitness,
                "feasible": result.feasible,
                "robustness_measure": robustness_analysis.get("robustness_measure", 0.0)
            },
            computation_time=result.execution_time_seconds,
            memory_usage=0.0,
            cpu_usage=0.0
        )
        
        session.add(optimization_result)
        await session.commit()
    
    async def get_uncertainty_analysis(self, 
                                     session: AsyncSession,
                                     task_id: str) -> Dict[str, Any]:
        """
        获取不确定性分析结果
        
        Args:
            session: 数据库会话
            task_id: 任务ID
            
        Returns:
            Dict[str, Any]: 不确定性分析结果
        """
        task = await self._get_task_by_id(session, task_id)
        if not task or not task.result_data:
            return {}
        
        result_data = task.result_data
        robustness_analysis = result_data.get("robustness_analysis", {})
        
        return {
            "task_id": task_id,
            "optimization_method": result_data.get("optimization_method", "unknown"),
            "robustness_measure": robustness_analysis.get("robustness_measure", 0.0),
            "sensitivity_analysis": robustness_analysis.get("sensitivity_analysis", {}),
            "uncertainty_impact": robustness_analysis.get("uncertainty_impact", {}),
            "scenario_analysis": robustness_analysis.get("scenario_analysis", {}),
            "confidence_intervals": robustness_analysis.get("confidence_intervals", {})
        }
