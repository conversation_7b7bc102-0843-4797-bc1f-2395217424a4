"""
路径规划服务

遵循base-rules.md规范：
- 函数复杂度控制，每个函数圈复杂度不超过10
- 函数参数不超过5个
- 使用描述性的函数命名
- 模块职责单一性：只包含路径规划相关的业务逻辑
"""

from typing import Optional, List, Dict, Any
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_

from src.database.models.path_planning import (
    PathPlanningTask,
    PathPlanningResult,
    WorkAreaTopology,
    PathPlanningStatusEnum,
    PathTypeEnum,
    OptimizationObjectiveEnum
)
from src.database.models.optimization import OptimizationTask, OptimizationTaskTypeEnum
from src.algorithms.path_planning.dijkstra import DijkstraAlgorithm
from src.algorithms.path_planning.a_star import AStarAlgorithm
from src.algorithms.path_planning.dynamic_programming import DynamicProgrammingAlgorithm
from src.algorithms.base import AlgorithmConfig


class PathPlanningService:
    """
    路径规划服务类
    
    提供路径规划的业务逻辑处理：
    - 创建和管理路径规划任务
    - 执行不同类型的路径规划算法
    - 分析和优化路径结果
    - 管理作业区域拓扑数据
    """
    
    def __init__(self) -> None:
        """初始化路径规划服务"""
        self.algorithm_registry = {
            "dijkstra": DijkstraAlgorithm,
            "a_star": AStarAlgorithm,
            "dynamic_programming": DynamicProgrammingAlgorithm
        }
    
    async def create_planning_task(self, 
                                 session: AsyncSession,
                                 task_data: Dict[str, Any]) -> str:
        """
        创建路径规划任务
        
        Args:
            session: 数据库会话
            task_data: 任务数据
            
        Returns:
            str: 任务ID
        """
        # 验证输入数据
        self._validate_task_data(task_data)
        
        # 创建路径规划任务
        planning_task = PathPlanningTask(
            task_name=task_data["task_name"],
            task_description=task_data.get("task_description"),
            path_type=PathTypeEnum(task_data["path_type"]),
            start_point=task_data["start_point"],
            end_point=task_data["end_point"],
            waypoints=task_data.get("waypoints"),
            constraints=task_data.get("constraints", {}),
            optimization_objectives=task_data["optimization_objectives"],
            objective_weights=task_data.get("objective_weights"),
            algorithm_type=task_data["algorithm_type"],
            algorithm_parameters=task_data.get("algorithm_parameters", {}),
            created_by=task_data.get("created_by")
        )
        
        session.add(planning_task)
        await session.commit()
        await session.refresh(planning_task)
        
        return planning_task.id
    
    def _validate_task_data(self, task_data: Dict[str, Any]) -> None:
        """
        验证任务数据
        
        Args:
            task_data: 任务数据
            
        Raises:
            ValueError: 数据验证失败
        """
        required_fields = ["task_name", "path_type", "start_point", "end_point", 
                          "optimization_objectives", "algorithm_type"]
        
        for field in required_fields:
            if field not in task_data:
                raise ValueError(f"缺少必需字段: {field}")
        
        # 验证路径类型
        if task_data["path_type"] not in [e.value for e in PathTypeEnum]:
            raise ValueError(f"无效的路径类型: {task_data['path_type']}")
        
        # 验证算法类型
        if task_data["algorithm_type"] not in self.algorithm_registry:
            raise ValueError(f"不支持的算法类型: {task_data['algorithm_type']}")
        
        # 验证起终点格式
        for point_name in ["start_point", "end_point"]:
            point = task_data[point_name]
            if not isinstance(point, dict) or "node_id" not in point:
                raise ValueError(f"{point_name}必须包含node_id字段")
    
    async def execute_planning(self, 
                             session: AsyncSession,
                             task_id: str) -> Dict[str, Any]:
        """
        执行路径规划
        
        Args:
            session: 数据库会话
            task_id: 任务ID
            
        Returns:
            Dict[str, Any]: 规划结果
        """
        # 获取任务信息
        task = await self._get_task_by_id(session, task_id)
        if not task:
            raise ValueError(f"任务不存在: {task_id}")
        
        if task.status != PathPlanningStatusEnum.PENDING:
            raise ValueError(f"任务状态不允许执行: {task.status}")
        
        try:
            # 更新任务状态
            task.status = PathPlanningStatusEnum.PLANNING
            task.started_at = datetime.utcnow()
            await session.commit()
            
            # 准备算法输入
            problem_definition = await self._prepare_problem_definition(session, task)
            
            # 创建并配置算法
            algorithm_class = self.algorithm_registry[task.algorithm_type]
            config = self._create_algorithm_config(task.algorithm_parameters)
            algorithm = algorithm_class(config)
            
            # 执行算法
            result = algorithm.optimize(
                problem_definition=problem_definition,
                objective_function=self._create_objective_function(task),
                constraints=self._create_constraints(task)
            )
            
            # 保存结果
            await self._save_planning_result(session, task, result, algorithm)
            
            # 更新任务状态
            task.status = PathPlanningStatusEnum.COMPLETED
            task.completed_at = datetime.utcnow()
            task.planning_duration = int((task.completed_at - task.started_at).total_seconds())
            task.optimal_path = result.best_solution
            task.performance_metrics = {
                "execution_time": result.execution_time_seconds,
                "iterations": result.iterations_completed,
                "converged": result.converged
            }
            
            await session.commit()
            
            return {
                "task_id": task_id,
                "status": "completed",
                "optimal_path": result.best_solution,
                "total_distance": result.best_fitness,
                "execution_time": result.execution_time_seconds
            }
            
        except Exception as e:
            # 更新任务状态为失败
            task.status = PathPlanningStatusEnum.FAILED
            task.error_message = str(e)
            task.completed_at = datetime.utcnow()
            await session.commit()
            
            raise e
    
    async def _get_task_by_id(self, session: AsyncSession, task_id: str) -> Optional[PathPlanningTask]:
        """获取任务信息"""
        result = await session.execute(
            select(PathPlanningTask).where(PathPlanningTask.id == task_id)
        )
        return result.scalar_one_or_none()
    
    async def _prepare_problem_definition(self, 
                                        session: AsyncSession,
                                        task: PathPlanningTask) -> Dict[str, Any]:
        """
        准备算法问题定义
        
        Args:
            session: 数据库会话
            task: 路径规划任务
            
        Returns:
            Dict[str, Any]: 问题定义
        """
        # 获取相关的拓扑数据
        topology = await self._get_relevant_topology(session, task.path_type)
        
        if not topology:
            raise ValueError(f"未找到{task.path_type}类型的拓扑数据")
        
        return {
            "nodes": topology.nodes,
            "edges": topology.edges,
            "start_node": task.start_point["node_id"],
            "end_node": task.end_point["node_id"],
            "waypoints": [wp["node_id"] for wp in task.waypoints or []],
            "constraints": task.constraints,
            "heuristic": task.algorithm_parameters.get("heuristic", "euclidean")
        }
    
    async def _get_relevant_topology(self, 
                                   session: AsyncSession,
                                   path_type: PathTypeEnum) -> Optional[WorkAreaTopology]:
        """获取相关的拓扑数据"""
        # 根据路径类型映射到区域类型
        area_type_mapping = {
            PathTypeEnum.WAREHOUSE_INTERNAL: "warehouse",
            PathTypeEnum.GROUND_TRANSPORT: "transport",
            PathTypeEnum.APRON_OPERATION: "apron",
            PathTypeEnum.INTEGRATED_PATH: "integrated"
        }
        
        area_type = area_type_mapping.get(path_type, "integrated")
        
        result = await session.execute(
            select(WorkAreaTopology).where(
                and_(
                    WorkAreaTopology.area_type == area_type,
                    WorkAreaTopology.is_active == True
                )
            ).order_by(WorkAreaTopology.created_at.desc())
        )
        
        return result.first()
    
    def _create_algorithm_config(self, parameters: Dict[str, Any]) -> AlgorithmConfig:
        """创建算法配置"""
        return AlgorithmConfig(
            max_iterations=parameters.get("max_iterations", 1000),
            convergence_tolerance=parameters.get("convergence_tolerance", 1e-6),
            max_time_seconds=parameters.get("max_time_seconds", 300),
            parallel_execution=parameters.get("parallel_execution", False),
            algorithm_params=parameters
        )
    
    def _create_objective_function(self, task: PathPlanningTask):
        """创建目标函数"""
        objectives = task.optimization_objectives
        weights = task.objective_weights or {}
        
        def objective_function(solution: Dict[str, Any]) -> float:
            """多目标加权目标函数"""
            total_score = 0.0
            
            if OptimizationObjectiveEnum.SHORTEST_DISTANCE in objectives:
                distance_score = solution.get("total_distance", 0.0)
                weight = weights.get("distance", 1.0)
                total_score += weight * distance_score
            
            if OptimizationObjectiveEnum.MINIMUM_TIME in objectives:
                time_score = solution.get("estimated_time", 0.0)
                weight = weights.get("time", 1.0)
                total_score += weight * time_score
            
            if OptimizationObjectiveEnum.LOWEST_COST in objectives:
                cost_score = solution.get("estimated_cost", 0.0)
                weight = weights.get("cost", 1.0)
                total_score += weight * cost_score
            
            return total_score
        
        return objective_function
    
    def _create_constraints(self, task: PathPlanningTask) -> List:
        """创建约束条件"""
        constraints = []
        task_constraints = task.constraints or {}
        
        # 安全距离约束
        if "safety_distance" in task_constraints:
            min_distance = task_constraints["safety_distance"]
            
            def safety_constraint(edge) -> bool:
                return edge.weight >= min_distance
            
            constraints.append(safety_constraint)
        
        # 时间窗口约束
        if "time_windows" in task_constraints:
            time_windows = task_constraints["time_windows"]
            
            def time_window_constraint(edge) -> bool:
                # 简化实现：检查边的时间属性
                edge_time = edge.properties.get("time_cost", 0)
                return any(tw["start"] <= edge_time <= tw["end"] for tw in time_windows)
            
            constraints.append(time_window_constraint)
        
        return constraints
    
    async def _save_planning_result(self, 
                                  session: AsyncSession,
                                  task: PathPlanningTask,
                                  result,
                                  algorithm) -> None:
        """保存规划结果"""
        if not result.best_solution or not result.best_solution.get("path"):
            return
        
        # 创建路径规划结果记录
        planning_result = PathPlanningResult(
            task_id=task.id,
            path_sequence=result.best_solution.get("path", []),
            total_distance=result.best_fitness,
            estimated_time=self._estimate_travel_time(result.best_solution),
            estimated_cost=self._estimate_travel_cost(result.best_solution),
            constraint_satisfaction=self._analyze_constraint_satisfaction(task, result),
            resource_requirements=self._analyze_resource_requirements(result.best_solution)
        )
        
        session.add(planning_result)
        await session.commit()
    
    def _estimate_travel_time(self, solution: Dict[str, Any]) -> float:
        """估算行程时间"""
        # 简化实现：假设平均速度
        distance = solution.get("total_distance", 0.0)
        average_speed = 30.0  # km/h
        return distance / average_speed * 60  # 转换为分钟
    
    def _estimate_travel_cost(self, solution: Dict[str, Any]) -> float:
        """估算行程成本"""
        # 简化实现：基于距离的成本模型
        distance = solution.get("total_distance", 0.0)
        cost_per_km = 5.0  # 每公里成本
        return distance * cost_per_km
    
    def _analyze_constraint_satisfaction(self, 
                                       task: PathPlanningTask,
                                       result) -> Dict[str, Any]:
        """分析约束满足情况"""
        return {
            "all_constraints_satisfied": result.feasible,
            "constraint_violations": result.constraint_violations or {},
            "satisfaction_score": 1.0 if result.feasible else 0.8
        }
    
    def _analyze_resource_requirements(self, solution: Dict[str, Any]) -> Dict[str, Any]:
        """分析资源需求"""
        path_length = len(solution.get("path", []))
        
        return {
            "estimated_fuel_consumption": solution.get("total_distance", 0.0) * 0.1,
            "estimated_personnel_hours": path_length * 0.5,
            "equipment_utilization": min(path_length / 10.0, 1.0)
        }
