-- 第3阶段数据库迁移脚本 - 高级算法和优化功能
-- 创建时间：2025年8月4日
-- 遵循base-rules.md规范，不使用硬编码数据

-- 创建优化任务类型枚举
CREATE TYPE optimization_task_type_enum AS ENUM (
    'path_planning',
    'resource_scheduling', 
    'multi_objective',
    'robust_optimization',
    'comprehensive_evaluation'
);

-- 创建优化状态枚举
CREATE TYPE optimization_status_enum AS ENUM (
    'pending',
    'running',
    'completed',
    'failed',
    'cancelled'
);

-- 创建算法类型枚举
CREATE TYPE algorithm_type_enum AS ENUM (
    'genetic_algorithm',
    'simulated_annealing',
    'tabu_search',
    'particle_swarm',
    'nsga2',
    'nsga3',
    'spea2',
    'moead',
    'dijkstra',
    'a_star'
);

-- 创建路径规划状态枚举
CREATE TYPE path_planning_status_enum AS ENUM (
    'pending',
    'planning',
    'completed',
    'failed',
    'cancelled'
);

-- 创建路径类型枚举
CREATE TYPE path_type_enum AS ENUM (
    'warehouse_internal',
    'ground_transport',
    'apron_operation',
    'integrated_path'
);

-- 创建优化目标枚举
CREATE TYPE optimization_objective_enum AS ENUM (
    'shortest_distance',
    'minimum_time',
    'lowest_cost',
    'highest_safety',
    'balanced_multi'
);

-- 创建多目标优化状态枚举
CREATE TYPE multi_objective_status_enum AS ENUM (
    'pending',
    'optimizing',
    'completed',
    'failed',
    'cancelled'
);

-- 创建目标函数类型枚举
CREATE TYPE objective_function_type_enum AS ENUM (
    'minimize',
    'maximize'
);

-- 创建多目标算法枚举
CREATE TYPE multi_objective_algorithm_enum AS ENUM (
    'nsga2',
    'nsga3',
    'spea2',
    'moead',
    'paes'
);

-- 创建评估状态枚举
CREATE TYPE evaluation_status_enum AS ENUM (
    'pending',
    'evaluating',
    'completed',
    'failed',
    'cancelled'
);

-- 创建评估方法枚举
CREATE TYPE evaluation_method_enum AS ENUM (
    'weighted_average',
    'fuzzy_evaluation',
    'ahp',
    'topsis',
    'grey_relational',
    'electre'
);

-- 创建比较类型枚举
CREATE TYPE comparison_type_enum AS ENUM (
    'pairwise',
    'ranking',
    'clustering',
    'pareto_analysis'
);

-- 创建优化任务表
CREATE TABLE optimization_tasks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    task_name VARCHAR(255) NOT NULL,
    task_description TEXT,
    task_type optimization_task_type_enum NOT NULL,
    algorithm_type algorithm_type_enum NOT NULL,
    algorithm_config JSONB NOT NULL DEFAULT '{}',
    problem_definition JSONB NOT NULL DEFAULT '{}',
    status optimization_status_enum DEFAULT 'pending',
    progress_percentage INTEGER DEFAULT 0,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    execution_duration INTEGER,
    result_data JSONB,
    performance_metrics JSONB,
    error_message TEXT,
    error_details JSONB,
    created_by VARCHAR(100),
    scenario_id UUID REFERENCES scenarios(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建算法配置表
CREATE TABLE algorithm_configurations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    algorithm_name VARCHAR(255) NOT NULL,
    algorithm_type algorithm_type_enum NOT NULL,
    algorithm_description TEXT,
    default_parameters JSONB NOT NULL DEFAULT '{}',
    parameter_ranges JSONB,
    applicable_problem_types JSONB NOT NULL DEFAULT '[]',
    problem_size_limits JSONB,
    performance_benchmarks JSONB,
    is_active BOOLEAN DEFAULT TRUE,
    version VARCHAR(50) DEFAULT '1.0.0',
    created_by VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建优化结果表
CREATE TABLE optimization_results (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    task_id UUID NOT NULL REFERENCES optimization_tasks(id) ON DELETE CASCADE,
    solution_data JSONB NOT NULL,
    objective_values JSONB NOT NULL,
    constraint_violations JSONB,
    convergence_data JSONB,
    iteration_count INTEGER,
    solution_quality JSONB,
    computation_time FLOAT,
    memory_usage FLOAT,
    cpu_usage FLOAT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建路径规划任务表
CREATE TABLE path_planning_tasks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    task_name VARCHAR(255) NOT NULL,
    task_description TEXT,
    path_type path_type_enum NOT NULL,
    start_point JSONB NOT NULL,
    end_point JSONB NOT NULL,
    waypoints JSONB,
    constraints JSONB NOT NULL DEFAULT '{}',
    optimization_objectives JSONB NOT NULL,
    objective_weights JSONB,
    algorithm_type VARCHAR(50) NOT NULL,
    algorithm_parameters JSONB,
    status path_planning_status_enum DEFAULT 'pending',
    progress_percentage INTEGER DEFAULT 0,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    planning_duration INTEGER,
    optimal_path JSONB,
    alternative_paths JSONB,
    path_analysis JSONB,
    performance_metrics JSONB,
    error_message TEXT,
    error_details JSONB,
    created_by VARCHAR(100),
    optimization_task_id UUID REFERENCES optimization_tasks(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建作业区域拓扑表
CREATE TABLE work_area_topology (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    area_name VARCHAR(255) NOT NULL,
    area_type VARCHAR(50) NOT NULL,
    area_description TEXT,
    nodes JSONB NOT NULL DEFAULT '[]',
    edges JSONB NOT NULL DEFAULT '[]',
    area_properties JSONB NOT NULL DEFAULT '{}',
    current_status JSONB NOT NULL DEFAULT '{}',
    boundary_coordinates JSONB,
    center_coordinates JSONB,
    version VARCHAR(50) DEFAULT '1.0.0',
    is_active BOOLEAN DEFAULT TRUE,
    created_by VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建路径规划结果表
CREATE TABLE path_planning_results (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    task_id UUID NOT NULL REFERENCES path_planning_tasks(id) ON DELETE CASCADE,
    path_sequence JSONB NOT NULL,
    total_distance FLOAT NOT NULL,
    estimated_time FLOAT NOT NULL,
    estimated_cost FLOAT,
    critical_points JSONB,
    bottleneck_analysis JSONB,
    constraint_satisfaction JSONB NOT NULL DEFAULT '{}',
    risk_assessment JSONB,
    safety_score FLOAT,
    resource_requirements JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建多目标优化任务表
CREATE TABLE multi_objective_tasks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    task_name VARCHAR(255) NOT NULL,
    task_description TEXT,
    objective_functions JSONB NOT NULL,
    objective_weights JSONB,
    decision_variables JSONB NOT NULL,
    variable_bounds JSONB NOT NULL,
    constraints JSONB NOT NULL DEFAULT '[]',
    algorithm_type multi_objective_algorithm_enum NOT NULL,
    algorithm_parameters JSONB NOT NULL DEFAULT '{}',
    population_size INTEGER DEFAULT 100,
    max_generations INTEGER DEFAULT 1000,
    termination_criteria JSONB,
    status multi_objective_status_enum DEFAULT 'pending',
    current_generation INTEGER DEFAULT 0,
    progress_percentage INTEGER DEFAULT 0,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    optimization_duration INTEGER,
    pareto_solutions JSONB,
    convergence_data JSONB,
    quality_indicators JSONB,
    hypervolume FLOAT,
    spacing FLOAT,
    spread FLOAT,
    error_message TEXT,
    error_details JSONB,
    created_by VARCHAR(100),
    optimization_task_id UUID REFERENCES optimization_tasks(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建帕累托解表
CREATE TABLE pareto_solutions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    task_id UUID NOT NULL REFERENCES multi_objective_tasks(id) ON DELETE CASCADE,
    solution_id VARCHAR(100) NOT NULL,
    decision_variables JSONB NOT NULL,
    objective_values JSONB NOT NULL,
    constraint_violations JSONB,
    dominance_rank INTEGER,
    crowding_distance FLOAT,
    feasibility BOOLEAN DEFAULT TRUE,
    quality_score FLOAT,
    generation INTEGER NOT NULL,
    parent_solutions JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建目标函数表
CREATE TABLE objective_functions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    function_name VARCHAR(255) NOT NULL,
    function_description TEXT,
    function_type objective_function_type_enum NOT NULL,
    function_expression TEXT NOT NULL,
    function_parameters JSONB,
    is_linear BOOLEAN DEFAULT FALSE,
    is_convex BOOLEAN DEFAULT FALSE,
    is_differentiable BOOLEAN DEFAULT TRUE,
    value_range JSONB,
    default_weight FLOAT DEFAULT 1.0,
    priority_level INTEGER DEFAULT 1,
    is_active BOOLEAN DEFAULT TRUE,
    created_by VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建评估任务表
CREATE TABLE evaluation_tasks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    task_name VARCHAR(255) NOT NULL,
    task_description TEXT,
    scheme_ids JSONB NOT NULL,
    scheme_names JSONB NOT NULL,
    evaluation_scenarios JSONB NOT NULL,
    evaluation_indicators JSONB NOT NULL,
    indicator_weights JSONB NOT NULL,
    weight_config_id UUID REFERENCES indicator_weight_configs(id),
    evaluation_method evaluation_method_enum NOT NULL,
    method_parameters JSONB,
    sensitivity_analysis BOOLEAN DEFAULT FALSE,
    uncertainty_analysis BOOLEAN DEFAULT FALSE,
    comparison_type comparison_type_enum DEFAULT 'ranking',
    status evaluation_status_enum DEFAULT 'pending',
    progress_percentage INTEGER DEFAULT 0,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    evaluation_duration INTEGER,
    scheme_scores JSONB,
    scheme_ranking JSONB,
    detailed_scores JSONB,
    comparison_analysis JSONB,
    sensitivity_results JSONB,
    improvement_suggestions JSONB,
    error_message TEXT,
    error_details JSONB,
    created_by VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建权重配置表
CREATE TABLE weight_configurations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    config_name VARCHAR(255) NOT NULL,
    config_description TEXT,
    applicable_scenarios JSONB NOT NULL,
    scenario_conditions JSONB,
    indicator_weights JSONB NOT NULL,
    category_weights JSONB,
    weight_source VARCHAR(100) NOT NULL,
    source_details JSONB,
    consistency_ratio FLOAT,
    validation_results JSONB,
    version VARCHAR(50) DEFAULT '1.0.0',
    is_active BOOLEAN DEFAULT TRUE,
    is_default BOOLEAN DEFAULT FALSE,
    valid_from TIMESTAMP WITH TIME ZONE,
    valid_until TIMESTAMP WITH TIME ZONE,
    created_by VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建评估结果表
CREATE TABLE evaluation_results (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    task_id UUID NOT NULL REFERENCES evaluation_tasks(id) ON DELETE CASCADE,
    scheme_id VARCHAR(255) NOT NULL,
    scheme_name VARCHAR(255) NOT NULL,
    total_score FLOAT NOT NULL,
    normalized_score FLOAT NOT NULL,
    ranking_position INTEGER NOT NULL,
    category_scores JSONB NOT NULL,
    indicator_scores JSONB NOT NULL,
    indicator_contributions JSONB NOT NULL,
    strength_indicators JSONB NOT NULL DEFAULT '[]',
    weakness_indicators JSONB NOT NULL DEFAULT '[]',
    relative_performance JSONB,
    gap_analysis JSONB,
    weight_sensitivity JSONB,
    parameter_sensitivity JSONB,
    uncertainty_bounds JSONB,
    confidence_intervals JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引以提高查询性能
CREATE INDEX idx_optimization_tasks_status ON optimization_tasks(status);
CREATE INDEX idx_optimization_tasks_type ON optimization_tasks(task_type);
CREATE INDEX idx_optimization_tasks_created_at ON optimization_tasks(created_at);

CREATE INDEX idx_path_planning_tasks_status ON path_planning_tasks(status);
CREATE INDEX idx_path_planning_tasks_type ON path_planning_tasks(path_type);

CREATE INDEX idx_multi_objective_tasks_status ON multi_objective_tasks(status);
CREATE INDEX idx_multi_objective_tasks_algorithm ON multi_objective_tasks(algorithm_type);

CREATE INDEX idx_evaluation_tasks_status ON evaluation_tasks(status);
CREATE INDEX idx_evaluation_tasks_method ON evaluation_tasks(evaluation_method);

CREATE INDEX idx_pareto_solutions_task_id ON pareto_solutions(task_id);
CREATE INDEX idx_pareto_solutions_generation ON pareto_solutions(generation);

CREATE INDEX idx_evaluation_results_task_id ON evaluation_results(task_id);
CREATE INDEX idx_evaluation_results_ranking ON evaluation_results(ranking_position);

-- 添加表注释
COMMENT ON TABLE optimization_tasks IS '优化任务表 - 存储各类优化任务的基本信息和执行状态';
COMMENT ON TABLE algorithm_configurations IS '算法配置表 - 存储各类算法的配置模板和参数设置';
COMMENT ON TABLE optimization_results IS '优化结果表 - 存储优化算法的详细执行结果';
COMMENT ON TABLE path_planning_tasks IS '路径规划任务表 - 存储路径规划任务的详细信息';
COMMENT ON TABLE work_area_topology IS '作业区域拓扑表 - 存储作业区域的拓扑结构信息';
COMMENT ON TABLE path_planning_results IS '路径规划结果表 - 存储路径规划的详细结果数据';
COMMENT ON TABLE multi_objective_tasks IS '多目标优化任务表 - 存储多目标优化任务的详细信息';
COMMENT ON TABLE pareto_solutions IS '帕累托解表 - 存储帕累托最优解的详细信息';
COMMENT ON TABLE objective_functions IS '目标函数表 - 存储目标函数的详细定义';
COMMENT ON TABLE evaluation_tasks IS '评估任务表 - 存储综合评估任务的详细信息';
COMMENT ON TABLE weight_configurations IS '权重配置表 - 存储不同场景下的指标权重配置';
COMMENT ON TABLE evaluation_results IS '评估结果表 - 存储详细的评估结果和分析数据';
