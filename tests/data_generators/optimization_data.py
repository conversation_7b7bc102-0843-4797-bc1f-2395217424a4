"""
优化算法测试数据生成器

遵循base-rules.md规范：
- 函数复杂度控制，每个函数圈复杂度不超过10
- 函数参数不超过5个
- 使用描述性的函数命名
"""

import random
import numpy as np
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta

from src.database.models.optimization import (
    OptimizationTaskTypeEnum,
    OptimizationStatusEnum,
    AlgorithmTypeEnum
)


class OptimizationDataGenerator:
    """
    优化算法测试数据生成器
    
    生成各种优化算法的测试数据：
    - 优化任务数据
    - 算法配置数据
    - 问题定义数据
    - 结果数据
    """
    
    def __init__(self, random_seed: Optional[int] = None) -> None:
        """
        初始化数据生成器
        
        Args:
            random_seed: 随机种子
        """
        if random_seed is not None:
            random.seed(random_seed)
            np.random.seed(random_seed)
    
    def generate_optimization_task(self, 
                                 task_type: OptimizationTaskTypeEnum = None,
                                 algorithm_type: AlgorithmTypeEnum = None) -> Dict[str, Any]:
        """
        生成优化任务数据
        
        Args:
            task_type: 任务类型
            algorithm_type: 算法类型
            
        Returns:
            Dict[str, Any]: 优化任务数据
        """
        if task_type is None:
            task_type = random.choice(list(OptimizationTaskTypeEnum))
        
        if algorithm_type is None:
            algorithm_type = random.choice(list(AlgorithmTypeEnum))
        
        task_names = [
            "生产调度优化", "资源分配优化", "路径规划优化", 
            "库存管理优化", "成本控制优化", "效率提升优化"
        ]
        
        return {
            "task_name": random.choice(task_names) + f"_{random.randint(1000, 9999)}",
            "task_description": f"使用{algorithm_type.value}算法进行{task_type.value}优化",
            "task_type": task_type,
            "algorithm_type": algorithm_type,
            "algorithm_config": self._generate_algorithm_config(algorithm_type),
            "problem_definition": self._generate_problem_definition(task_type),
            "created_by": f"test_user_{random.randint(1, 100)}",
            "scenario_id": f"scenario_{random.randint(1, 50)}"
        }
    
    def _generate_algorithm_config(self, algorithm_type: AlgorithmTypeEnum) -> Dict[str, Any]:
        """生成算法配置"""
        base_config = {
            "max_iterations": random.randint(100, 2000),
            "convergence_tolerance": random.uniform(1e-6, 1e-3),
            "max_time_seconds": random.randint(60, 3600)
        }
        
        if algorithm_type == AlgorithmTypeEnum.GENETIC_ALGORITHM:
            base_config.update({
                "population_size": random.randint(20, 200),
                "crossover_rate": random.uniform(0.6, 0.95),
                "mutation_rate": random.uniform(0.01, 0.3),
                "selection_method": random.choice(["tournament", "roulette", "rank"]),
                "encoding_type": random.choice(["binary", "real", "permutation"])
            })
        elif algorithm_type == AlgorithmTypeEnum.PARTICLE_SWARM:
            base_config.update({
                "population_size": random.randint(10, 100),
                "inertia_weight": random.uniform(0.1, 1.0),
                "cognitive_coefficient": random.uniform(0.5, 4.0),
                "social_coefficient": random.uniform(0.5, 4.0),
                "max_velocity": random.uniform(0.1, 2.0)
            })
        elif algorithm_type == AlgorithmTypeEnum.SIMULATED_ANNEALING:
            base_config.update({
                "initial_temperature": random.uniform(100, 10000),
                "final_temperature": random.uniform(0.001, 1.0),
                "cooling_rate": random.uniform(0.8, 0.99),
                "cooling_schedule": random.choice(["exponential", "linear", "logarithmic"])
            })
        
        return base_config
    
    def _generate_problem_definition(self, task_type: OptimizationTaskTypeEnum) -> Dict[str, Any]:
        """生成问题定义"""
        if task_type == OptimizationTaskTypeEnum.RESOURCE_SCHEDULING:
            return self._generate_scheduling_problem()
        elif task_type == OptimizationTaskTypeEnum.PATH_OPTIMIZATION:
            return self._generate_path_problem()
        elif task_type == OptimizationTaskTypeEnum.ROBUST_OPTIMIZATION:
            return self._generate_robust_problem()
        else:
            return self._generate_generic_problem()
    
    def _generate_scheduling_problem(self) -> Dict[str, Any]:
        """生成调度问题定义"""
        num_resources = random.randint(3, 10)
        num_tasks = random.randint(5, 20)
        
        resources = []
        for i in range(num_resources):
            resources.append({
                "id": f"resource_{i}",
                "name": f"资源{i+1}",
                "capacity": random.randint(1, 5),
                "efficiency": random.uniform(0.5, 2.0),
                "cost_per_hour": random.uniform(5.0, 50.0)
            })
        
        tasks = []
        for i in range(num_tasks):
            tasks.append({
                "id": f"task_{i}",
                "name": f"任务{i+1}",
                "duration": random.uniform(1.0, 10.0),
                "priority": random.randint(1, 5),
                "resource_requirements": random.randint(1, 3)
            })
        
        constraints = [
            {"type": "resource_capacity", "description": "资源容量限制"},
            {"type": "task_dependency", "description": "任务依赖关系"},
            {"type": "time_window", "description": "时间窗口约束"}
        ]
        
        return {
            "resources": resources,
            "tasks": tasks,
            "constraints": constraints,
            "optimization_objectives": ["minimize_makespan", "minimize_cost"]
        }
    
    def _generate_path_problem(self) -> Dict[str, Any]:
        """生成路径问题定义"""
        num_nodes = random.randint(10, 50)
        
        nodes = []
        for i in range(num_nodes):
            nodes.append({
                "id": f"node_{i}",
                "x": random.uniform(0, 100),
                "y": random.uniform(0, 100),
                "type": random.choice(["normal", "depot", "customer"])
            })
        
        edges = []
        for i in range(num_nodes):
            for j in range(i+1, min(i+5, num_nodes)):  # 限制连接数
                distance = np.sqrt((nodes[i]["x"] - nodes[j]["x"])**2 + 
                                 (nodes[i]["y"] - nodes[j]["y"])**2)
                edges.append({
                    "from": f"node_{i}",
                    "to": f"node_{j}",
                    "weight": distance,
                    "type": "undirected"
                })
        
        return {
            "nodes": nodes,
            "edges": edges,
            "start_node": "node_0",
            "end_node": f"node_{num_nodes-1}",
            "heuristic": "euclidean"
        }
    
    def _generate_robust_problem(self) -> Dict[str, Any]:
        """生成鲁棒优化问题定义"""
        decision_dimension = random.randint(3, 10)
        
        return {
            "decision_dimension": decision_dimension,
            "decision_bounds": [(0, 1)] * decision_dimension,
            "uncertainty_set": {
                "parameter_names": [f"param_{i}" for i in range(3)],
                "nominal_values": [random.uniform(0.5, 2.0) for _ in range(3)],
                "uncertainty_bounds": [(-0.2, 0.2) for _ in range(3)],
                "uncertainty_type": "box"
            },
            "robustness_level": random.uniform(0.05, 0.3)
        }
    
    def _generate_generic_problem(self) -> Dict[str, Any]:
        """生成通用问题定义"""
        dimension = random.randint(2, 20)
        
        return {
            "dimension": dimension,
            "bounds": [(random.uniform(-10, -1), random.uniform(1, 10)) for _ in range(dimension)],
            "objective_type": random.choice(["minimize", "maximize"]),
            "constraints": []
        }
    
    def generate_optimization_results(self, task_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成优化结果数据
        
        Args:
            task_data: 任务数据
            
        Returns:
            Dict[str, Any]: 优化结果数据
        """
        algorithm_type = task_data["algorithm_type"]
        
        # 生成基本结果
        best_fitness = random.uniform(0.1, 100.0)
        iterations = random.randint(50, task_data["algorithm_config"]["max_iterations"])
        
        result_data = {
            "best_fitness": best_fitness,
            "iterations_completed": iterations,
            "execution_time_seconds": random.uniform(1.0, 300.0),
            "converged": random.choice([True, False]),
            "feasible": random.choice([True, True, True, False])  # 75%概率可行
        }
        
        # 根据算法类型生成特定结果
        if algorithm_type == AlgorithmTypeEnum.GENETIC_ALGORITHM:
            result_data.update({
                "final_population_diversity": random.uniform(0.1, 1.0),
                "convergence_rate": random.uniform(0.001, 0.1),
                "best_individual": [random.uniform(-1, 1) for _ in range(5)]
            })
        elif algorithm_type == AlgorithmTypeEnum.PARTICLE_SWARM:
            result_data.update({
                "swarm_diversity": random.uniform(0.1, 1.0),
                "average_velocity": random.uniform(0.01, 0.5),
                "best_position": [random.uniform(-1, 1) for _ in range(5)]
            })
        elif algorithm_type == AlgorithmTypeEnum.SIMULATED_ANNEALING:
            result_data.update({
                "final_temperature": random.uniform(0.001, 1.0),
                "acceptance_rate": random.uniform(0.1, 0.8),
                "restart_count": random.randint(0, 5)
            })
        
        return result_data
    
    def generate_batch_tasks(self, count: int) -> List[Dict[str, Any]]:
        """
        批量生成优化任务
        
        Args:
            count: 生成数量
            
        Returns:
            List[Dict[str, Any]]: 任务列表
        """
        tasks = []
        for _ in range(count):
            task = self.generate_optimization_task()
            tasks.append(task)
        
        return tasks
    
    def generate_performance_test_data(self) -> Dict[str, Any]:
        """
        生成性能测试数据
        
        Returns:
            Dict[str, Any]: 性能测试数据
        """
        return {
            "large_scale_problem": {
                "dimension": 1000,
                "population_size": 500,
                "max_iterations": 10000,
                "problem_type": "high_dimensional_optimization"
            },
            "multi_modal_problem": {
                "dimension": 20,
                "num_local_optima": 50,
                "problem_type": "multi_modal_optimization"
            },
            "constrained_problem": {
                "dimension": 10,
                "num_constraints": 15,
                "constraint_types": ["equality", "inequality", "bound"],
                "problem_type": "constrained_optimization"
            }
        }
    
    def generate_benchmark_problems(self) -> List[Dict[str, Any]]:
        """
        生成基准测试问题
        
        Returns:
            List[Dict[str, Any]]: 基准问题列表
        """
        benchmark_problems = [
            {
                "name": "Sphere Function",
                "dimension": 30,
                "bounds": [(-5.12, 5.12)] * 30,
                "global_optimum": 0.0,
                "characteristics": ["unimodal", "separable", "scalable"]
            },
            {
                "name": "Rosenbrock Function", 
                "dimension": 30,
                "bounds": [(-2.048, 2.048)] * 30,
                "global_optimum": 0.0,
                "characteristics": ["unimodal", "non-separable", "narrow_valley"]
            },
            {
                "name": "Rastrigin Function",
                "dimension": 30,
                "bounds": [(-5.12, 5.12)] * 30,
                "global_optimum": 0.0,
                "characteristics": ["multimodal", "separable", "many_local_optima"]
            },
            {
                "name": "Ackley Function",
                "dimension": 30,
                "bounds": [(-32.768, 32.768)] * 30,
                "global_optimum": 0.0,
                "characteristics": ["multimodal", "non-separable", "exponential"]
            }
        ]
        
        return benchmark_problems
