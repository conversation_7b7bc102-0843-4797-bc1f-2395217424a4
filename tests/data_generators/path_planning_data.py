"""
路径规划测试数据生成器

遵循base-rules.md规范：
- 函数复杂度控制，每个函数圈复杂度不超过10
- 函数参数不超过5个
- 使用描述性的函数命名
"""

import random
import numpy as np
from typing import Dict, Any, List, Optional, Tuple

from src.database.models.path_planning import (
    PathPlanningStatusEnum,
    PathTypeEnum,
    OptimizationObjectiveEnum
)


class PathPlanningDataGenerator:
    """
    路径规划测试数据生成器
    
    生成各种路径规划的测试数据：
    - 路径规划任务数据
    - 拓扑图数据
    - 约束条件数据
    - 路径结果数据
    """
    
    def __init__(self, random_seed: Optional[int] = None) -> None:
        """
        初始化数据生成器
        
        Args:
            random_seed: 随机种子
        """
        if random_seed is not None:
            random.seed(random_seed)
            np.random.seed(random_seed)
    
    def generate_path_planning_task(self, 
                                  path_type: PathTypeEnum = None,
                                  complexity: str = "medium") -> Dict[str, Any]:
        """
        生成路径规划任务数据
        
        Args:
            path_type: 路径类型
            complexity: 复杂度级别
            
        Returns:
            Dict[str, Any]: 路径规划任务数据
        """
        if path_type is None:
            path_type = random.choice(list(PathTypeEnum))
        
        task_names = {
            PathTypeEnum.WAREHOUSE_INTERNAL: "仓库内部路径规划",
            PathTypeEnum.GROUND_TRANSPORT: "地面运输路径规划", 
            PathTypeEnum.APRON_OPERATION: "停机坪作业路径规划",
            PathTypeEnum.INTEGRATED_PATH: "综合路径规划"
        }
        
        # 生成起终点
        start_point, end_point = self._generate_start_end_points(path_type)
        
        # 生成中间路径点
        waypoints = self._generate_waypoints(path_type, complexity)
        
        # 生成约束条件
        constraints = self._generate_path_constraints(path_type)
        
        # 生成优化目标
        objectives = self._generate_optimization_objectives(path_type)
        
        return {
            "task_name": task_names[path_type] + f"_{random.randint(1000, 9999)}",
            "task_description": f"{path_type.value}的路径优化任务",
            "path_type": path_type,
            "start_point": start_point,
            "end_point": end_point,
            "waypoints": waypoints,
            "constraints": constraints,
            "optimization_objectives": objectives,
            "objective_weights": self._generate_objective_weights(objectives),
            "algorithm_type": random.choice(["dijkstra", "a_star", "dynamic_programming"]),
            "algorithm_parameters": self._generate_algorithm_parameters(),
            "created_by": f"test_user_{random.randint(1, 100)}"
        }
    
    def _generate_start_end_points(self, path_type: PathTypeEnum) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """生成起终点"""
        if path_type == PathTypeEnum.WAREHOUSE_INTERNAL:
            start_point = {
                "node_id": "warehouse_entrance",
                "coordinates": [0.0, 0.0],
                "node_type": "entrance",
                "properties": {"zone": "entry", "capacity": 10}
            }
            end_point = {
                "node_id": f"storage_location_{random.randint(1, 100)}",
                "coordinates": [random.uniform(50, 200), random.uniform(50, 200)],
                "node_type": "storage",
                "properties": {"zone": "storage", "capacity": 5}
            }
        elif path_type == PathTypeEnum.GROUND_TRANSPORT:
            start_point = {
                "node_id": "depot_start",
                "coordinates": [random.uniform(0, 10), random.uniform(0, 10)],
                "node_type": "depot",
                "properties": {"facility_type": "loading_dock"}
            }
            end_point = {
                "node_id": "destination_terminal",
                "coordinates": [random.uniform(500, 1000), random.uniform(500, 1000)],
                "node_type": "terminal",
                "properties": {"facility_type": "unloading_dock"}
            }
        elif path_type == PathTypeEnum.APRON_OPERATION:
            start_point = {
                "node_id": "gate_position",
                "coordinates": [random.uniform(0, 50), random.uniform(0, 50)],
                "node_type": "gate",
                "properties": {"gate_number": f"G{random.randint(1, 50)}"}
            }
            end_point = {
                "node_id": "aircraft_stand",
                "coordinates": [random.uniform(100, 300), random.uniform(100, 300)],
                "node_type": "stand",
                "properties": {"stand_number": f"S{random.randint(1, 20)}"}
            }
        else:  # INTEGRATED_PATH
            start_point = {
                "node_id": "origin_hub",
                "coordinates": [0.0, 0.0],
                "node_type": "hub",
                "properties": {"hub_type": "origin"}
            }
            end_point = {
                "node_id": "destination_hub",
                "coordinates": [random.uniform(800, 1200), random.uniform(800, 1200)],
                "node_type": "hub", 
                "properties": {"hub_type": "destination"}
            }
        
        return start_point, end_point
    
    def _generate_waypoints(self, path_type: PathTypeEnum, complexity: str) -> List[Dict[str, Any]]:
        """生成中间路径点"""
        if complexity == "simple":
            num_waypoints = random.randint(0, 2)
        elif complexity == "medium":
            num_waypoints = random.randint(2, 5)
        else:  # complex
            num_waypoints = random.randint(5, 10)
        
        waypoints = []
        for i in range(num_waypoints):
            if path_type == PathTypeEnum.WAREHOUSE_INTERNAL:
                waypoint = {
                    "node_id": f"waypoint_w{i}",
                    "coordinates": [random.uniform(10, 180), random.uniform(10, 180)],
                    "node_type": "waypoint",
                    "properties": {"zone": random.choice(["picking", "packing", "sorting"])}
                }
            elif path_type == PathTypeEnum.GROUND_TRANSPORT:
                waypoint = {
                    "node_id": f"checkpoint_c{i}",
                    "coordinates": [random.uniform(50, 900), random.uniform(50, 900)],
                    "node_type": "checkpoint",
                    "properties": {"checkpoint_type": random.choice(["fuel", "inspection", "rest"])}
                }
            elif path_type == PathTypeEnum.APRON_OPERATION:
                waypoint = {
                    "node_id": f"service_point_s{i}",
                    "coordinates": [random.uniform(20, 280), random.uniform(20, 280)],
                    "node_type": "service",
                    "properties": {"service_type": random.choice(["fuel", "catering", "cleaning"])}
                }
            else:  # INTEGRATED_PATH
                waypoint = {
                    "node_id": f"transfer_point_t{i}",
                    "coordinates": [random.uniform(100, 1100), random.uniform(100, 1100)],
                    "node_type": "transfer",
                    "properties": {"transfer_type": random.choice(["modal", "hub", "consolidation"])}
                }
            
            waypoints.append(waypoint)
        
        return waypoints
    
    def _generate_path_constraints(self, path_type: PathTypeEnum) -> Dict[str, Any]:
        """生成路径约束条件"""
        base_constraints = {
            "safety_distance": random.uniform(1.0, 5.0),
            "max_path_length": random.uniform(100, 1000),
            "time_limit": random.uniform(30, 300)  # 分钟
        }
        
        if path_type == PathTypeEnum.WAREHOUSE_INTERNAL:
            base_constraints.update({
                "aisle_width": random.uniform(2.0, 4.0),
                "load_capacity": random.uniform(100, 1000),
                "restricted_zones": [f"zone_{i}" for i in range(random.randint(1, 3))]
            })
        elif path_type == PathTypeEnum.GROUND_TRANSPORT:
            base_constraints.update({
                "vehicle_type": random.choice(["truck", "van", "trailer"]),
                "weight_limit": random.uniform(1000, 40000),
                "height_limit": random.uniform(2.5, 4.5),
                "road_restrictions": random.choice([True, False])
            })
        elif path_type == PathTypeEnum.APRON_OPERATION:
            base_constraints.update({
                "aircraft_type": random.choice(["narrow_body", "wide_body", "regional"]),
                "ground_equipment": random.choice(["tug", "pushback", "belt_loader"]),
                "weather_conditions": random.choice(["clear", "rain", "fog"]),
                "noise_restrictions": random.choice([True, False])
            })
        
        # 添加时间窗口约束
        if random.choice([True, False]):
            num_windows = random.randint(1, 3)
            time_windows = []
            for _ in range(num_windows):
                start_time = random.randint(0, 1200)  # 分钟
                duration = random.randint(30, 240)
                time_windows.append({
                    "start": start_time,
                    "end": start_time + duration,
                    "type": random.choice(["allowed", "restricted"])
                })
            base_constraints["time_windows"] = time_windows
        
        return base_constraints
    
    def _generate_optimization_objectives(self, path_type: PathTypeEnum) -> List[OptimizationObjectiveEnum]:
        """生成优化目标"""
        all_objectives = list(OptimizationObjectiveEnum)
        
        # 根据路径类型选择合适的目标
        if path_type == PathTypeEnum.WAREHOUSE_INTERNAL:
            preferred_objectives = [
                OptimizationObjectiveEnum.SHORTEST_DISTANCE,
                OptimizationObjectiveEnum.MINIMUM_TIME,
                OptimizationObjectiveEnum.MAXIMUM_SAFETY
            ]
        elif path_type == PathTypeEnum.GROUND_TRANSPORT:
            preferred_objectives = [
                OptimizationObjectiveEnum.SHORTEST_DISTANCE,
                OptimizationObjectiveEnum.LOWEST_COST,
                OptimizationObjectiveEnum.MINIMUM_TIME
            ]
        elif path_type == PathTypeEnum.APRON_OPERATION:
            preferred_objectives = [
                OptimizationObjectiveEnum.MINIMUM_TIME,
                OptimizationObjectiveEnum.MAXIMUM_SAFETY,
                OptimizationObjectiveEnum.MINIMUM_CONGESTION
            ]
        else:  # INTEGRATED_PATH
            preferred_objectives = all_objectives
        
        # 随机选择1-3个目标
        num_objectives = random.randint(1, min(3, len(preferred_objectives)))
        selected_objectives = random.sample(preferred_objectives, num_objectives)
        
        return selected_objectives
    
    def _generate_objective_weights(self, objectives: List[OptimizationObjectiveEnum]) -> Dict[str, float]:
        """生成目标权重"""
        weights = {}
        total_weight = 0.0
        
        # 生成随机权重
        for obj in objectives:
            weight = random.uniform(0.1, 1.0)
            weights[obj.value] = weight
            total_weight += weight
        
        # 归一化权重
        for obj in objectives:
            weights[obj.value] /= total_weight
        
        return weights
    
    def _generate_algorithm_parameters(self) -> Dict[str, Any]:
        """生成算法参数"""
        return {
            "max_iterations": random.randint(100, 2000),
            "convergence_tolerance": random.uniform(1e-6, 1e-3),
            "max_time_seconds": random.randint(30, 600),
            "heuristic": random.choice(["euclidean", "manhattan", "chebyshev"]),
            "parallel_execution": random.choice([True, False])
        }
    
    def generate_work_area_topology(self, area_type: str = "warehouse") -> Dict[str, Any]:
        """
        生成作业区域拓扑数据
        
        Args:
            area_type: 区域类型
            
        Returns:
            Dict[str, Any]: 拓扑数据
        """
        if area_type == "warehouse":
            return self._generate_warehouse_topology()
        elif area_type == "transport":
            return self._generate_transport_topology()
        elif area_type == "apron":
            return self._generate_apron_topology()
        else:
            return self._generate_integrated_topology()
    
    def _generate_warehouse_topology(self) -> Dict[str, Any]:
        """生成仓库拓扑"""
        # 生成网格状仓库布局
        rows, cols = random.randint(5, 15), random.randint(5, 15)
        nodes = []
        edges = []
        
        # 生成节点
        for i in range(rows):
            for j in range(cols):
                node_id = f"w_{i}_{j}"
                node_type = "aisle" if (i % 2 == 0 or j % 2 == 0) else "storage"
                
                nodes.append({
                    "id": node_id,
                    "x": j * 10.0,
                    "y": i * 10.0,
                    "type": node_type,
                    "properties": {
                        "capacity": random.randint(1, 10) if node_type == "storage" else 0,
                        "zone": f"zone_{i//3}_{j//3}"
                    }
                })
        
        # 生成边（相邻节点连接）
        for i in range(rows):
            for j in range(cols):
                current_id = f"w_{i}_{j}"
                
                # 连接右侧节点
                if j < cols - 1:
                    next_id = f"w_{i}_{j+1}"
                    edges.append({
                        "from": current_id,
                        "to": next_id,
                        "weight": 10.0,
                        "type": "bidirectional"
                    })
                
                # 连接下方节点
                if i < rows - 1:
                    next_id = f"w_{i+1}_{j}"
                    edges.append({
                        "from": current_id,
                        "to": next_id,
                        "weight": 10.0,
                        "type": "bidirectional"
                    })
        
        return {
            "area_name": f"仓库区域_{random.randint(1, 100)}",
            "area_type": "warehouse",
            "area_description": "自动生成的仓库拓扑结构",
            "nodes": nodes,
            "edges": edges,
            "area_properties": {
                "total_area": rows * cols * 100,  # 平方米
                "storage_capacity": sum(1 for node in nodes if node["type"] == "storage"),
                "aisle_width": 3.0
            },
            "boundary_coordinates": [[0, 0], [cols*10, 0], [cols*10, rows*10], [0, rows*10]],
            "center_coordinates": [cols*5, rows*5],
            "created_by": "data_generator"
        }
    
    def _generate_transport_topology(self) -> Dict[str, Any]:
        """生成运输网络拓扑"""
        num_nodes = random.randint(10, 30)
        nodes = []
        edges = []
        
        # 生成节点
        for i in range(num_nodes):
            node_type = random.choice(["depot", "customer", "hub", "intersection"])
            nodes.append({
                "id": f"t_{i}",
                "x": random.uniform(0, 1000),
                "y": random.uniform(0, 1000),
                "type": node_type,
                "properties": {
                    "demand": random.randint(0, 100) if node_type == "customer" else 0,
                    "capacity": random.randint(50, 500) if node_type in ["depot", "hub"] else 0
                }
            })
        
        # 生成边（随机连接）
        for i in range(num_nodes):
            # 每个节点连接2-5个其他节点
            num_connections = random.randint(2, min(5, num_nodes-1))
            connected_nodes = random.sample([j for j in range(num_nodes) if j != i], num_connections)
            
            for j in connected_nodes:
                if not any(edge["from"] == f"t_{j}" and edge["to"] == f"t_{i}" for edge in edges):
                    distance = np.sqrt((nodes[i]["x"] - nodes[j]["x"])**2 + 
                                     (nodes[i]["y"] - nodes[j]["y"])**2)
                    edges.append({
                        "from": f"t_{i}",
                        "to": f"t_{j}",
                        "weight": distance,
                        "type": "bidirectional",
                        "properties": {
                            "road_type": random.choice(["highway", "arterial", "local"]),
                            "speed_limit": random.randint(30, 120)
                        }
                    })
        
        return {
            "area_name": f"运输网络_{random.randint(1, 100)}",
            "area_type": "transport",
            "area_description": "自动生成的运输网络拓扑",
            "nodes": nodes,
            "edges": edges,
            "area_properties": {
                "network_density": len(edges) / (num_nodes * (num_nodes - 1) / 2),
                "average_distance": np.mean([edge["weight"] for edge in edges]),
                "total_nodes": num_nodes
            },
            "created_by": "data_generator"
        }
    
    def _generate_apron_topology(self) -> Dict[str, Any]:
        """生成停机坪拓扑"""
        num_stands = random.randint(5, 20)
        num_gates = random.randint(3, 10)
        
        nodes = []
        edges = []
        
        # 生成停机位
        for i in range(num_stands):
            nodes.append({
                "id": f"stand_{i}",
                "x": random.uniform(100, 900),
                "y": random.uniform(100, 900),
                "type": "aircraft_stand",
                "properties": {
                    "aircraft_type": random.choice(["narrow_body", "wide_body", "regional"]),
                    "gate_connected": random.choice([True, False])
                }
            })
        
        # 生成登机口
        for i in range(num_gates):
            nodes.append({
                "id": f"gate_{i}",
                "x": random.uniform(0, 100),
                "y": random.uniform(200, 800),
                "type": "gate",
                "properties": {
                    "terminal": random.choice(["T1", "T2", "T3"]),
                    "capacity": random.randint(100, 400)
                }
            })
        
        # 生成服务点
        service_types = ["fuel", "catering", "cleaning", "maintenance"]
        for service_type in service_types:
            nodes.append({
                "id": f"service_{service_type}",
                "x": random.uniform(50, 950),
                "y": random.uniform(50, 950),
                "type": "service_point",
                "properties": {
                    "service_type": service_type,
                    "capacity": random.randint(2, 10)
                }
            })
        
        # 生成滑行道连接
        all_nodes = len(nodes)
        for i in range(all_nodes):
            # 每个节点连接到最近的几个节点
            distances = []
            for j in range(all_nodes):
                if i != j:
                    dist = np.sqrt((nodes[i]["x"] - nodes[j]["x"])**2 + 
                                 (nodes[i]["y"] - nodes[j]["y"])**2)
                    distances.append((j, dist))
            
            # 连接到最近的2-4个节点
            distances.sort(key=lambda x: x[1])
            num_connections = random.randint(2, min(4, len(distances)))
            
            for j, dist in distances[:num_connections]:
                if not any(edge["from"] == nodes[j]["id"] and edge["to"] == nodes[i]["id"] for edge in edges):
                    edges.append({
                        "from": nodes[i]["id"],
                        "to": nodes[j]["id"],
                        "weight": dist,
                        "type": "taxiway",
                        "properties": {
                            "width": random.uniform(15, 30),
                            "surface": random.choice(["asphalt", "concrete"])
                        }
                    })
        
        return {
            "area_name": f"停机坪区域_{random.randint(1, 100)}",
            "area_type": "apron",
            "area_description": "自动生成的停机坪拓扑结构",
            "nodes": nodes,
            "edges": edges,
            "area_properties": {
                "total_stands": num_stands,
                "total_gates": num_gates,
                "apron_area": 1000000,  # 平方米
                "service_points": len(service_types)
            },
            "created_by": "data_generator"
        }
    
    def _generate_integrated_topology(self) -> Dict[str, Any]:
        """生成综合拓扑"""
        # 结合多种区域类型
        warehouse_topo = self._generate_warehouse_topology()
        transport_topo = self._generate_transport_topology()
        
        # 合并节点和边
        all_nodes = warehouse_topo["nodes"] + transport_topo["nodes"]
        all_edges = warehouse_topo["edges"] + transport_topo["edges"]
        
        # 添加跨区域连接
        warehouse_nodes = [node["id"] for node in warehouse_topo["nodes"] if node["type"] == "aisle"]
        transport_nodes = [node["id"] for node in transport_topo["nodes"] if node["type"] == "depot"]
        
        for w_node in warehouse_nodes[:3]:  # 选择前3个仓库节点
            for t_node in transport_nodes[:2]:  # 选择前2个运输节点
                all_edges.append({
                    "from": w_node,
                    "to": t_node,
                    "weight": random.uniform(50, 200),
                    "type": "inter_area",
                    "properties": {"connection_type": "warehouse_to_transport"}
                })
        
        return {
            "area_name": f"综合区域_{random.randint(1, 100)}",
            "area_type": "integrated",
            "area_description": "自动生成的综合拓扑结构",
            "nodes": all_nodes,
            "edges": all_edges,
            "area_properties": {
                "total_nodes": len(all_nodes),
                "total_edges": len(all_edges),
                "sub_areas": ["warehouse", "transport"]
            },
            "created_by": "data_generator"
        }
