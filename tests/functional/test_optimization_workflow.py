"""
优化工作流功能测试

遵循base-rules.md规范：
- 函数复杂度控制，每个函数圈复杂度不超过10
- 函数参数不超过5个
- 使用描述性的函数命名
"""

import pytest
import asyncio
from typing import Dict, Any
from unittest.mock import AsyncMock, MagicMock

from tests.data_generators.optimization_data import OptimizationDataGenerator
from src.services.advanced_scheduling import AdvancedSchedulingService
from src.services.robust_optimization import RobustOptimizationService
from src.services.multi_objective_optimization import MultiObjectiveOptimizationService
from src.database.models.optimization import OptimizationStatusEnum, AlgorithmTypeEnum


class TestOptimizationWorkflow:
    """优化工作流功能测试类"""
    
    @pytest.fixture
    def data_generator(self):
        """数据生成器fixture"""
        return OptimizationDataGenerator(random_seed=42)
    
    @pytest.fixture
    def mock_session(self):
        """模拟数据库会话fixture"""
        session = AsyncMock()
        session.add = MagicMock()
        session.commit = AsyncMock()
        session.refresh = AsyncMock()
        return session
    
    @pytest.fixture
    def scheduling_service(self):
        """调度服务fixture"""
        return AdvancedSchedulingService()
    
    @pytest.fixture
    def robust_service(self):
        """鲁棒优化服务fixture"""
        return RobustOptimizationService()
    
    @pytest.fixture
    def mo_service(self):
        """多目标优化服务fixture"""
        return MultiObjectiveOptimizationService()
    
    @pytest.mark.asyncio
    async def test_create_scheduling_task_workflow(self, 
                                                 data_generator,
                                                 scheduling_service,
                                                 mock_session):
        """测试创建调度任务工作流"""
        # 生成测试数据
        task_data = data_generator.generate_optimization_task(
            algorithm_type=AlgorithmTypeEnum.GENETIC_ALGORITHM
        )
        
        # 模拟任务创建
        mock_task = MagicMock()
        mock_task.id = "test_task_123"
        mock_session.refresh.side_effect = lambda obj: setattr(obj, 'id', 'test_task_123')
        
        # 执行任务创建
        task_id = await scheduling_service.create_scheduling_task(mock_session, task_data)
        
        # 验证结果
        assert task_id == "test_task_123"
        mock_session.add.assert_called_once()
        mock_session.commit.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_scheduling_task_validation(self, 
                                            data_generator,
                                            scheduling_service,
                                            mock_session):
        """测试调度任务数据验证"""
        # 测试缺少必需字段
        invalid_data = {"task_name": "test"}
        
        with pytest.raises(ValueError, match="缺少必需字段"):
            await scheduling_service.create_scheduling_task(mock_session, invalid_data)
        
        # 测试无效算法类型
        invalid_data = data_generator.generate_optimization_task()
        invalid_data["algorithm_type"] = "invalid_algorithm"
        
        with pytest.raises(ValueError, match="不支持的算法类型"):
            await scheduling_service.create_scheduling_task(mock_session, invalid_data)
    
    @pytest.mark.asyncio
    async def test_robust_optimization_workflow(self,
                                              data_generator,
                                              robust_service,
                                              mock_session):
        """测试鲁棒优化工作流"""
        # 生成鲁棒优化任务数据
        task_data = {
            "task_name": "鲁棒优化测试",
            "problem_definition": {
                "decision_dimension": 5,
                "uncertainty_set": {
                    "parameter_names": ["demand", "cost"],
                    "nominal_values": [100, 1.0],
                    "uncertainty_bounds": [(-10, 10), (-0.2, 0.2)],
                    "uncertainty_type": "box"
                }
            }
        }
        
        # 模拟任务创建
        mock_session.refresh.side_effect = lambda obj: setattr(obj, 'id', 'robust_task_123')
        
        # 执行任务创建
        task_id = await robust_service.create_robust_optimization_task(mock_session, task_data)
        
        # 验证结果
        assert task_id == "robust_task_123"
        mock_session.add.assert_called_once()
        mock_session.commit.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_multi_objective_workflow(self,
                                          data_generator,
                                          mo_service,
                                          mock_session):
        """测试多目标优化工作流"""
        # 生成多目标优化任务数据
        task_data = {
            "task_name": "多目标优化测试",
            "objective_functions": [
                {"function_id": "obj1", "function_name": "目标1", "expression": "sum(x**2 for x in genes)", "type": "minimize"},
                {"function_id": "obj2", "function_name": "目标2", "expression": "sum(abs(x) for x in genes)", "type": "minimize"}
            ],
            "decision_variables": ["x1", "x2", "x3"],
            "variable_bounds": {
                "x1": {"lower": -5, "upper": 5},
                "x2": {"lower": -5, "upper": 5},
                "x3": {"lower": -5, "upper": 5}
            },
            "algorithm_type": "nsga2"
        }
        
        # 模拟任务创建
        mock_session.refresh.side_effect = lambda obj: setattr(obj, 'id', 'mo_task_123')
        
        # 执行任务创建
        task_id = await mo_service.create_multi_objective_task(mock_session, task_data)
        
        # 验证结果
        assert task_id == "mo_task_123"
        mock_session.add.assert_called_once()
        mock_session.commit.assert_called_once()
    
    def test_algorithm_configuration_generation(self, data_generator):
        """测试算法配置生成"""
        # 测试遗传算法配置
        ga_task = data_generator.generate_optimization_task(
            algorithm_type=AlgorithmTypeEnum.GENETIC_ALGORITHM
        )
        ga_config = ga_task["algorithm_config"]
        
        assert "population_size" in ga_config
        assert "crossover_rate" in ga_config
        assert "mutation_rate" in ga_config
        assert 0.6 <= ga_config["crossover_rate"] <= 0.95
        assert 0.01 <= ga_config["mutation_rate"] <= 0.3
        
        # 测试粒子群算法配置
        pso_task = data_generator.generate_optimization_task(
            algorithm_type=AlgorithmTypeEnum.PARTICLE_SWARM
        )
        pso_config = pso_task["algorithm_config"]
        
        assert "inertia_weight" in pso_config
        assert "cognitive_coefficient" in pso_config
        assert "social_coefficient" in pso_config
        assert 0.1 <= pso_config["inertia_weight"] <= 1.0
        
        # 测试模拟退火算法配置
        sa_task = data_generator.generate_optimization_task(
            algorithm_type=AlgorithmTypeEnum.SIMULATED_ANNEALING
        )
        sa_config = sa_task["algorithm_config"]
        
        assert "initial_temperature" in sa_config
        assert "final_temperature" in sa_config
        assert "cooling_rate" in sa_config
        assert sa_config["initial_temperature"] > sa_config["final_temperature"]
    
    def test_problem_definition_generation(self, data_generator):
        """测试问题定义生成"""
        from src.database.models.optimization import OptimizationTaskTypeEnum
        
        # 测试调度问题
        scheduling_task = data_generator.generate_optimization_task(
            task_type=OptimizationTaskTypeEnum.RESOURCE_SCHEDULING
        )
        problem_def = scheduling_task["problem_definition"]
        
        assert "resources" in problem_def
        assert "tasks" in problem_def
        assert "constraints" in problem_def
        assert len(problem_def["resources"]) >= 3
        assert len(problem_def["tasks"]) >= 5
        
        # 测试路径优化问题
        path_task = data_generator.generate_optimization_task(
            task_type=OptimizationTaskTypeEnum.PATH_OPTIMIZATION
        )
        problem_def = path_task["problem_definition"]
        
        assert "nodes" in problem_def
        assert "edges" in problem_def
        assert "start_node" in problem_def
        assert "end_node" in problem_def
        
        # 测试鲁棒优化问题
        robust_task = data_generator.generate_optimization_task(
            task_type=OptimizationTaskTypeEnum.ROBUST_OPTIMIZATION
        )
        problem_def = robust_task["problem_definition"]
        
        assert "decision_dimension" in problem_def
        assert "uncertainty_set" in problem_def
        assert "robustness_level" in problem_def
    
    def test_optimization_results_generation(self, data_generator):
        """测试优化结果生成"""
        # 生成任务数据
        task_data = data_generator.generate_optimization_task(
            algorithm_type=AlgorithmTypeEnum.GENETIC_ALGORITHM
        )
        
        # 生成结果数据
        result_data = data_generator.generate_optimization_results(task_data)
        
        # 验证基本结果字段
        assert "best_fitness" in result_data
        assert "iterations_completed" in result_data
        assert "execution_time_seconds" in result_data
        assert "converged" in result_data
        assert "feasible" in result_data
        
        # 验证遗传算法特定字段
        assert "final_population_diversity" in result_data
        assert "convergence_rate" in result_data
        assert "best_individual" in result_data
        
        # 验证数值范围
        assert result_data["best_fitness"] > 0
        assert result_data["execution_time_seconds"] > 0
        assert 0 <= result_data["final_population_diversity"] <= 1
    
    def test_batch_task_generation(self, data_generator):
        """测试批量任务生成"""
        batch_size = 10
        tasks = data_generator.generate_batch_tasks(batch_size)
        
        assert len(tasks) == batch_size
        
        # 验证每个任务都有必需字段
        for task in tasks:
            assert "task_name" in task
            assert "algorithm_type" in task
            assert "problem_definition" in task
            assert "algorithm_config" in task
    
    def test_benchmark_problems_generation(self, data_generator):
        """测试基准问题生成"""
        benchmark_problems = data_generator.generate_benchmark_problems()
        
        assert len(benchmark_problems) > 0
        
        # 验证基准问题结构
        for problem in benchmark_problems:
            assert "name" in problem
            assert "dimension" in problem
            assert "bounds" in problem
            assert "global_optimum" in problem
            assert "characteristics" in problem
            
            # 验证边界数量与维度匹配
            assert len(problem["bounds"]) == problem["dimension"]
    
    def test_performance_test_data_generation(self, data_generator):
        """测试性能测试数据生成"""
        perf_data = data_generator.generate_performance_test_data()
        
        # 验证大规模问题
        assert "large_scale_problem" in perf_data
        large_scale = perf_data["large_scale_problem"]
        assert large_scale["dimension"] >= 1000
        assert large_scale["population_size"] >= 500
        
        # 验证多峰问题
        assert "multi_modal_problem" in perf_data
        multi_modal = perf_data["multi_modal_problem"]
        assert multi_modal["num_local_optima"] > 1
        
        # 验证约束问题
        assert "constrained_problem" in perf_data
        constrained = perf_data["constrained_problem"]
        assert constrained["num_constraints"] > 0
    
    @pytest.mark.asyncio
    async def test_end_to_end_optimization_workflow(self,
                                                  data_generator,
                                                  scheduling_service,
                                                  mock_session):
        """测试端到端优化工作流"""
        # 1. 生成任务数据
        task_data = data_generator.generate_optimization_task()
        
        # 2. 创建任务
        mock_session.refresh.side_effect = lambda obj: setattr(obj, 'id', 'e2e_task_123')
        task_id = await scheduling_service.create_scheduling_task(mock_session, task_data)
        
        # 3. 验证任务创建
        assert task_id == "e2e_task_123"
        
        # 4. 模拟任务执行（由于涉及复杂的算法执行，这里只验证接口调用）
        # 在实际测试中，这里会调用execute_scheduling_optimization
        # 但由于算法执行时间较长，在单元测试中通常会mock
        
        # 5. 生成预期结果
        expected_result = data_generator.generate_optimization_results(task_data)
        
        # 6. 验证结果结构
        assert "best_fitness" in expected_result
        assert "iterations_completed" in expected_result
        assert expected_result["iterations_completed"] <= task_data["algorithm_config"]["max_iterations"]
    
    def test_data_consistency_validation(self, data_generator):
        """测试数据一致性验证"""
        # 生成多个任务，验证数据一致性
        tasks = data_generator.generate_batch_tasks(5)
        
        for task in tasks:
            # 验证算法配置与算法类型匹配
            algorithm_type = task["algorithm_type"]
            algorithm_config = task["algorithm_config"]
            
            if algorithm_type == AlgorithmTypeEnum.GENETIC_ALGORITHM:
                assert "population_size" in algorithm_config
                assert "crossover_rate" in algorithm_config
                assert "mutation_rate" in algorithm_config
            elif algorithm_type == AlgorithmTypeEnum.PARTICLE_SWARM:
                assert "inertia_weight" in algorithm_config
                assert "cognitive_coefficient" in algorithm_config
                assert "social_coefficient" in algorithm_config
            elif algorithm_type == AlgorithmTypeEnum.SIMULATED_ANNEALING:
                assert "initial_temperature" in algorithm_config
                assert "cooling_rate" in algorithm_config
            
            # 验证问题定义与任务类型匹配
            task_type = task.get("task_type")
            problem_def = task["problem_definition"]
            
            if task_type and hasattr(task_type, 'value'):
                if "SCHEDULING" in task_type.value:
                    assert "resources" in problem_def or "tasks" in problem_def
                elif "PATH" in task_type.value:
                    assert "nodes" in problem_def or "dimension" in problem_def
                elif "ROBUST" in task_type.value:
                    assert "uncertainty_set" in problem_def or "decision_dimension" in problem_def
