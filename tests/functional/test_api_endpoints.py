"""
API接口功能测试

遵循base-rules.md规范：
- 函数复杂度控制，每个函数圈复杂度不超过10
- 函数参数不超过5个
- 使用描述性的函数命名
"""

import pytest
import json
from typing import Dict, Any
from fastapi.testclient import TestClient
from unittest.mock import AsyncMock, MagicMock, patch

from src.main import app
from tests.data_generators.optimization_data import OptimizationDataGenerator
from tests.data_generators.path_planning_data import PathPlanningDataGenerator


class TestAPIEndpoints:
    """API接口功能测试类"""
    
    @pytest.fixture
    def client(self):
        """测试客户端fixture"""
        return TestClient(app)
    
    @pytest.fixture
    def optimization_data_generator(self):
        """优化数据生成器fixture"""
        return OptimizationDataGenerator(random_seed=42)
    
    @pytest.fixture
    def path_planning_data_generator(self):
        """路径规划数据生成器fixture"""
        return PathPlanningDataGenerator(random_seed=42)
    
    @pytest.fixture
    def mock_db_session(self):
        """模拟数据库会话fixture"""
        session = AsyncMock()
        session.add = MagicMock()
        session.commit = AsyncMock()
        session.refresh = AsyncMock()
        return session
    
    def test_health_check_endpoint(self, client):
        """测试健康检查接口"""
        response = client.get("/health")
        assert response.status_code == 200
        
        data = response.json()
        assert "status" in data
        assert data["status"] == "healthy"
    
    def test_api_documentation_endpoints(self, client):
        """测试API文档接口"""
        # 测试OpenAPI文档
        response = client.get("/docs")
        assert response.status_code == 200
        
        # 测试OpenAPI JSON
        response = client.get("/openapi.json")
        assert response.status_code == 200
        
        openapi_data = response.json()
        assert "openapi" in openapi_data
        assert "info" in openapi_data
        assert "paths" in openapi_data
    
    @patch('src.api.dependencies.get_db_session')
    def test_create_optimization_task_endpoint(self, 
                                             mock_get_db,
                                             client,
                                             optimization_data_generator,
                                             mock_db_session):
        """测试创建优化任务接口"""
        # 设置mock
        mock_get_db.return_value = mock_db_session
        mock_db_session.refresh.side_effect = lambda obj: setattr(obj, 'id', 'test_task_123')
        
        # 生成测试数据
        task_data = optimization_data_generator.generate_optimization_task()
        
        # 准备请求数据
        request_data = {
            "task_name": task_data["task_name"],
            "task_description": task_data["task_description"],
            "problem_definition": task_data["problem_definition"],
            "algorithm_config": task_data["algorithm_config"]
        }
        
        # 发送请求
        with patch('src.services.advanced_scheduling.AdvancedSchedulingService.create_scheduling_task') as mock_create:
            mock_create.return_value = "test_task_123"
            
            response = client.post(
                "/api/v1/advanced-scheduling/tasks",
                json=request_data
            )
        
        # 验证响应
        assert response.status_code == 201
        
        response_data = response.json()
        assert "task_id" in response_data
        assert "message" in response_data
        assert "status" in response_data
        assert response_data["status"] == "created"
    
    def test_get_scheduling_algorithms_endpoint(self, client):
        """测试获取调度算法列表接口"""
        response = client.get("/api/v1/advanced-scheduling/algorithms")
        assert response.status_code == 200
        
        algorithms = response.json()
        assert isinstance(algorithms, list)
        assert len(algorithms) > 0
        
        # 验证算法信息结构
        for algorithm in algorithms:
            assert "algorithm_type" in algorithm
            assert "algorithm_name" in algorithm
            assert "description" in algorithm
            assert "suitable_for" in algorithm
            assert "parameters" in algorithm
            assert "performance" in algorithm
    
    def test_get_problem_templates_endpoint(self, client):
        """测试获取问题模板接口"""
        response = client.get("/api/v1/advanced-scheduling/problem-templates")
        assert response.status_code == 200
        
        templates = response.json()
        assert isinstance(templates, list)
        assert len(templates) > 0
        
        # 验证模板结构
        for template in templates:
            assert "template_id" in template
            assert "template_name" in template
            assert "description" in template
            assert "problem_structure" in template
            assert "optimization_objectives" in template
    
    def test_validate_problem_endpoint(self, client, optimization_data_generator):
        """测试问题验证接口"""
        # 生成有效问题定义
        task_data = optimization_data_generator.generate_optimization_task()
        problem_definition = task_data["problem_definition"]
        
        response = client.post(
            "/api/v1/advanced-scheduling/validate-problem",
            json=problem_definition
        )
        
        assert response.status_code == 200
        
        validation_result = response.json()
        assert "is_valid" in validation_result
        assert "errors" in validation_result
        assert "warnings" in validation_result
        assert "suggestions" in validation_result
    
    def test_validate_invalid_problem_endpoint(self, client):
        """测试无效问题验证接口"""
        # 提供无效问题定义
        invalid_problem = {"invalid_field": "invalid_value"}
        
        response = client.post(
            "/api/v1/advanced-scheduling/validate-problem",
            json=invalid_problem
        )
        
        assert response.status_code == 200
        
        validation_result = response.json()
        assert validation_result["is_valid"] is False
        assert len(validation_result["errors"]) > 0
    
    def test_get_robust_optimization_methods_endpoint(self, client):
        """测试获取鲁棒优化方法接口"""
        response = client.get("/api/v1/robust-optimization/methods")
        assert response.status_code == 200
        
        methods = response.json()
        assert isinstance(methods, list)
        assert len(methods) > 0
        
        # 验证方法信息结构
        for method in methods:
            assert "method_type" in method
            assert "method_name" in method
            assert "description" in method
            assert "suitable_for" in method
            assert "parameters" in method
            assert "advantages" in method
            assert "disadvantages" in method
    
    def test_get_uncertainty_sets_endpoint(self, client):
        """测试获取不确定性集合类型接口"""
        response = client.get("/api/v1/robust-optimization/uncertainty-sets")
        assert response.status_code == 200
        
        uncertainty_sets = response.json()
        assert isinstance(uncertainty_sets, list)
        assert len(uncertainty_sets) > 0
        
        # 验证不确定性集合结构
        for uset in uncertainty_sets:
            assert "set_type" in uset
            assert "set_name" in uset
            assert "description" in uset
            assert "mathematical_form" in uset
            assert "parameters" in uset
            assert "advantages" in uset
            assert "disadvantages" in uset
            assert "example" in uset
    
    def test_generate_scenarios_endpoint(self, client):
        """测试生成场景接口"""
        scenario_config = {
            "num_scenarios": 10,
            "method": "monte_carlo",
            "parameters": {
                "demand": {
                    "distribution": "normal",
                    "mean": 100,
                    "std": 20
                },
                "cost": {
                    "distribution": "uniform",
                    "low": 0.8,
                    "high": 1.2
                }
            }
        }
        
        response = client.post(
            "/api/v1/robust-optimization/generate-scenarios",
            json=scenario_config
        )
        
        assert response.status_code == 200
        
        scenario_data = response.json()
        assert "scenarios" in scenario_data
        assert "num_scenarios" in scenario_data
        assert "generation_method" in scenario_data
        assert "statistics" in scenario_data
        
        assert len(scenario_data["scenarios"]) == 10
        assert scenario_data["num_scenarios"] == 10
    
    def test_get_multi_objective_algorithms_endpoint(self, client):
        """测试获取多目标优化算法接口"""
        response = client.get("/api/v1/multi-objective/algorithms")
        assert response.status_code == 200
        
        algorithms = response.json()
        assert isinstance(algorithms, list)
        assert len(algorithms) > 0
        
        # 验证算法信息
        for algorithm in algorithms:
            assert "algorithm_type" in algorithm
            assert "algorithm_name" in algorithm
            assert "description" in algorithm
            assert "suitable_for" in algorithm
            assert "features" in algorithm
            assert "parameters" in algorithm
            assert "performance" in algorithm
    
    def test_get_objective_functions_endpoint(self, client):
        """测试获取预定义目标函数接口"""
        response = client.get("/api/v1/multi-objective/objective-functions")
        assert response.status_code == 200
        
        functions = response.json()
        assert isinstance(functions, list)
        assert len(functions) > 0
        
        # 验证目标函数结构
        for func in functions:
            assert "function_id" in func
            assert "function_name" in func
            assert "description" in func
            assert "type" in func
            assert "characteristics" in func
    
    def test_analyze_pareto_front_endpoint(self, client):
        """测试帕累托前沿分析接口"""
        # 生成模拟帕累托前沿数据
        pareto_data = {
            "solutions": [
                {"objectives": [1.0, 2.0], "decision_variables": [0.5, 0.3]},
                {"objectives": [1.5, 1.5], "decision_variables": [0.7, 0.4]},
                {"objectives": [2.0, 1.0], "decision_variables": [0.9, 0.2]},
                {"objectives": [0.8, 2.5], "decision_variables": [0.3, 0.6]},
                {"objectives": [1.2, 1.8], "decision_variables": [0.6, 0.5]}
            ]
        }
        
        response = client.post(
            "/api/v1/multi-objective/analyze-pareto-front",
            json=pareto_data
        )
        
        assert response.status_code == 200
        
        analysis_result = response.json()
        assert "pareto_front_analysis" in analysis_result
        
        analysis = analysis_result["pareto_front_analysis"]
        assert "basic_statistics" in analysis
        assert "quality_indicators" in analysis
        assert "front_characteristics" in analysis
        assert "recommendations" in analysis
    
    def test_get_quality_indicators_endpoint(self, client):
        """测试获取质量指标说明接口"""
        response = client.get("/api/v1/multi-objective/quality-indicators")
        assert response.status_code == 200
        
        indicators = response.json()
        assert isinstance(indicators, list)
        assert len(indicators) > 0
        
        # 验证指标信息结构
        for indicator in indicators:
            assert "indicator_name" in indicator
            assert "indicator_description" in indicator
            assert "calculation_method" in indicator
            assert "interpretation" in indicator
            assert "advantages" in indicator
            assert "disadvantages" in indicator
            assert "suitable_for" in indicator
    
    @patch('src.api.dependencies.get_db_session')
    def test_create_path_planning_task_endpoint(self,
                                              mock_get_db,
                                              client,
                                              path_planning_data_generator,
                                              mock_db_session):
        """测试创建路径规划任务接口"""
        # 设置mock
        mock_get_db.return_value = mock_db_session
        mock_db_session.refresh.side_effect = lambda obj: setattr(obj, 'id', 'path_task_123')
        
        # 生成测试数据
        task_data = path_planning_data_generator.generate_path_planning_task()
        
        # 准备请求数据
        request_data = {
            "task_name": task_data["task_name"],
            "task_description": task_data["task_description"],
            "path_type": task_data["path_type"].value,
            "start_point": task_data["start_point"],
            "end_point": task_data["end_point"],
            "waypoints": task_data["waypoints"],
            "constraints": task_data["constraints"],
            "optimization_objectives": [obj.value for obj in task_data["optimization_objectives"]],
            "algorithm_type": task_data["algorithm_type"]
        }
        
        # 发送请求
        with patch('src.services.path_planning.PathPlanningService.create_planning_task') as mock_create:
            mock_create.return_value = "path_task_123"
            
            response = client.post(
                "/api/v1/path-planning/tasks",
                json=request_data
            )
        
        # 验证响应
        assert response.status_code == 201
        
        response_data = response.json()
        assert "task_id" in response_data
        assert response_data["task_id"] == "path_task_123"
    
    def test_get_path_planning_algorithms_endpoint(self, client):
        """测试获取路径规划算法接口"""
        response = client.get("/api/v1/path-planning/algorithms")
        assert response.status_code == 200
        
        algorithms = response.json()
        assert isinstance(algorithms, list)
        assert len(algorithms) > 0
        
        # 验证算法信息
        for algorithm in algorithms:
            assert "algorithm_type" in algorithm
            assert "algorithm_name" in algorithm
            assert "description" in algorithm
            assert "suitable_for" in algorithm
            assert "parameters" in algorithm
    
    def test_error_handling(self, client):
        """测试错误处理"""
        # 测试404错误
        response = client.get("/api/v1/nonexistent-endpoint")
        assert response.status_code == 404
        
        # 测试无效JSON数据
        response = client.post(
            "/api/v1/advanced-scheduling/tasks",
            data="invalid json"
        )
        assert response.status_code == 422
    
    def test_request_validation(self, client):
        """测试请求数据验证"""
        # 测试缺少必需字段
        invalid_request = {"incomplete": "data"}
        
        response = client.post(
            "/api/v1/advanced-scheduling/tasks",
            json=invalid_request
        )
        
        assert response.status_code == 422
        
        error_detail = response.json()
        assert "detail" in error_detail
    
    def test_cors_headers(self, client):
        """测试CORS头部"""
        response = client.options("/api/v1/advanced-scheduling/algorithms")
        
        # 验证CORS相关头部存在
        headers = response.headers
        # 注意：具体的CORS头部取决于应用的CORS配置
        # 这里只是示例，实际测试需要根据配置调整
    
    def test_rate_limiting(self, client):
        """测试速率限制（如果实现了的话）"""
        # 这是一个示例测试，实际实现取决于是否配置了速率限制
        endpoint = "/api/v1/advanced-scheduling/algorithms"
        
        # 发送多个请求
        responses = []
        for _ in range(10):
            response = client.get(endpoint)
            responses.append(response)
        
        # 验证所有请求都成功（如果没有速率限制）
        # 或者验证某些请求被限制（如果有速率限制）
        success_count = sum(1 for r in responses if r.status_code == 200)
        assert success_count > 0  # 至少有一些请求成功
