"""
算法性能测试

遵循base-rules.md规范：
- 函数复杂度控制，每个函数圈复杂度不超过10
- 函数参数不超过5个
- 使用描述性的函数命名
"""

import pytest
import time
import statistics
import numpy as np
from typing import Dict, Any, List, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed

from tests.data_generators.optimization_data import OptimizationDataGenerator
from src.algorithms.genetic.genetic_algorithm import GeneticAlgorithm
from src.algorithms.particle_swarm.pso_algorithm import ParticleSwarmOptimization
from src.algorithms.simulated_annealing.sa_algorithm import SimulatedAnnealingAlgorithm
from src.algorithms.multi_objective.nsga2 import NSGA2Algorithm
from src.algorithms.base import AlgorithmConfig


class TestAlgorithmPerformance:
    """算法性能测试类"""
    
    @pytest.fixture
    def data_generator(self):
        """数据生成器fixture"""
        return OptimizationDataGenerator(random_seed=42)
    
    @pytest.fixture
    def performance_config(self):
        """性能测试配置fixture"""
        return {
            "small_scale": {"dimension": 10, "population_size": 30, "max_iterations": 100},
            "medium_scale": {"dimension": 50, "population_size": 100, "max_iterations": 500},
            "large_scale": {"dimension": 100, "population_size": 200, "max_iterations": 1000}
        }
    
    def create_test_objective_function(self, problem_type: str = "sphere"):
        """
        创建测试目标函数
        
        Args:
            problem_type: 问题类型
            
        Returns:
            Callable: 目标函数
        """
        def sphere_function(solution: Dict[str, Any]) -> float:
            """球面函数"""
            variables = solution.get("variables", solution.get("genes", []))
            return sum(x**2 for x in variables)
        
        def rosenbrock_function(solution: Dict[str, Any]) -> float:
            """Rosenbrock函数"""
            variables = solution.get("variables", solution.get("genes", []))
            if len(variables) < 2:
                return float('inf')
            
            result = 0.0
            for i in range(len(variables) - 1):
                result += 100 * (variables[i+1] - variables[i]**2)**2 + (1 - variables[i])**2
            return result
        
        def rastrigin_function(solution: Dict[str, Any]) -> float:
            """Rastrigin函数"""
            variables = solution.get("variables", solution.get("genes", []))
            n = len(variables)
            return 10 * n + sum(x**2 - 10 * np.cos(2 * np.pi * x) for x in variables)
        
        if problem_type == "sphere":
            return sphere_function
        elif problem_type == "rosenbrock":
            return rosenbrock_function
        elif problem_type == "rastrigin":
            return rastrigin_function
        else:
            return sphere_function
    
    def measure_algorithm_performance(self, 
                                    algorithm,
                                    problem_definition: Dict[str, Any],
                                    objective_function,
                                    num_runs: int = 5) -> Dict[str, Any]:
        """
        测量算法性能
        
        Args:
            algorithm: 算法实例
            problem_definition: 问题定义
            objective_function: 目标函数
            num_runs: 运行次数
            
        Returns:
            Dict[str, Any]: 性能指标
        """
        execution_times = []
        best_fitnesses = []
        convergence_iterations = []
        memory_usages = []
        
        for run in range(num_runs):
            start_time = time.time()
            
            # 执行优化
            result = algorithm.optimize(
                problem_definition=problem_definition,
                objective_function=objective_function,
                constraints=[]
            )
            
            end_time = time.time()
            execution_time = end_time - start_time
            
            execution_times.append(execution_time)
            best_fitnesses.append(result.best_fitness)
            convergence_iterations.append(result.iterations_completed)
            
            # 简化的内存使用估算
            memory_usage = self._estimate_memory_usage(algorithm)
            memory_usages.append(memory_usage)
        
        return {
            "execution_time": {
                "mean": statistics.mean(execution_times),
                "std": statistics.stdev(execution_times) if len(execution_times) > 1 else 0,
                "min": min(execution_times),
                "max": max(execution_times)
            },
            "best_fitness": {
                "mean": statistics.mean(best_fitnesses),
                "std": statistics.stdev(best_fitnesses) if len(best_fitnesses) > 1 else 0,
                "min": min(best_fitnesses),
                "max": max(best_fitnesses)
            },
            "convergence_iterations": {
                "mean": statistics.mean(convergence_iterations),
                "std": statistics.stdev(convergence_iterations) if len(convergence_iterations) > 1 else 0,
                "min": min(convergence_iterations),
                "max": max(convergence_iterations)
            },
            "memory_usage": {
                "mean": statistics.mean(memory_usages),
                "std": statistics.stdev(memory_usages) if len(memory_usages) > 1 else 0,
                "min": min(memory_usages),
                "max": max(memory_usages)
            },
            "success_rate": sum(1 for f in best_fitnesses if f < 1e6) / len(best_fitnesses),
            "num_runs": num_runs
        }
    
    def _estimate_memory_usage(self, algorithm) -> float:
        """
        估算算法内存使用
        
        Args:
            algorithm: 算法实例
            
        Returns:
            float: 内存使用估算（MB）
        """
        # 简化的内存使用估算
        base_memory = 1.0  # 基础内存 1MB
        
        if hasattr(algorithm, 'config'):
            population_size = getattr(algorithm.config, 'population_size', 50)
            # 假设每个个体占用1KB内存
            population_memory = population_size * 0.001
            return base_memory + population_memory
        
        return base_memory
    
    @pytest.mark.performance
    def test_genetic_algorithm_performance(self, data_generator, performance_config):
        """测试遗传算法性能"""
        for scale, config in performance_config.items():
            print(f"\n测试遗传算法 - {scale}规模")
            
            # 创建算法配置
            algorithm_config = AlgorithmConfig(
                max_iterations=config["max_iterations"],
                population_size=config["population_size"],
                convergence_tolerance=1e-6,
                max_time_seconds=300,
                algorithm_params={
                    "crossover_rate": 0.8,
                    "mutation_rate": 0.1,
                    "selection_method": "tournament"
                }
            )
            
            # 创建算法实例
            algorithm = GeneticAlgorithm(algorithm_config)
            
            # 创建问题定义
            problem_definition = {
                "dimension": config["dimension"],
                "bounds": [(-5.12, 5.12)] * config["dimension"]
            }
            
            # 创建目标函数
            objective_function = self.create_test_objective_function("sphere")
            
            # 测量性能
            performance = self.measure_algorithm_performance(
                algorithm, problem_definition, objective_function, num_runs=3
            )
            
            # 验证性能指标
            assert performance["execution_time"]["mean"] > 0
            assert performance["best_fitness"]["mean"] >= 0
            assert performance["success_rate"] >= 0
            
            print(f"平均执行时间: {performance['execution_time']['mean']:.2f}秒")
            print(f"平均最佳适应度: {performance['best_fitness']['mean']:.6f}")
            print(f"成功率: {performance['success_rate']:.2%}")
    
    @pytest.mark.performance
    def test_particle_swarm_performance(self, data_generator, performance_config):
        """测试粒子群算法性能"""
        for scale, config in performance_config.items():
            print(f"\n测试粒子群算法 - {scale}规模")
            
            # 创建算法配置
            algorithm_config = AlgorithmConfig(
                max_iterations=config["max_iterations"],
                population_size=config["population_size"],
                convergence_tolerance=1e-6,
                max_time_seconds=300,
                algorithm_params={
                    "inertia_weight": 0.9,
                    "cognitive_coefficient": 2.0,
                    "social_coefficient": 2.0
                }
            )
            
            # 创建算法实例
            algorithm = ParticleSwarmOptimization(algorithm_config)
            
            # 创建问题定义
            problem_definition = {
                "dimension": config["dimension"],
                "bounds": [(-5.12, 5.12)] * config["dimension"]
            }
            
            # 创建目标函数
            objective_function = self.create_test_objective_function("sphere")
            
            # 测量性能
            performance = self.measure_algorithm_performance(
                algorithm, problem_definition, objective_function, num_runs=3
            )
            
            # 验证性能指标
            assert performance["execution_time"]["mean"] > 0
            assert performance["best_fitness"]["mean"] >= 0
            
            print(f"平均执行时间: {performance['execution_time']['mean']:.2f}秒")
            print(f"平均最佳适应度: {performance['best_fitness']['mean']:.6f}")
    
    @pytest.mark.performance
    def test_simulated_annealing_performance(self, data_generator, performance_config):
        """测试模拟退火算法性能"""
        # 只测试小规模和中等规模，因为模拟退火是单点搜索
        test_scales = ["small_scale", "medium_scale"]
        
        for scale in test_scales:
            config = performance_config[scale]
            print(f"\n测试模拟退火算法 - {scale}规模")
            
            # 创建算法配置
            algorithm_config = AlgorithmConfig(
                max_iterations=config["max_iterations"],
                population_size=1,  # 模拟退火是单点搜索
                convergence_tolerance=1e-6,
                max_time_seconds=300,
                algorithm_params={
                    "initial_temperature": 1000.0,
                    "final_temperature": 0.01,
                    "cooling_rate": 0.95
                }
            )
            
            # 创建算法实例
            algorithm = SimulatedAnnealingAlgorithm(algorithm_config)
            
            # 创建问题定义
            problem_definition = {
                "dimension": config["dimension"],
                "bounds": [(-5.12, 5.12)] * config["dimension"]
            }
            
            # 创建目标函数
            objective_function = self.create_test_objective_function("sphere")
            
            # 测量性能
            performance = self.measure_algorithm_performance(
                algorithm, problem_definition, objective_function, num_runs=3
            )
            
            # 验证性能指标
            assert performance["execution_time"]["mean"] > 0
            assert performance["best_fitness"]["mean"] >= 0
            
            print(f"平均执行时间: {performance['execution_time']['mean']:.2f}秒")
            print(f"平均最佳适应度: {performance['best_fitness']['mean']:.6f}")
    
    @pytest.mark.performance
    def test_nsga2_performance(self, data_generator, performance_config):
        """测试NSGA-II算法性能"""
        for scale, config in performance_config.items():
            print(f"\n测试NSGA-II算法 - {scale}规模")
            
            # 创建算法配置
            algorithm_config = AlgorithmConfig(
                max_iterations=config["max_iterations"],
                population_size=config["population_size"],
                convergence_tolerance=1e-6,
                max_time_seconds=300,
                algorithm_params={
                    "crossover_rate": 0.9,
                    "mutation_rate": 0.1,
                    "num_objectives": 2
                }
            )
            
            # 创建算法实例
            algorithm = NSGA2Algorithm(algorithm_config)
            
            # 创建多目标问题定义
            problem_definition = {
                "gene_length": config["dimension"],
                "gene_bounds": [(-5.12, 5.12)] * config["dimension"],
                "num_objectives": 2,
                "objective_functions": [
                    self.create_test_objective_function("sphere"),
                    self.create_test_objective_function("rosenbrock")
                ]
            }
            
            # 测量性能（多目标优化不使用单一目标函数）
            start_time = time.time()
            
            algorithm.initialize_population(problem_definition)
            
            # 运行几代
            for _ in range(min(10, config["max_iterations"])):
                solutions = algorithm.generate_new_solutions()
                if not solutions:
                    break
            
            end_time = time.time()
            execution_time = end_time - start_time
            
            # 获取帕累托前沿
            best_solution = algorithm._get_best_solution()
            pareto_front_size = best_solution.get("pareto_front_size", 0)
            
            print(f"执行时间: {execution_time:.2f}秒")
            print(f"帕累托前沿大小: {pareto_front_size}")
            
            # 验证结果
            assert execution_time > 0
            assert pareto_front_size >= 0
    
    @pytest.mark.performance
    def test_algorithm_comparison(self, data_generator):
        """测试算法性能比较"""
        print("\n算法性能比较测试")
        
        # 统一的测试配置
        test_config = {
            "dimension": 20,
            "population_size": 50,
            "max_iterations": 200
        }
        
        problem_definition = {
            "dimension": test_config["dimension"],
            "bounds": [(-5.12, 5.12)] * test_config["dimension"]
        }
        
        objective_function = self.create_test_objective_function("sphere")
        
        algorithms = {
            "遗传算法": GeneticAlgorithm(AlgorithmConfig(
                max_iterations=test_config["max_iterations"],
                population_size=test_config["population_size"],
                algorithm_params={"crossover_rate": 0.8, "mutation_rate": 0.1}
            )),
            "粒子群算法": ParticleSwarmOptimization(AlgorithmConfig(
                max_iterations=test_config["max_iterations"],
                population_size=test_config["population_size"],
                algorithm_params={"inertia_weight": 0.9, "cognitive_coefficient": 2.0, "social_coefficient": 2.0}
            )),
            "模拟退火": SimulatedAnnealingAlgorithm(AlgorithmConfig(
                max_iterations=test_config["max_iterations"],
                population_size=1,
                algorithm_params={"initial_temperature": 1000.0, "cooling_rate": 0.95}
            ))
        }
        
        results = {}
        
        for name, algorithm in algorithms.items():
            print(f"\n测试 {name}")
            performance = self.measure_algorithm_performance(
                algorithm, problem_definition, objective_function, num_runs=3
            )
            results[name] = performance
            
            print(f"平均执行时间: {performance['execution_time']['mean']:.2f}秒")
            print(f"平均最佳适应度: {performance['best_fitness']['mean']:.6f}")
            print(f"成功率: {performance['success_rate']:.2%}")
        
        # 比较结果
        print("\n性能比较总结:")
        print("算法\t\t执行时间(秒)\t最佳适应度\t成功率")
        print("-" * 60)
        
        for name, perf in results.items():
            print(f"{name}\t\t{perf['execution_time']['mean']:.2f}\t\t{perf['best_fitness']['mean']:.6f}\t{perf['success_rate']:.2%}")
    
    @pytest.mark.performance
    def test_scalability_analysis(self, data_generator):
        """测试算法可扩展性分析"""
        print("\n算法可扩展性分析")
        
        dimensions = [10, 20, 50, 100]
        algorithm_config = AlgorithmConfig(
            max_iterations=100,
            population_size=50,
            algorithm_params={"crossover_rate": 0.8, "mutation_rate": 0.1}
        )
        
        objective_function = self.create_test_objective_function("sphere")
        
        scalability_results = {}
        
        for dim in dimensions:
            print(f"\n测试维度: {dim}")
            
            problem_definition = {
                "dimension": dim,
                "bounds": [(-5.12, 5.12)] * dim
            }
            
            algorithm = GeneticAlgorithm(algorithm_config)
            
            start_time = time.time()
            result = algorithm.optimize(
                problem_definition=problem_definition,
                objective_function=objective_function,
                constraints=[]
            )
            end_time = time.time()
            
            execution_time = end_time - start_time
            scalability_results[dim] = {
                "execution_time": execution_time,
                "best_fitness": result.best_fitness,
                "iterations": result.iterations_completed
            }
            
            print(f"执行时间: {execution_time:.2f}秒")
            print(f"最佳适应度: {result.best_fitness:.6f}")
        
        # 分析可扩展性
        print("\n可扩展性分析结果:")
        print("维度\t执行时间(秒)\t最佳适应度\t迭代次数")
        print("-" * 50)
        
        for dim, result in scalability_results.items():
            print(f"{dim}\t{result['execution_time']:.2f}\t\t{result['best_fitness']:.6f}\t{result['iterations']}")
        
        # 验证可扩展性趋势
        execution_times = [result["execution_time"] for result in scalability_results.values()]
        
        # 执行时间应该随维度增加而增加
        for i in range(1, len(execution_times)):
            # 允许一定的波动，但总体趋势应该是增加的
            assert execution_times[i] >= execution_times[i-1] * 0.5  # 允许50%的波动
    
    @pytest.mark.performance
    def test_parallel_execution_performance(self, data_generator):
        """测试并行执行性能"""
        print("\n并行执行性能测试")
        
        # 创建多个优化任务
        num_tasks = 4
        tasks = []
        
        for i in range(num_tasks):
            algorithm_config = AlgorithmConfig(
                max_iterations=100,
                population_size=30,
                algorithm_params={"crossover_rate": 0.8, "mutation_rate": 0.1}
            )
            
            algorithm = GeneticAlgorithm(algorithm_config)
            problem_definition = {
                "dimension": 10,
                "bounds": [(-5.12, 5.12)] * 10
            }
            objective_function = self.create_test_objective_function("sphere")
            
            tasks.append((algorithm, problem_definition, objective_function))
        
        # 串行执行
        print("串行执行...")
        start_time = time.time()
        
        serial_results = []
        for algorithm, problem_def, obj_func in tasks:
            result = algorithm.optimize(problem_def, obj_func, [])
            serial_results.append(result)
        
        serial_time = time.time() - start_time
        
        # 并行执行
        print("并行执行...")
        start_time = time.time()
        
        parallel_results = []
        with ThreadPoolExecutor(max_workers=4) as executor:
            futures = []
            for algorithm, problem_def, obj_func in tasks:
                future = executor.submit(algorithm.optimize, problem_def, obj_func, [])
                futures.append(future)
            
            for future in as_completed(futures):
                result = future.result()
                parallel_results.append(result)
        
        parallel_time = time.time() - start_time
        
        print(f"串行执行时间: {serial_time:.2f}秒")
        print(f"并行执行时间: {parallel_time:.2f}秒")
        print(f"加速比: {serial_time / parallel_time:.2f}x")
        
        # 验证并行执行的效果
        assert len(parallel_results) == num_tasks
        assert parallel_time < serial_time  # 并行应该更快（在多核系统上）
